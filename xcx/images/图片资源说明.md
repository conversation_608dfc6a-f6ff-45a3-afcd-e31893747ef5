# 图片资源说明

## 🚨 当前缺失的图片文件

以下图片文件需要添加到 `xcx/images/` 目录：

### 1. 轮播图占位图
- **文件名**: `banner-placeholder.jpg`
- **建议尺寸**: 750px × 300px
- **用途**: 首页轮播图占位
- **格式**: JPG

### 2. 产品占位图
- **文件名**: `product-placeholder.jpg`
- **建议尺寸**: 300px × 200px
- **用途**: 产品列表占位图
- **格式**: JPG

### 3. 空状态图片
- **文件名**: `empty-products.png`
- **建议尺寸**: 200px × 200px
- **用途**: 产品列表为空时显示
- **格式**: PNG（透明背景）

### 4. 公司占位图
- **文件名**: `company-placeholder.png`
- **建议尺寸**: 120px × 120px
- **用途**: 保险公司 Logo 占位
- **格式**: PNG

### 5. 默认头像
- **文件名**: `default-avatar.png`
- **建议尺寸**: 100px × 100px
- **用途**: 用户默认头像
- **格式**: PNG（圆形或方形）

## 🔧 临时解决方案

在没有实际图片文件的情况下，可以使用以下方法：

### 方案1：使用在线占位图服务
```javascript
// 在相关页面的 data 中设置
data: {
  bannerPlaceholder: 'https://via.placeholder.com/750x300/1976D2/FFFFFF?text=Banner',
  productPlaceholder: 'https://via.placeholder.com/300x200/42A5F5/FFFFFF?text=Product',
  emptyImage: 'https://via.placeholder.com/200x200/E0E0E0/757575?text=Empty',
  companyPlaceholder: 'https://via.placeholder.com/120x120/F5F5F5/1976D2?text=Logo',
  defaultAvatar: 'https://via.placeholder.com/100x100/E0E0E0/757575?text=User'
}
```

### 方案2：使用 CSS 背景色替代
```css
.image-placeholder {
  background-color: #E0E0E0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #757575;
  font-size: 24rpx;
}
```

### 方案3：使用 Unicode 图标
```html
<!-- 替代图片的 Unicode 图标 -->
<view class="icon-placeholder">
  <text>🏢</text> <!-- 公司图标 -->
  <text>📋</text> <!-- 产品图标 -->
  <text>👤</text> <!-- 用户图标 -->
  <text>🖼️</text> <!-- 图片图标 -->
</view>
```

## 📝 推荐的图片内容

### 轮播图建议内容：
- 香港保险服务介绍
- 产品推广图片
- 公司品牌宣传

### 产品占位图建议：
- 保险产品类型图标
- 简洁的产品示意图
- 品牌色彩的抽象图形

### 空状态图片建议：
- 简洁的插画风格
- 表示"暂无内容"的图标
- 友好的提示图形

### 公司 Logo 建议：
- 知名保险公司的官方 Logo
- 统一的占位图标
- 品牌标识

### 默认头像建议：
- 简洁的用户图标
- 圆形头像样式
- 中性的颜色搭配

## 🎨 设计规范

### 颜色规范：
- **主色**: #1976D2 (蓝色)
- **辅助色**: #42A5F5 (浅蓝)
- **中性色**: #E0E0E0 (浅灰)
- **文字色**: #757575 (深灰)

### 尺寸规范：
- 保持 2:1 或 3:2 的宽高比
- 使用偶数像素尺寸
- 考虑高分辨率屏幕适配

### 文件格式：
- **照片类**: 使用 JPG 格式
- **图标类**: 使用 PNG 格式
- **简单图形**: 考虑使用 SVG 格式
