# 图标文件说明

## 需要的图标文件

为了完善底部导航栏，您需要在此目录下添加以下图标文件：

### 首页图标
- `home.png` - 首页未选中状态图标
- `home-active.png` - 首页选中状态图标

### 产品图标
- `products.png` - 产品未选中状态图标
- `products-active.png` - 产品选中状态图标

### 理赔图标
- `claims.png` - 理赔未选中状态图标
- `claims-active.png` - 理赔选中状态图标

### 个人中心图标
- `profile.png` - 个人中心未选中状态图标
- `profile-active.png` - 个人中心选中状态图标

## 图标规格要求

- 尺寸：81px * 81px
- 格式：PNG
- 背景：透明
- 颜色：未选中状态建议使用 #7A7E83，选中状态建议使用 #1976D2

## 添加图标后的配置

添加图标文件后，需要在 `app.json` 的 `tabBar.list` 中为每个页面添加 `iconPath` 和 `selectedIconPath` 属性。

示例：
```json
{
  "pagePath": "pages/home/<USER>",
  "iconPath": "images/home.png",
  "selectedIconPath": "images/home-active.png",
  "text": "首页"
}
```
