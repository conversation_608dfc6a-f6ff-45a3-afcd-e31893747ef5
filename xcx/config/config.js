// config/config.js
// 小程序配置文件

const config = {
  // API配置
  api: {
    // 生产环境API地址
    baseUrl: 'https://baoxian.weixinjishu.top',
    
    // 开发环境API地址（可选）
    devBaseUrl: 'http://127.0.0.1:8000',
    
    // 超时时间
    timeout: 10000
  },
  
  // 微信小程序配置
  wechat: {
    appId: '', // 微信小程序AppID
    appSecret: '' // 微信小程序AppSecret（后端使用）
  },
  
  // 分页配置
  pagination: {
    pageSize: 20,
    maxPageSize: 100
  },
  
  // 缓存配置
  cache: {
    // 缓存过期时间（毫秒）
    expireTime: 30 * 60 * 1000, // 30分钟
    
    // 缓存键前缀
    keyPrefix: 'hk_insurance_'
  },
  
  // 上传配置
  upload: {
    // 最大文件大小（字节）
    maxFileSize: 10 * 1024 * 1024, // 10MB
    
    // 允许的文件类型
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf']
  },
  
  // 联系方式
  contact: {
    wechat: 'HKInsurance2024',
    phone: '+852 1234 5678',
    email: '<EMAIL>',
    address: '香港中环皇后大道中99号中环中心15楼'
  },
  
  // 版本信息
  version: '1.0.0',
  
  // 环境判断
  isDev: false, // 设置为true使用开发环境API
  
  // 获取当前API地址
  getApiBaseUrl() {
    return this.isDev ? this.api.devBaseUrl : this.api.baseUrl
  }
}

module.exports = config
