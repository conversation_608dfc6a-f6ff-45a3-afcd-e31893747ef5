# 香港保险微信小程序前端

## 📱 项目概述

香港保险微信小程序前端，提供保险产品浏览、咨询、投保等功能。

## 🔧 配置说明

### API域名配置

项目已配置使用生产域名：`https://baoxian.weixinjishu.top`

API配置文件位置：`config/config.js`

```javascript
const config = {
  api: {
    baseUrl: 'https://baoxian.weixinjishu.top',  // 生产环境
    devBaseUrl: 'http://127.0.0.1:8000',        // 开发环境
  },
  isDev: false  // 设置为true使用开发环境
}
```

### 微信小程序配置

1. **AppID配置**
   - 在微信公众平台获取AppID
   - 更新 `config/config.js` 中的 `wechat.appId`

2. **服务器域名配置**
   - 在微信公众平台 -> 开发 -> 开发设置 -> 服务器域名
   - 添加以下域名：
     - request合法域名：`https://baoxian.weixinjishu.top`
     - uploadFile合法域名：`https://baoxian.weixinjishu.top`
     - downloadFile合法域名：`https://baoxian.weixinjishu.top`

## 📁 项目结构

```
xcx/
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── config/
│   └── config.js         # 配置文件
├── pages/                # 页面目录
│   ├── home/            # 首页
│   ├── products/        # 产品列表
│   ├── product-detail/  # 产品详情
│   ├── purchase/        # 投保流程
│   ├── profile/         # 个人中心
│   ├── claims/          # 理赔服务
│   └── services/        # 服务页面
├── utils/               # 工具函数
├── images/              # 图片资源
└── README.md           # 说明文档
```

## 🚀 开发指南

### 1. 开发环境设置

1. 安装微信开发者工具
2. 导入项目目录
3. 配置AppID
4. 设置后端API地址

### 2. 环境切换

开发环境：
```javascript
// config/config.js
isDev: true
```

生产环境：
```javascript
// config/config.js
isDev: false
```

### 3. API调用

使用全局request方法：
```javascript
const app = getApp()

// GET请求
const data = await app.request({
  url: '/insurance/api/products/'
})

// POST请求
const result = await app.request({
  url: '/insurance/api/products/1/inquire/',
  method: 'POST',
  data: { name: '张三', phone: '13800138000' }
})
```

## 📋 功能模块

### 1. 首页 (home)
- 轮播图展示
- 产品分类导航
- 热门产品推荐
- 保险优势介绍

### 2. 产品列表 (products)
- 产品筛选和排序
- 分页加载
- 产品卡片展示
- 搜索功能

### 3. 产品详情 (product-detail)
- 产品详细信息
- 保费计算器
- 用户评价
- 收藏功能

### 4. 投保流程 (purchase)
- 三步式投保流程
- 表单验证
- 预约面谈
- 进度跟踪

### 5. 个人中心 (profile)
- 用户信息管理
- 数据统计展示
- 功能菜单
- 登录状态管理

### 6. 理赔服务 (claims)
- 理赔指南
- 快速理赔入口
- 理赔记录查询
- 客服联系

### 7. 服务页面 (services)
- 在线咨询
- 预约服务
- 保费计算器
- 帮助中心

## 🎨 UI/UX特色

- **现代化设计**：渐变色彩、圆角卡片、阴影效果
- **响应式布局**：适配不同屏幕尺寸
- **流畅交互**：加载状态、错误处理、动画效果
- **用户体验**：下拉刷新、分页加载、状态管理

## 🔐 安全考虑

1. **数据验证**：前端表单验证
2. **错误处理**：统一错误处理机制
3. **状态管理**：登录状态检查
4. **数据缓存**：敏感数据不缓存

## 📱 兼容性

- 微信版本：7.0.0+
- 基础库版本：2.10.0+
- 支持iOS和Android

## 🐛 调试指南

### 1. 网络请求调试
```javascript
// 在app.js中启用调试模式
console.log('API Request:', options)
console.log('API Response:', response)
```

### 2. 常见问题

**网络请求失败**
- 检查域名配置
- 验证SSL证书
- 确认服务器状态

**登录失败**
- 检查AppID配置
- 验证微信登录流程
- 确认后端接口

**页面显示异常**
- 检查数据格式
- 验证API返回
- 确认页面逻辑

## 📦 发布流程

1. **代码检查**
   - 确认API域名为生产环境
   - 检查所有功能正常
   - 验证页面跳转

2. **提交审核**
   - 上传代码到微信公众平台
   - 填写版本信息
   - 提交审核

3. **发布上线**
   - 审核通过后发布
   - 监控线上状态
   - 收集用户反馈

## 📞 技术支持

如有技术问题，请联系：
- 邮箱：<EMAIL>
- 微信：HKInsurance2024

## 📄 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 完整功能实现
- 生产环境配置
