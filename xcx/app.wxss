/**app.wxss**/
/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 底部导航页面容器修复 */
.tab-page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  box-sizing: border-box;
}

/* 修复底部导航显示问题 */
.page-with-tabbar {
  padding-bottom: calc(env(safe-area-inset-bottom) + 100rpx);
}

/* 底部安全间距 */
.bottom-safe-area {
  height: calc(env(safe-area-inset-bottom) + 100rpx);
  background: transparent;
}

/* 通用样式 */
.card {
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 12rpx -2rpx rgba(0, 0, 0, 0.1);
  margin: 24rpx;
  overflow: hidden;
}

.card-padding {
  padding: 32rpx;
}

.gradient-bg {
  background: linear-gradient(135deg, #7C3AED 0%, #A855F7 100%);
}

.text-primary {
  color: #1E40AF;
}

.text-secondary {
  color: #6B7280;
}

.text-success {
  color: #10B981;
}

.text-warning {
  color: #F59E0B;
}

.text-danger {
  color: #EF4444;
}

.btn {
  padding: 24rpx 48rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  text-align: center;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: #1E40AF;
  color: white;
}

.btn-secondary {
  background-color: #F3F4F6;
  color: #374151;
}

.btn-success {
  background-color: #10B981;
  color: white;
}

.btn-outline {
  background-color: transparent;
  border: 2rpx solid #1E40AF;
  color: #1E40AF;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.font-bold {
  font-weight: bold;
}

.font-semibold {
  font-weight: 600;
}

.text-xs {
  font-size: 20rpx;
}

.text-sm {
  font-size: 24rpx;
}

.text-base {
  font-size: 28rpx;
}

.text-lg {
  font-size: 32rpx;
}

.text-xl {
  font-size: 36rpx;
}

.text-2xl {
  font-size: 48rpx;
}

.mb-1 {
  margin-bottom: 8rpx;
}

.mb-2 {
  margin-bottom: 16rpx;
}

.mb-3 {
  margin-bottom: 24rpx;
}

.mb-4 {
  margin-bottom: 32rpx;
}

.mt-1 {
  margin-top: 8rpx;
}

.mt-2 {
  margin-top: 16rpx;
}

.mt-3 {
  margin-top: 24rpx;
}

.mt-4 {
  margin-top: 32rpx;
}

.p-1 {
  padding: 8rpx;
}

.p-2 {
  padding: 16rpx;
}

.p-3 {
  padding: 24rpx;
}

.p-4 {
  padding: 32rpx;
}

.px-2 {
  padding-left: 16rpx;
  padding-right: 16rpx;
}

.px-3 {
  padding-left: 24rpx;
  padding-right: 24rpx;
}

.px-4 {
  padding-left: 32rpx;
  padding-right: 32rpx;
}

.py-2 {
  padding-top: 16rpx;
  padding-bottom: 16rpx;
}

.py-3 {
  padding-top: 24rpx;
  padding-bottom: 24rpx;
}

.py-4 {
  padding-top: 32rpx;
  padding-bottom: 32rpx;
}

.rounded {
  border-radius: 8rpx;
}

.rounded-lg {
  border-radius: 16rpx;
}

.rounded-xl {
  border-radius: 24rpx;
}

.rounded-full {
  border-radius: 50%;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}
