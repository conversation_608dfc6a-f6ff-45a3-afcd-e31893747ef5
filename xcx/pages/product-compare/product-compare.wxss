/* pages/product-compare/product-compare.wxss */

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 48rpx 32rpx;
  color: white;
  text-align: center;
}

.header-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.header-subtitle {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

/* 比较内容 */
.compare-content {
  padding-bottom: 120rpx;
}

/* 产品卡片滚动区域 */
.products-scroll {
  background: white;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.products-container {
  display: flex;
  padding: 0 32rpx;
  gap: 24rpx;
}

.product-column {
  flex-shrink: 0;
  width: 240rpx;
}

.product-card {
  background: white;
  border-radius: 16rpx;
  border: 2rpx solid #f0f0f0;
  overflow: hidden;
}

.product-header {
  position: relative;
  height: 160rpx;
  background: #f8f9fa;
}

.product-image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #f8f9fa;
}

.placeholder-icon {
  font-size: 48rpx;
  color: #ccc;
}

.product-image {
  width: 100%;
  height: 100%;
}

.remove-btn {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  padding: 0;
}

.product-info {
  padding: 24rpx 16rpx;
}

.product-name {
  display: block;
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.3;
}

.product-category {
  display: block;
  font-size: 20rpx;
  color: #666;
}

/* 添加产品卡片 */
.add-product-column {
  flex-shrink: 0;
  width: 240rpx;
}

.add-product-card {
  height: 280rpx;
  border: 2rpx dashed #ccc;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.add-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.add-text {
  font-size: 24rpx;
}

/* 比较表格 */
.compare-table {
  background: white;
}

.table-section {
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  display: block;
  padding: 32rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  background: #f8f9fa;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}

.row-label {
  width: 200rpx;
  padding: 32rpx 24rpx;
  font-size: 24rpx;
  color: #666;
  background: #fafafa;
  border-right: 1rpx solid #f0f0f0;
}

.row-values {
  flex: 1;
  display: flex;
}

.value-cell {
  flex: 1;
  padding: 32rpx 24rpx;
  border-right: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.value-cell:last-child {
  border-right: none;
}

.value-text {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

/* 特色比较 */
.features-comparison {
  display: flex;
  padding: 32rpx 0;
}

.feature-column {
  flex: 1;
  padding: 0 24rpx;
  border-right: 1rpx solid #f0f0f0;
}

.feature-column:last-child {
  border-right: none;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
}

.feature-icon {
  font-size: 20rpx;
  color: #10b981;
  margin-top: 4rpx;
}

.feature-text {
  flex: 1;
  font-size: 22rpx;
  color: #333;
  line-height: 1.4;
}

/* 操作区域 */
.action-section {
  padding: 32rpx;
}

.action-buttons {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  background: #1e40af;
  color: white;
  border-radius: 16rpx;
  font-size: 24rpx;
  border: none;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.3;
}

.empty-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 48rpx;
  line-height: 1.5;
}

.goto-products-btn {
  padding: 24rpx 48rpx;
  background: #1e40af;
  color: white;
  border-radius: 50rpx;
  font-size: 24rpx;
  border: none;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 24rpx;
  z-index: 100;
}

.clear-all-btn {
  flex: 1;
  padding: 24rpx;
  background: #f3f4f6;
  color: #666;
  border-radius: 16rpx;
  font-size: 24rpx;
  border: none;
}

.share-btn {
  flex: 1;
  padding: 24rpx;
  background: #10b981;
  color: white;
  border-radius: 16rpx;
  font-size: 24rpx;
  border: none;
}
