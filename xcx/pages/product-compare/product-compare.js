// pages/product-compare/product-compare.js
const app = getApp()

Page({
  data: {
    compareList: []
  },

  onLoad() {
    this.loadCompareList()
  },

  onShow() {
    // 每次显示页面时重新加载比较列表
    this.loadCompareList()
  },

  // 加载比较列表
  loadCompareList() {
    const compareList = wx.getStorageSync('compare_products') || []
    this.setData({ compareList })
    
    // 更新导航栏标题
    wx.setNavigationBarTitle({
      title: `产品比较 (${compareList.length}/3)`
    })
  },

  // 移除产品
  onRemoveProduct(e) {
    const productId = e.currentTarget.dataset.id
    
    wx.showModal({
      title: '确认移除',
      content: '确定要从比较列表中移除这个产品吗？',
      success: (res) => {
        if (res.confirm) {
          this.removeFromCompare(productId)
          this.loadCompareList()
          wx.showToast({ title: '已移除', icon: 'success' })
        }
      }
    })
  },

  // 添加产品
  onAddProduct() {
    wx.navigateTo({
      url: '/pages/products/products?compare=true'
    })
  },

  // 查看产品详情
  onViewDetail(e) {
    const productId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${productId}`
    })
  },

  // 去产品页面
  onGotoProducts() {
    wx.navigateTo({
      url: '/pages/products/products'
    })
  },

  // 清空比较列表
  onClearAll() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有比较产品吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('compare_products')
          this.setData({ compareList: [] })
          wx.showToast({ title: '已清空', icon: 'success' })
        }
      }
    })
  },

  // 分享比较
  onShare() {
    const { compareList } = this.data
    const productNames = compareList.map(item => item.name).join(' vs ')
    
    wx.showActionSheet({
      itemList: ['分享给朋友', '生成比较图片'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 分享给朋友
            this.shareToFriend()
            break
          case 1:
            // 生成比较图片
            this.generateCompareImage()
            break
        }
      }
    })
  },

  // 分享给朋友
  shareToFriend() {
    // 触发系统分享
  },

  // 生成比较图片
  generateCompareImage() {
    wx.showToast({
      title: '功能开发中...',
      icon: 'none'
    })
  },

  // 本地存储操作
  removeFromCompare(productId) {
    let compareList = wx.getStorageSync('compare_products') || []
    compareList = compareList.filter(item => item.id !== productId)
    wx.setStorageSync('compare_products', compareList)
  },

  // 分享功能
  onShareAppMessage() {
    const { compareList } = this.data
    const productNames = compareList.map(item => item.name).join(' vs ')
    
    return {
      title: `产品比较：${productNames}`,
      path: '/pages/product-compare/product-compare',
      imageUrl: '/images/share-compare.jpg'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    const { compareList } = this.data
    const productNames = compareList.map(item => item.name).join(' vs ')
    
    return {
      title: `香港保险产品比较：${productNames}`,
      imageUrl: '/images/share-compare.jpg'
    }
  }
})
