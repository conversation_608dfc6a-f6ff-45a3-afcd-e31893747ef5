<!--pages/product-compare/product-compare.wxml-->
<view class="container">
  <!-- 头部 -->
  <view class="header">
    <text class="header-title">产品比较</text>
    <text class="header-subtitle">最多可比较3个产品</text>
  </view>

  <!-- 比较列表 -->
  <view class="compare-content" wx:if="{{compareList.length > 0}}">
    <!-- 产品卡片 -->
    <scroll-view class="products-scroll" scroll-x="true">
      <view class="products-container">
        <view class="product-column" wx:for="{{compareList}}" wx:key="id">
          <view class="product-card">
            <view class="product-header">
              <view class="product-image-placeholder" wx:if="{{!item.main_image}}">
                <text class="placeholder-icon">📋</text>
              </view>
              <image wx:else src="{{item.main_image}}" class="product-image" mode="aspectFill"></image>
              <button class="remove-btn" bindtap="onRemoveProduct" data-id="{{item.id}}">✕</button>
            </view>
            <view class="product-info">
              <text class="product-name">{{item.name}}</text>
              <text class="product-category">{{item.category.name}}</text>
            </view>
          </view>
        </view>
        
        <!-- 添加产品按钮 -->
        <view class="add-product-column" wx:if="{{compareList.length < 3}}">
          <view class="add-product-card" bindtap="onAddProduct">
            <view class="add-icon">➕</view>
            <text class="add-text">添加产品</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 比较表格 -->
    <view class="compare-table">
      <!-- 基本信息 -->
      <view class="table-section">
        <text class="section-title">基本信息</text>
        
        <view class="table-row">
          <view class="row-label">最低保费</view>
          <view class="row-values">
            <view class="value-cell" wx:for="{{compareList}}" wx:key="id">
              <text class="value-text">{{item.min_premium}} {{item.currency}}</text>
            </view>
          </view>
        </view>
        
        <view class="table-row">
          <view class="row-label">预期收益</view>
          <view class="row-values">
            <view class="value-cell" wx:for="{{compareList}}" wx:key="id">
              <text class="value-text" wx:if="{{item.expected_return}}">{{item.expected_return}}%</text>
              <text class="value-text" wx:else>-</text>
            </view>
          </view>
        </view>
        
        <view class="table-row">
          <view class="row-label">产品类型</view>
          <view class="row-values">
            <view class="value-cell" wx:for="{{compareList}}" wx:key="id">
              <text class="value-text">{{item.category.name}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 产品特色 -->
      <view class="table-section">
        <text class="section-title">产品特色</text>
        
        <view class="features-comparison">
          <view class="feature-column" wx:for="{{compareList}}" wx:key="id" wx:for-item="product">
            <view class="feature-list">
              <view class="feature-item" wx:for="{{product.features}}" wx:key="*this" wx:for-item="feature">
                <text class="feature-icon">✓</text>
                <text class="feature-text">{{feature}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-section">
        <view class="action-buttons">
          <button class="action-btn" wx:for="{{compareList}}" wx:key="id" 
                  bindtap="onViewDetail" data-id="{{item.id}}">
            查看详情
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <view class="empty-icon">📊</view>
    <text class="empty-title">暂无比较产品</text>
    <text class="empty-subtitle">去产品页面添加想要比较的产品吧</text>
    <button class="goto-products-btn" bindtap="onGotoProducts">浏览产品</button>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar" wx:if="{{compareList.length > 0}}">
    <button class="clear-all-btn" bindtap="onClearAll">清空比较</button>
    <button class="share-btn" bindtap="onShare">分享比较</button>
  </view>
</view>
