// pages/purchase/purchase.js
const app = getApp()

Page({
  data: {
    currentStep: 1,
    product: {},
    selectedPremium: '',
    selectedPeriod: '',
    
    // 表单数据
    formData: {
      name: '',
      phone: '',
      age: '',
      income: '',
      message: '',
      idNumber: '',
      gender: '',
      birthDate: ''
    },

    // 选择器数据
    ageRanges: ['请选择年龄', '18-25岁', '26-35岁', '36-45岁', '46-55岁', '56-65岁', '65岁以上'],
    ageIndex: 0,
    incomeRanges: ['请选择年收入', '10万以下', '10-30万', '30-50万', '50-100万', '100万以上'],
    incomeIndex: 0,

    // 证件类型
    idTypes: ['请选择证件类型', '身份证', '护照', '港澳通行证', '台胞证'],
    idTypeIndex: 0,

    // 职业选择
    occupations: ['请选择职业', '公务员', '企业管理人员', '专业技术人员', '销售人员', '服务人员', '学生', '退休人员', '其他'],
    occupationIndex: 0,

    // 健康告知
    healthQuestions: [
      {
        id: 1,
        question: '您是否患有高血压、糖尿病、心脏病等慢性疾病？',
        answer: ''
      },
      {
        id: 2,
        question: '您是否曾经住院治疗或手术？',
        answer: ''
      },
      {
        id: 3,
        question: '您是否正在服用任何处方药物？',
        answer: ''
      }
    ],

    // 受益人信息
    beneficiaryType: 'legal',
    beneficiaryInfo: {
      name: '',
      relationship: ''
    },
    relationships: ['请选择关系', '配偶', '子女', '父母', '兄弟姐妹', '其他'],
    relationshipIndex: 0,
    
    // 预约相关
    selectedMethod: 'online',
    selectedDate: '',
    selectedTime: '',
    today: '',
    timeSlots: [
      { time: '09:00', available: true, selected: false },
      { time: '10:00', available: true, selected: false },
      { time: '11:00', available: false, selected: false },
      { time: '14:00', available: true, selected: false },
      { time: '15:00', available: true, selected: false },
      { time: '16:00', available: true, selected: false }
    ]
  },

  onLoad(options) {
    // 获取今天日期
    const today = new Date()
    const todayStr = today.toISOString().split('T')[0]
    this.setData({ today: todayStr })

    // 获取产品信息
    if (options.productId) {
      this.loadProductInfo(options.productId)
    }
    
    // 获取选择的保费和期限
    if (options.premium) {
      this.setData({ selectedPremium: options.premium })
    }
    if (options.period) {
      this.setData({ selectedPeriod: options.period })
    }
  },

  // 加载产品信息
  async loadProductInfo(productId) {
    try {
      const product = await app.request({
        url: `/insurance/api/products/${productId}/`
      })
      
      this.setData({ product })
      
      // 更新页面标题
      wx.setNavigationBarTitle({
        title: `投保 - ${product.name}`
      })
    } catch (error) {
      console.error('加载产品信息失败:', error)
      app.showToast('加载产品信息失败')
    }
  },

  // 输入框变化
  onInputChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    
    this.setData({
      [`formData.${field}`]: value
    })
  },

  // 年龄选择变化
  onAgeChange(e) {
    const index = parseInt(e.detail.value)
    this.setData({
      ageIndex: index,
      'formData.age': this.data.ageRanges[index]
    })
  },

  // 收入选择变化
  onIncomeChange(e) {
    const index = parseInt(e.detail.value)
    this.setData({
      incomeIndex: index,
      'formData.income': this.data.incomeRanges[index]
    })
  },

  // 面谈方式选择
  onMethodSelect(e) {
    const method = e.currentTarget.dataset.method
    this.setData({ selectedMethod: method })
  },

  // 日期选择变化
  onDateChange(e) {
    this.setData({ selectedDate: e.detail.value })
  },

  // 时间选择
  onTimeSelect(e) {
    const time = e.currentTarget.dataset.time
    const timeSlots = this.data.timeSlots.map(slot => ({
      ...slot,
      selected: slot.time === time && slot.available
    }))
    
    this.setData({
      timeSlots,
      selectedTime: time
    })
  },

  // 下一步
  async onNextStep() {
    const { currentStep } = this.data
    
    if (currentStep === 1) {
      // 验证第一步表单
      if (!this.validateStep1()) {
        return
      }
      
      // 提交咨询信息
      await this.submitConsultation()
      
      this.setData({ currentStep: 2 })
    } else if (currentStep === 2) {
      // 验证第二步表单
      if (!this.validateStep2()) {
        return
      }
      
      // 提交预约信息
      await this.submitAppointment()
      
      this.setData({ currentStep: 3 })
    }
  },

  // 上一步
  onPrevStep() {
    const { currentStep } = this.data
    if (currentStep > 1) {
      this.setData({ currentStep: currentStep - 1 })
    }
  },

  // 验证第一步表单
  validateStep1() {
    const { formData, ageIndex } = this.data
    
    if (!formData.name.trim()) {
      app.showToast('请输入姓名')
      return false
    }
    
    if (!formData.phone.trim()) {
      app.showToast('请输入手机号')
      return false
    }
    
    if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      app.showToast('请输入正确的手机号')
      return false
    }
    
    if (ageIndex === 0) {
      app.showToast('请选择年龄范围')
      return false
    }
    
    return true
  },

  // 验证第二步表单
  validateStep2() {
    const { selectedDate, selectedTime } = this.data
    
    if (!selectedDate) {
      app.showToast('请选择面谈日期')
      return false
    }
    
    if (!selectedTime) {
      app.showToast('请选择面谈时间')
      return false
    }
    
    return true
  },

  // 提交咨询信息
  async submitConsultation() {
    const { formData, product } = this.data
    
    try {
      app.showLoading('提交中...')
      
      await app.request({
        url: `/insurance/api/products/${product.id}/inquire/`,
        method: 'POST',
        data: {
          name: formData.name,
          phone: formData.phone,
          age: this.getAgeFromRange(formData.age),
          annual_income: this.getIncomeFromRange(formData.income),
          message: formData.message || '通过小程序咨询产品'
        }
      })
      
      app.showToast('咨询信息提交成功', 'success')
    } catch (error) {
      console.error('提交咨询失败:', error)
      app.showToast('提交失败，请重试')
      throw error
    } finally {
      app.hideLoading()
    }
  },

  // 提交预约信息
  async submitAppointment() {
    const { selectedMethod, selectedDate, selectedTime, formData } = this.data
    
    try {
      app.showLoading('预约中...')
      
      // 这里应该调用预约API，暂时模拟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 保存预约信息到本地
      const appointment = {
        id: Date.now(),
        productName: this.data.product.name,
        method: selectedMethod,
        date: selectedDate,
        time: selectedTime,
        customerName: formData.name,
        customerPhone: formData.phone,
        status: 'confirmed',
        createdAt: new Date().toISOString()
      }
      
      const appointments = wx.getStorageSync('appointments') || []
      appointments.push(appointment)
      wx.setStorageSync('appointments', appointments)
      
      app.showToast('预约成功', 'success')
    } catch (error) {
      console.error('预约失败:', error)
      app.showToast('预约失败，请重试')
      throw error
    } finally {
      app.hideLoading()
    }
  },

  // 从年龄范围获取数值
  getAgeFromRange(ageRange) {
    const ageMap = {
      '18-25岁': 22,
      '26-35岁': 30,
      '36-45岁': 40,
      '46-55岁': 50,
      '56-65岁': 60,
      '65岁以上': 70
    }
    return ageMap[ageRange] || null
  },

  // 从收入范围获取数值
  getIncomeFromRange(incomeRange) {
    const incomeMap = {
      '10万以下': 80000,
      '10-30万': 200000,
      '30-50万': 400000,
      '50-100万': 750000,
      '100万以上': 1200000
    }
    return incomeMap[incomeRange] || null
  },

  // 返回首页
  onBackToHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  },

  // 查看我的预约
  onViewMyAppointments() {
    wx.navigateTo({
      url: '/pages/services/services?tab=appointments'
    })
  },

  // 跳转到支付页面
  onProceedToPayment() {
    const { product, formData } = this.data;

    // 构建订单数据
    const orderData = {
      productName: product.name || '欧洲旅游保险计划A',
      insuredName: formData.name || '张小明',
      coveragePeriod: '2024-03-15 至 2024-03-20 (5天)',
      destination: '法国',
      medicalCoverage: '30万欧元',
      insuranceFee: '145.00',
      serviceFee: '0.00',
      discount: '10.00',
      totalAmount: '135.00',
      originalPrice: '145.00'
    };

    // 跳转到支付页面
    wx.navigateTo({
      url: `/pages/payment/payment?orderData=${encodeURIComponent(JSON.stringify(orderData))}`
    });
  },

  // 新增的表单处理方法
  onIdTypeChange(e) {
    this.setData({ idTypeIndex: e.detail.value });
  },

  onGenderSelect(e) {
    const gender = e.currentTarget.dataset.gender;
    this.setData({ 'formData.gender': gender });
  },

  onBirthDateChange(e) {
    this.setData({ 'formData.birthDate': e.detail.value });
  },

  onOccupationChange(e) {
    this.setData({ occupationIndex: e.detail.value });
  },

  // 健康告知
  onHealthAnswerTap(e) {
    const { id, answer } = e.currentTarget.dataset;
    const { healthQuestions } = this.data;

    const updatedQuestions = healthQuestions.map(question => {
      if (question.id === id) {
        return { ...question, answer };
      }
      return question;
    });

    this.setData({ healthQuestions: updatedQuestions });
  },

  // 受益人信息
  onBeneficiaryTypeSelect(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({ beneficiaryType: type });
  },

  onBeneficiaryInputChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({ [`beneficiaryInfo.${field}`]: value });
  },

  onRelationshipChange(e) {
    this.setData({ relationshipIndex: e.detail.value });
  },

  // 分享
  onShareAppMessage() {
    const { product } = this.data
    return {
      title: `${product.name} - 香港保险投保`,
      path: `/pages/product-detail/product-detail?id=${product.id}`,
      imageUrl: product.main_image || '/images/share-purchase.jpg'
    }
  }
})
