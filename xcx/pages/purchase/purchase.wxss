/* pages/purchase/purchase.wxss */
@import "../../common/placeholder.wxss";

/* 进度条 */
.progress-bar {
  background: white;
  padding: 48rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 0 0 auto;
}

.step-circle {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #E5E7EB;
  color: #9CA3AF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  transition: all 0.3s;
}

.progress-step.active .step-circle {
  background: #1E40AF;
  color: white;
}

.step-number {
  font-size: 24rpx;
  font-weight: bold;
}

.step-text {
  font-size: 20rpx;
  color: #6B7280;
}

.progress-step.active .step-text {
  color: #1E40AF;
  font-weight: 600;
}

.progress-line {
  width: 80rpx;
  height: 4rpx;
  background: #E5E7EB;
  margin: 0 24rpx;
  transition: all 0.3s;
}

.progress-line.active {
  background: #1E40AF;
}

/* 产品摘要 */
.product-summary {
  background: white;
  border-radius: 32rpx;
  margin: 0 32rpx 32rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 12rpx -2rpx rgba(0, 0, 0, 0.1);
}

.product-info {
  display: flex;
  gap: 24rpx;
  align-items: flex-start;
}

.product-image {
  width: 96rpx;
  height: 96rpx;
  border-radius: 16rpx;
  flex-shrink: 0;
}

.product-details {
  flex: 1;
}

.product-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 8rpx;
}

.product-company {
  display: block;
  font-size: 24rpx;
  color: #6B7280;
  margin-bottom: 16rpx;
}

.product-meta {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.product-premium,
.product-period {
  font-size: 20rpx;
  color: #1E40AF;
  background: #F0F9FF;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  display: inline-block;
  width: fit-content;
}

/* 步骤内容 */
.step-content {
  margin: 0 32rpx;
}

.step-header {
  text-align: center;
  margin-bottom: 48rpx;
}

.step-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 16rpx;
}

.step-desc {
  font-size: 24rpx;
  color: #6B7280;
  line-height: 1.5;
}

/* 表单卡片 */
.form-card {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 12rpx -2rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 48rpx;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 24rpx;
  color: #374151;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.form-input {
  width: 100%;
  border: 2rpx solid #E5E7EB;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  background: #F9FAFB;
  color: #374151;
}

.form-input:focus {
  border-color: #1E40AF;
  background: white;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  border: 2rpx solid #E5E7EB;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  background: #F9FAFB;
  color: #374151;
}

.picker-input {
  border: 2rpx solid #E5E7EB;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  background: #F9FAFB;
  color: #374151;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.picker-input .placeholder {
  color: #9CA3AF;
}

.picker-arrow {
  color: #9CA3AF;
  font-size: 20rpx;
}

/* 预约卡片 */
.appointment-card {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 12rpx -2rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 48rpx;
}

.appointment-section {
  margin-bottom: 48rpx;
}

.appointment-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 24rpx;
}

/* 面谈方式选择 */
.method-options {
  display: flex;
  gap: 24rpx;
}

.method-item {
  flex: 1;
  border: 2rpx solid #E5E7EB;
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  transition: all 0.3s;
}

.method-item.active {
  border-color: #1E40AF;
  background: #F0F9FF;
}

.method-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.method-name {
  display: block;
  font-size: 24rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 8rpx;
}

.method-desc {
  font-size: 20rpx;
  color: #6B7280;
}

/* 时间选择 */
.time-slots {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.date-picker {
  width: 100%;
}

.time-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.time-slot {
  padding: 20rpx;
  border-radius: 16rpx;
  text-align: center;
  font-size: 24rpx;
  transition: all 0.3s;
  width: 30%;
  margin-bottom: 16rpx;
}

.time-slot.available {
  border: 2rpx solid #E5E7EB;
  background: #F9FAFB;
  color: #374151;
}

.time-slot.selected {
  border: 2rpx solid #1E40AF;
  background: #1E40AF;
  color: white;
}

.time-slot.disabled {
  border: 2rpx solid #F3F4F6;
  background: #F3F4F6;
  color: #9CA3AF;
}

/* 地址卡片 */
.address-card {
  border: 2rpx solid #E5E7EB;
  border-radius: 24rpx;
  padding: 32rpx;
  background: #F9FAFB;
}

.address-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 12rpx;
}

.address-detail {
  display: block;
  font-size: 24rpx;
  color: #374151;
  margin-bottom: 8rpx;
}

.address-note {
  font-size: 20rpx;
  color: #6B7280;
}

/* 完成卡片 */
.completion-card {
  background: white;
  border-radius: 32rpx;
  padding: 48rpx 32rpx;
  box-shadow: 0 8rpx 12rpx -2rpx rgba(0, 0, 0, 0.1);
  text-align: center;
  margin-bottom: 48rpx;
}

.completion-icon {
  font-size: 96rpx;
  margin-bottom: 24rpx;
}

.completion-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 16rpx;
}

.completion-desc {
  font-size: 24rpx;
  color: #6B7280;
  margin-bottom: 48rpx;
}

/* 预约摘要 */
.appointment-summary {
  border: 2rpx solid #E5E7EB;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 48rpx;
  text-align: left;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-size: 24rpx;
  color: #6B7280;
}

.summary-value {
  font-size: 24rpx;
  color: #1F2937;
  font-weight: 600;
}

/* 下一步流程 */
.next-steps {
  text-align: left;
}

.next-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 24rpx;
}

.next-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.next-item {
  display: flex;
  gap: 16rpx;
  align-items: flex-start;
}

.next-number {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #1E40AF;
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.next-text {
  font-size: 24rpx;
  color: #374151;
  line-height: 1.5;
  flex: 1;
}

/* 步骤操作按钮 */
.step-actions {
  display: flex;
  gap: 24rpx;
  margin-top: 48rpx;
  padding-bottom: 48rpx;
}

.step-actions .btn-secondary {
  flex: 0 0 160rpx;
  background: #F3F4F6;
  color: #6B7280;
}

.step-actions .btn-primary {
  flex: 1;
}

/* 性别选择 */
.gender-options {
  display: flex;
  gap: 24rpx;
}

.gender-item {
  flex: 1;
  background: #F9FAFB;
  border: 2rpx solid #E5E7EB;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  transition: all 0.3s;
}

.gender-item.active {
  background: #EBF8FF;
  border-color: #1976D2;
}

.gender-icon {
  display: block;
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.gender-text {
  display: block;
  font-size: 26rpx;
  color: #374151;
  font-weight: 600;
}

/* 健康告知 */
.health-declaration {
  background: #FEF3C7;
  border: 2rpx solid #F59E0B;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-top: 32rpx;
}

.declaration-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #92400E;
  margin-bottom: 16rpx;
}

.declaration-text {
  display: block;
  font-size: 24rpx;
  color: #92400E;
  margin-bottom: 24rpx;
  line-height: 1.5;
}

.health-questions {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.health-question {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
}

.question-text {
  display: block;
  font-size: 26rpx;
  color: #374151;
  margin-bottom: 16rpx;
  line-height: 1.5;
}

.question-options {
  display: flex;
  gap: 16rpx;
}

.option-item {
  flex: 1;
  background: #F9FAFB;
  border: 2rpx solid #E5E7EB;
  border-radius: 12rpx;
  padding: 16rpx;
  text-align: center;
  transition: all 0.3s;
}

.option-item.active.yes {
  background: #FEE2E2;
  border-color: #DC2626;
}

.option-item.active.no {
  background: #DCFCE7;
  border-color: #16A34A;
}

.option-text {
  font-size: 24rpx;
  font-weight: 600;
}

.option-item.active.yes .option-text {
  color: #DC2626;
}

.option-item.active.no .option-text {
  color: #16A34A;
}

/* 受益人信息 */
.beneficiary-section {
  background: #F0F9FF;
  border: 2rpx solid #0EA5E9;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-top: 32rpx;
}

.section-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #0C4A6E;
  margin-bottom: 24rpx;
}

.beneficiary-options {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.beneficiary-item {
  flex: 1;
  background: white;
  border: 2rpx solid #E5E7EB;
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
  transition: all 0.3s;
}

.beneficiary-item.active {
  background: #EBF8FF;
  border-color: #0EA5E9;
}

.beneficiary-text {
  font-size: 24rpx;
  color: #374151;
  font-weight: 600;
}

.beneficiary-item.active .beneficiary-text {
  color: #0EA5E9;
}

.designated-beneficiary {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
}
