<!--pages/purchase/purchase.wxml-->
<view class="container">
  <!-- 进度条 -->
  <view class="progress-bar">
    <view class="progress-step {{currentStep >= 1 ? 'active' : ''}}">
      <view class="step-circle">
        <text class="step-number">1</text>
      </view>
      <text class="step-text">产品咨询</text>
    </view>
    <view class="progress-line {{currentStep >= 2 ? 'active' : ''}}"></view>
    <view class="progress-step {{currentStep >= 2 ? 'active' : ''}}">
      <view class="step-circle">
        <text class="step-number">2</text>
      </view>
      <text class="step-text">预约面谈</text>
    </view>
    <view class="progress-line {{currentStep >= 3 ? 'active' : ''}}"></view>
    <view class="progress-step {{currentStep >= 3 ? 'active' : ''}}">
      <view class="step-circle">
        <text class="step-number">3</text>
      </view>
      <text class="step-text">签约投保</text>
    </view>
  </view>

  <!-- 当前产品信息 -->
  <view class="product-summary">
    <view class="product-info">
      <view class="product-image-placeholder" wx:if="{{!product.main_image}}">
        <text class="placeholder-icon">📋</text>
      </view>
      <image wx:else src="{{product.main_image}}" class="product-image" mode="aspectFill"></image>
      <view class="product-details">
        <text class="product-name">{{product.name}}</text>
        <text class="product-company">{{product.company_name}}</text>
        <view class="product-meta">
          <text class="product-premium">年缴保费：{{selectedPremium}}{{product.currency}}</text>
          <text class="product-period">缴费期：{{selectedPeriod}}年</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 步骤1：产品咨询 -->
  <view class="step-content" wx:if="{{currentStep === 1}}">
    <view class="step-header">
      <text class="step-title">产品咨询</text>
      <text class="step-desc">请填写您的基本信息，我们的专业顾问将为您提供详细的产品咨询</text>
    </view>

    <view class="form-card">
      <view class="form-group">
        <text class="form-label">姓名 *</text>
        <input class="form-input" placeholder="请输入您的姓名" value="{{formData.name}}" bindinput="onInputChange" data-field="name" />
      </view>

      <view class="form-group">
        <text class="form-label">手机号 *</text>
        <input class="form-input" placeholder="请输入手机号" type="number" value="{{formData.phone}}" bindinput="onInputChange" data-field="phone" />
      </view>

      <view class="form-group">
        <text class="form-label">年龄 *</text>
        <picker bindchange="onAgeChange" value="{{ageIndex}}" range="{{ageRanges}}">
          <view class="picker-input">
            <text class="{{ageIndex === 0 ? 'placeholder' : ''}}">{{ageRanges[ageIndex]}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <view class="form-group">
        <text class="form-label">年收入</text>
        <picker bindchange="onIncomeChange" value="{{incomeIndex}}" range="{{incomeRanges}}">
          <view class="picker-input">
            <text class="{{incomeIndex === 0 ? 'placeholder' : ''}}">{{incomeRanges[incomeIndex]}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <view class="form-group">
        <text class="form-label">证件类型 *</text>
        <picker bindchange="onIdTypeChange" value="{{idTypeIndex}}" range="{{idTypes}}">
          <view class="picker-input">
            <text class="{{idTypeIndex === 0 ? 'placeholder' : ''}}">{{idTypes[idTypeIndex]}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <view class="form-group">
        <text class="form-label">证件号码 *</text>
        <input class="form-input" placeholder="请输入证件号码" value="{{formData.idNumber}}" bindinput="onInputChange" data-field="idNumber" />
      </view>

      <view class="form-group">
        <text class="form-label">性别 *</text>
        <view class="gender-options">
          <view class="gender-item {{formData.gender === 'male' ? 'active' : ''}}" bindtap="onGenderSelect" data-gender="male">
            <text class="gender-icon">👨</text>
            <text class="gender-text">男</text>
          </view>
          <view class="gender-item {{formData.gender === 'female' ? 'active' : ''}}" bindtap="onGenderSelect" data-gender="female">
            <text class="gender-icon">👩</text>
            <text class="gender-text">女</text>
          </view>
        </view>
      </view>

      <view class="form-group">
        <text class="form-label">出生日期 *</text>
        <picker mode="date" value="{{formData.birthDate}}" end="{{today}}" bindchange="onBirthDateChange">
          <view class="picker-input">
            <text class="{{!formData.birthDate ? 'placeholder' : ''}}">{{formData.birthDate || '请选择出生日期'}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <view class="form-group">
        <text class="form-label">职业</text>
        <picker bindchange="onOccupationChange" value="{{occupationIndex}}" range="{{occupations}}">
          <view class="picker-input">
            <text class="{{occupationIndex === 0 ? 'placeholder' : ''}}">{{occupations[occupationIndex]}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <view class="form-group">
        <text class="form-label">咨询内容</text>
        <textarea class="form-textarea" placeholder="请描述您的保险需求或疑问" value="{{formData.message}}" bindinput="onInputChange" data-field="message"></textarea>
      </view>

      <!-- 健康告知 -->
      <view class="health-declaration">
        <text class="declaration-title">健康告知</text>
        <view class="declaration-content">
          <text class="declaration-text">请仔细阅读以下健康告知事项，并如实回答：</text>
          <view class="health-questions">
            <view class="health-question" wx:for="{{healthQuestions}}" wx:key="id">
              <text class="question-text">{{item.question}}</text>
              <view class="question-options">
                <view class="option-item {{item.answer === 'yes' ? 'active yes' : ''}}" bindtap="onHealthAnswerTap" data-id="{{item.id}}" data-answer="yes">
                  <text class="option-text">是</text>
                </view>
                <view class="option-item {{item.answer === 'no' ? 'active no' : ''}}" bindtap="onHealthAnswerTap" data-id="{{item.id}}" data-answer="no">
                  <text class="option-text">否</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 受益人信息 -->
      <view class="beneficiary-section">
        <text class="section-title">受益人信息</text>
        <view class="beneficiary-options">
          <view class="beneficiary-item {{beneficiaryType === 'legal' ? 'active' : ''}}" bindtap="onBeneficiaryTypeSelect" data-type="legal">
            <text class="beneficiary-text">法定受益人</text>
          </view>
          <view class="beneficiary-item {{beneficiaryType === 'designated' ? 'active' : ''}}" bindtap="onBeneficiaryTypeSelect" data-type="designated">
            <text class="beneficiary-text">指定受益人</text>
          </view>
        </view>

        <view class="designated-beneficiary" wx:if="{{beneficiaryType === 'designated'}}">
          <view class="form-group">
            <text class="form-label">受益人姓名</text>
            <input class="form-input" placeholder="请输入受益人姓名" value="{{beneficiaryInfo.name}}" bindinput="onBeneficiaryInputChange" data-field="name" />
          </view>
          <view class="form-group">
            <text class="form-label">与被保险人关系</text>
            <picker bindchange="onRelationshipChange" value="{{relationshipIndex}}" range="{{relationships}}">
              <view class="picker-input">
                <text class="{{relationshipIndex === 0 ? 'placeholder' : ''}}">{{relationships[relationshipIndex]}}</text>
                <text class="picker-arrow">▼</text>
              </view>
            </picker>
          </view>
        </view>
      </view>
    </view>

    <view class="step-actions">
      <button class="btn-primary" bindtap="onNextStep">提交咨询</button>
    </view>
  </view>

  <!-- 步骤2：预约面谈 -->
  <view class="step-content" wx:if="{{currentStep === 2}}">
    <view class="step-header">
      <text class="step-title">预约面谈</text>
      <text class="step-desc">选择合适的时间和地点，与我们的专业顾问进行面对面咨询</text>
    </view>

    <view class="appointment-card">
      <view class="appointment-section">
        <text class="section-title">选择面谈方式</text>
        <view class="method-options">
          <view class="method-item {{selectedMethod === 'online' ? 'active' : ''}}" bindtap="onMethodSelect" data-method="online">
            <view class="method-icon">💻</view>
            <text class="method-name">线上视频</text>
            <text class="method-desc">微信视频通话</text>
          </view>
          <view class="method-item {{selectedMethod === 'offline' ? 'active' : ''}}" bindtap="onMethodSelect" data-method="offline">
            <view class="method-icon">🏢</view>
            <text class="method-name">线下面谈</text>
            <text class="method-desc">香港办公室</text>
          </view>
        </view>
      </view>

      <view class="appointment-section">
        <text class="section-title">选择面谈时间</text>
        <view class="time-slots">
          <view class="date-picker">
            <picker mode="date" value="{{selectedDate}}" start="{{today}}" bindchange="onDateChange">
              <view class="picker-input">
                <text>{{selectedDate || '选择日期'}}</text>
                <text class="picker-arrow">▼</text>
              </view>
            </picker>
          </view>
          <view class="time-grid">
            <view class="time-slot {{item.available ? (item.selected ? 'selected' : 'available') : 'disabled'}}" 
                  wx:for="{{timeSlots}}" wx:key="time" 
                  bindtap="onTimeSelect" data-time="{{item.time}}">
              <text>{{item.time}}</text>
            </view>
          </view>
        </view>
      </view>

      <view class="appointment-section" wx:if="{{selectedMethod === 'offline'}}">
        <text class="section-title">面谈地址</text>
        <view class="address-card">
          <text class="address-title">香港中环办公室</text>
          <text class="address-detail">香港中环皇后大道中99号中环中心15楼</text>
          <text class="address-note">地铁中环站A出口步行3分钟</text>
        </view>
      </view>
    </view>

    <view class="step-actions">
      <button class="btn-secondary" bindtap="onPrevStep">上一步</button>
      <button class="btn-primary" bindtap="onNextStep">确认预约</button>
    </view>
  </view>

  <!-- 步骤3：签约投保 -->
  <view class="step-content" wx:if="{{currentStep === 3}}">
    <view class="step-header">
      <text class="step-title">签约投保</text>
      <text class="step-desc">面谈完成后，您可以选择合适的保险方案进行签约投保</text>
    </view>

    <view class="completion-card">
      <view class="completion-icon">✅</view>
      <text class="completion-title">预约成功</text>
      <text class="completion-desc">我们的专业顾问将在预约时间与您联系</text>
      
      <view class="appointment-summary">
        <view class="summary-item">
          <text class="summary-label">面谈方式</text>
          <text class="summary-value">{{selectedMethod === 'online' ? '线上视频' : '线下面谈'}}</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">面谈时间</text>
          <text class="summary-value">{{selectedDate}} {{selectedTime}}</text>
        </view>
        <view class="summary-item" wx:if="{{selectedMethod === 'offline'}}">
          <text class="summary-label">面谈地址</text>
          <text class="summary-value">香港中环皇后大道中99号</text>
        </view>
      </view>

      <view class="next-steps">
        <text class="next-title">接下来的流程：</text>
        <view class="next-list">
          <view class="next-item">
            <text class="next-number">1</text>
            <text class="next-text">顾问将提前1天与您确认面谈安排</text>
          </view>
          <view class="next-item">
            <text class="next-number">2</text>
            <text class="next-text">面谈时详细了解您的保险需求</text>
          </view>
          <view class="next-item">
            <text class="next-number">3</text>
            <text class="next-text">为您定制最适合的保险方案</text>
          </view>
          <view class="next-item">
            <text class="next-number">4</text>
            <text class="next-text">协助您完成投保手续</text>
          </view>
        </view>
      </view>
    </view>

    <view class="step-actions">
      <button class="btn-secondary" bindtap="onBackToHome">返回首页</button>
      <button class="btn-primary" bindtap="onProceedToPayment">立即支付</button>
    </view>
  </view>
</view>
