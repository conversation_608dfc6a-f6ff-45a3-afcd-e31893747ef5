/**index.wxss - 简化版本**/
page {
  height: 100vh;
  background-color: #1976D2;
}

.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 60rpx 40rpx;
  box-sizing: border-box;
  background-color: #1976D2;
}

/* 欢迎页面头部 */
.welcome-header {
  text-align: center;
  color: #ffffff;
  margin-bottom: 60rpx;
}

.text-logo {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 30rpx;
  border-radius: 15rpx;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.app-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
  color: #ffffff;
}

.app-subtitle {
  display: block;
  font-size: 24rpx;
  color: #ffffff;
  opacity: 0.9;
}

/* 功能介绍 */
.features-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  margin: 40rpx 0;
}

.feature-item {
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 15rpx;
  padding: 30rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.feature-icon {
  font-size: 40rpx;
  margin-bottom: 15rpx;
}

.feature-title {
  display: block;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.feature-desc {
  display: block;
  color: #ffffff;
  opacity: 0.85;
  font-size: 22rpx;
  line-height: 1.4;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin: 40rpx 0 20rpx;
}

.primary-btn {
  background-color: #ffffff;
  color: #1976D2;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.primary-btn::after {
  border: none;
}

.secondary-btn {
  background-color: transparent;
  color: #ffffff;
  border: 2rpx solid #ffffff;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 76rpx;
  font-size: 26rpx;
}

.secondary-btn::after {
  border: none;
}

/* 底部信息 */
.footer-info {
  text-align: center;
  margin-top: 30rpx;
}

.footer-text {
  display: block;
  color: #ffffff;
  opacity: 0.7;
  font-size: 20rpx;
  margin-bottom: 8rpx;
}
