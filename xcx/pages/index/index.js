// index.js
// 香港保险小程序 - 欢迎页面

Page({
  data: {
    appTitle: '香港保险管理系统',
    features: [
      {
        icon: '🏛️',
        title: '专业保险产品',
        desc: '涵盖人寿、医疗、意外等多种保险产品'
      },
      {
        icon: '📱',
        title: '便捷在线服务',
        desc: '随时随地查看保单、申请理赔'
      },
      {
        icon: '🛡️',
        title: '安全保障',
        desc: '银行级安全保护，信息安全有保障'
      }
    ]
  },

  onLoad() {
    // 页面加载时的逻辑
    console.log('欢迎页面加载')

    // 测试app实例是否正常
    const app = getApp()
    console.log('App实例:', app)
    console.log('API地址:', app.globalData.apiBase)
  },

  // 前往首页
  goToHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  },

  // 前往产品页面
  goToProducts() {
    wx.switchTab({
      url: '/pages/products/products'
    })
  }
})
