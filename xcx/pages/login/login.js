// pages/login/login.js
const app = getApp()

Page({
  data: {
    phoneNumber: '',
    verifyCode: '',
    canSendCode: false,
    canLogin: false,
    codeButtonText: '获取验证码',
    countdown: 0,
    agreedToTerms: false
  },

  onLoad(options) {
    // 检查是否有重定向页面
    this.redirectUrl = options.redirect || '/pages/home/<USER>'
  },

  // 手机号输入
  onPhoneInput(e) {
    const phoneNumber = e.detail.value
    this.setData({
      phoneNumber,
      canSendCode: this.isValidPhone(phoneNumber)
    })
    this.checkCanLogin()
  },

  // 验证码输入
  onCodeInput(e) {
    const verifyCode = e.detail.value
    this.setData({
      verifyCode
    })
    this.checkCanLogin()
  },

  // 检查手机号格式
  isValidPhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  },

  // 检查是否可以登录
  checkCanLogin() {
    const { phoneNumber, verifyCode, agreedToTerms } = this.data
    const canLogin = this.isValidPhone(phoneNumber) && 
                    verifyCode.length === 6 && 
                    agreedToTerms
    this.setData({ canLogin })
  },

  // 发送验证码
  onSendCode() {
    if (!this.data.canSendCode || this.data.countdown > 0) return

    console.log('发送验证码到:', this.data.phoneNumber)
    
    // 模拟发送验证码
    wx.showToast({
      title: '验证码已发送',
      icon: 'success'
    })

    // 开始倒计时
    this.startCountdown()
  },

  // 开始倒计时
  startCountdown() {
    let countdown = 60
    this.setData({
      countdown,
      codeButtonText: `${countdown}s后重发`
    })

    const timer = setInterval(() => {
      countdown--
      if (countdown <= 0) {
        clearInterval(timer)
        this.setData({
          countdown: 0,
          codeButtonText: '获取验证码'
        })
      } else {
        this.setData({
          countdown,
          codeButtonText: `${countdown}s后重发`
        })
      }
    }, 1000)
  },

  // 登录
  onLogin() {
    if (!this.data.canLogin) return

    wx.showLoading({
      title: '登录中...'
    })

    // 模拟登录请求
    setTimeout(() => {
      wx.hideLoading()
      
      // 保存登录状态
      app.globalData.isLoggedIn = true
      app.globalData.userInfo = {
        phone: this.data.phoneNumber,
        nickname: '用户' + this.data.phoneNumber.slice(-4),
        avatar: ''
      }

      wx.setStorageSync('userInfo', app.globalData.userInfo)
      wx.setStorageSync('isLoggedIn', true)

      wx.showToast({
        title: '登录成功',
        icon: 'success'
      })

      // 跳转回原页面或首页
      setTimeout(() => {
        if (this.redirectUrl === '/pages/home/<USER>') {
          wx.switchTab({
            url: this.redirectUrl
          })
        } else {
          wx.redirectTo({
            url: this.redirectUrl
          })
        }
      }, 1500)
    }, 2000)
  },

  // 微信登录
  onWechatLogin(e) {
    if (!this.data.agreedToTerms) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    console.log('微信登录:', e.detail)
    
    if (e.detail.userInfo) {
      wx.showLoading({
        title: '登录中...'
      })

      // 模拟微信登录
      setTimeout(() => {
        wx.hideLoading()
        
        app.globalData.isLoggedIn = true
        app.globalData.userInfo = {
          nickname: e.detail.userInfo.nickName,
          avatar: e.detail.userInfo.avatarUrl,
          phone: ''
        }

        wx.setStorageSync('userInfo', app.globalData.userInfo)
        wx.setStorageSync('isLoggedIn', true)

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })

        setTimeout(() => {
          if (this.redirectUrl === '/pages/home/<USER>') {
            wx.switchTab({
              url: this.redirectUrl
            })
          } else {
            wx.redirectTo({
              url: this.redirectUrl
            })
          }
        }, 1500)
      }, 2000)
    }
  },

  // 切换协议同意状态
  onToggleAgreement() {
    const agreedToTerms = !this.data.agreedToTerms
    this.setData({ agreedToTerms })
    this.checkCanLogin()
  },

  // 查看用户协议
  onViewTerms() {
    wx.showModal({
      title: '用户协议',
      content: '这里是用户协议的内容...',
      showCancel: false
    })
  },

  // 查看隐私政策
  onViewPrivacy() {
    wx.showModal({
      title: '隐私政策',
      content: '这里是隐私政策的内容...',
      showCancel: false
    })
  },

  // 联系客服
  onContactService() {
    wx.makePhoneCall({
      phoneNumber: '************'
    })
  }
})
