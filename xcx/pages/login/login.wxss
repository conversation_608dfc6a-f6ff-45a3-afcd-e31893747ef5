/* pages/login/login.wxss */

/* 页面容器 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* 顶部装饰 */
.top-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 400rpx;
  overflow: hidden;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: -100rpx;
  right: -50rpx;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 100rpx;
  left: -75rpx;
}

.circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 200rpx;
  right: 100rpx;
}

/* 登录头部 */
.login-header {
  padding: 120rpx 48rpx 80rpx;
  text-align: center;
  position: relative;
  z-index: 1;
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo-icon {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  margin-bottom: 32rpx;
}

.app-name {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 16rpx;
}

.app-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 登录表单 */
.login-form {
  background: white;
  margin: 0 32rpx;
  border-radius: 32rpx;
  padding: 48rpx;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.1);
}

.welcome-text {
  text-align: center;
  margin-bottom: 48rpx;
}

.welcome-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1F2937;
  display: block;
  margin-bottom: 8rpx;
}

.welcome-subtitle {
  font-size: 28rpx;
  color: #6B7280;
  display: block;
}

/* 表单区域 */
.form-section {
  margin-bottom: 48rpx;
}

.input-group {
  position: relative;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  border: 2rpx solid #E5E7EB;
  border-radius: 24rpx;
  padding: 24rpx;
  background: #F9FAFB;
}

.input-group:focus-within {
  border-color: #667eea;
  background: white;
}

.input-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  color: #6B7280;
}

.form-input {
  flex: 1;
  font-size: 32rpx;
  color: #1F2937;
  background: transparent;
  border: none;
}

.code-btn {
  padding: 16rpx 24rpx;
  background: #667eea;
  color: white;
  border-radius: 16rpx;
  font-size: 24rpx;
  border: none;
  margin-left: 16rpx;
}

.code-btn.disabled {
  background: #D1D5DB;
  color: #9CA3AF;
}

.login-btn {
  width: 100%;
  height: 96rpx;
  background: #D1D5DB;
  color: #9CA3AF;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  margin-top: 32rpx;
}

.login-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

/* 其他登录方式 */
.other-login {
  margin-bottom: 48rpx;
}

.divider {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.divider-line {
  flex: 1;
  height: 1rpx;
  background: #E5E7EB;
}

.divider-text {
  font-size: 24rpx;
  color: #9CA3AF;
  margin: 0 24rpx;
}

.social-login {
  display: flex;
  justify-content: center;
}

.social-btn {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx 48rpx;
  background: #07C160;
  color: white;
  border-radius: 24rpx;
  font-size: 28rpx;
  border: none;
}

.social-icon {
  font-size: 32rpx;
}

/* 协议条款 */
.agreement {
  margin-bottom: 32rpx;
}

.agreement-check {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #D1D5DB;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.checkbox.checked {
  background: #667eea;
  border-color: #667eea;
}

.check-icon {
  font-size: 20rpx;
  color: white;
  font-weight: bold;
}

.agreement-text {
  font-size: 24rpx;
  color: #6B7280;
  line-height: 1.5;
}

.link-text {
  color: #667eea;
  text-decoration: underline;
}

/* 客服帮助 */
.help-section {
  text-align: center;
  padding: 48rpx;
}

.help-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 16rpx;
  display: block;
}

.help-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  padding: 16rpx 32rpx;
  font-size: 24rpx;
}
