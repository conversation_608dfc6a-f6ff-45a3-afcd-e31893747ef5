<!--pages/login/login.wxml-->
<view class="container">
  <!-- 顶部装饰 -->
  <view class="top-decoration">
    <view class="decoration-circle circle-1"></view>
    <view class="decoration-circle circle-2"></view>
    <view class="decoration-circle circle-3"></view>
  </view>

  <!-- 登录头部 -->
  <view class="login-header">
    <view class="logo-section">
      <view class="logo-icon">🏦</view>
      <text class="app-name">香港保险专家</text>
      <text class="app-subtitle">专业的香港保险配置服务</text>
    </view>
  </view>

  <!-- 登录表单 -->
  <view class="login-form">
    <view class="welcome-text">
      <text class="welcome-title">欢迎回来</text>
      <text class="welcome-subtitle">请登录您的账户</text>
    </view>

    <!-- 手机号登录 -->
    <view class="form-section">
      <view class="input-group">
        <view class="input-icon">📱</view>
        <input class="form-input" placeholder="请输入手机号" type="number" value="{{phoneNumber}}" bindinput="onPhoneInput" maxlength="11" />
      </view>
      
      <view class="input-group">
        <view class="input-icon">🔐</view>
        <input class="form-input" placeholder="请输入验证码" type="number" value="{{verifyCode}}" bindinput="onCodeInput" maxlength="6" />
        <button class="code-btn {{canSendCode ? '' : 'disabled'}}" bindtap="onSendCode" disabled="{{!canSendCode}}">
          {{codeButtonText}}
        </button>
      </view>

      <button class="login-btn {{canLogin ? 'active' : 'disabled'}}" bindtap="onLogin" disabled="{{!canLogin}}">
        <text class="btn-text">登录</text>
      </button>
    </view>

    <!-- 其他登录方式 -->
    <view class="other-login">
      <view class="divider">
        <view class="divider-line"></view>
        <text class="divider-text">其他登录方式</text>
        <view class="divider-line"></view>
      </view>

      <view class="social-login">
        <button class="social-btn wechat" open-type="getUserInfo" bindgetuserinfo="onWechatLogin">
          <view class="social-icon">💬</view>
          <text class="social-text">微信登录</text>
        </button>
      </view>
    </view>

    <!-- 协议条款 -->
    <view class="agreement">
      <view class="agreement-check" bindtap="onToggleAgreement">
        <view class="checkbox {{agreedToTerms ? 'checked' : ''}}">
          <text class="check-icon" wx:if="{{agreedToTerms}}">✓</text>
        </view>
        <text class="agreement-text">
          我已阅读并同意
          <text class="link-text" bindtap="onViewTerms">《用户协议》</text>
          和
          <text class="link-text" bindtap="onViewPrivacy">《隐私政策》</text>
        </text>
      </view>
    </view>
  </view>

  <!-- 客服帮助 -->
  <view class="help-section">
    <text class="help-text">登录遇到问题？</text>
    <button class="help-btn" bindtap="onContactService">联系客服</button>
  </view>

  <!-- 底部安全间距 -->
  <view class="bottom-safe-area"></view>
</view>
