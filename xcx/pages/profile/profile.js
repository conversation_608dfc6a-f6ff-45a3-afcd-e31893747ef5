// pages/profile/profile.js
const app = getApp()

Page({
  data: {
    userInfo: null,
    dashboardData: {},
    collectionsCount: 0,

    // 用户统计数据
    userPoints: 1280,
    policyCount: 3,
    walletBalance: '0.00',
    couponCount: 2,
    favoriteCount: 0,
    compareCount: 0,

    // 通知状态
    hasUnreadNotifications: true,
    hasNewClaims: false,
    hasUnreadMessages: true,

    // 最近保单
    recentPolicies: [
      {
        id: 1,
        policy_number: 'BX2024031500123',
        product_name: '友邦储蓄分红险',
        status: 'active',
        statusText: '生效中',
        start_date: '2024-03-15',
        end_date: '2044-03-15',
        premium: '¥50,000/年'
      },
      {
        id: 2,
        policy_number: 'BX2024021200089',
        product_name: '日本旅游保险',
        status: 'expired',
        statusText: '已过期',
        start_date: '2024-02-12',
        end_date: '2024-02-18',
        premium: '¥95'
      }
    ],

    // 保单状态映射
    policyStatusMap: {
      'active': '生效中',
      'pending': '待生效',
      'expired': '已过期',
      'cancelled': '已取消'
    }
  },

  onLoad() {
    this.checkLoginStatus()
  },

  onShow() {
    this.checkLoginStatus()
    this.loadCollectionsCount()
    this.loadFavoriteCount()
    this.loadCompareCount()

    // 如果已登录，加载用户数据
    if (app.globalData.isLoggedIn) {
      this.loadUserDashboard()
    }
  },

  onPullDownRefresh() {
    this.loadUserDashboard().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 检查登录状态
  checkLoginStatus() {
    this.setData({
      userInfo: app.globalData.userInfo
    })
  },

  // 加载用户仪表板数据
  async loadUserDashboard() {
    if (!app.globalData.isLoggedIn) return

    try {
      const dashboardData = await app.request({
        url: '/users/api/dashboard/'
      })

      this.setData({
        dashboardData: dashboardData
      })
    } catch (error) {
      console.error('加载用户数据失败:', error)
    }
  },

  // 加载收藏数量
  loadCollectionsCount() {
    const collections = wx.getStorageSync('collections') || []
    this.setData({
      collectionsCount: collections.length
    })
  },

  // 登录点击
  onLoginTap() {
    this.performWeChatLogin()
  },

  // 执行微信登录
  async performWeChatLogin() {
    try {
      app.showLoading('登录中...')
      
      const userInfo = await app.wechatLogin()
      
      this.setData({ userInfo })
      
      // 登录成功后加载用户数据
      this.loadUserDashboard()
      
      app.showToast('登录成功', 'success')
    } catch (error) {
      console.error('登录失败:', error)
      app.showToast('登录失败，请重试')
    } finally {
      app.hideLoading()
    }
  },

  // 统计数据点击
  onStatTap(e) {
    const type = e.currentTarget.dataset.type
    
    switch (type) {
      case 'policies':
        wx.navigateTo({
          url: '/pages/services/services?tab=policies'
        })
        break
      case 'claims':
        wx.switchTab({
          url: '/pages/claims/claims'
        })
        break
      case 'collections':
        this.onActionTap({ currentTarget: { dataset: { action: 'collections' } } })
        break
    }
  },

  // 快捷功能点击
  onActionTap(e) {
    const action = e.currentTarget.dataset.action
    
    // 检查登录状态（除了收藏功能）
    if (action !== 'collections' && !app.globalData.isLoggedIn) {
      this.showLoginPrompt()
      return
    }
    
    switch (action) {
      case 'policies':
        wx.navigateTo({
          url: '/pages/services/services?tab=policies'
        })
        break
      case 'claims':
        wx.switchTab({
          url: '/pages/claims/claims'
        })
        break
      case 'wallet':
        wx.showToast({ title: '钱包功能开发中...', icon: 'none' })
        break
      case 'coupons':
        wx.showToast({ title: '优惠券功能开发中...', icon: 'none' })
        break
      case 'favorites':
        this.showFavorites()
        break
      case 'compare':
        wx.navigateTo({
          url: '/pages/product-compare/product-compare'
        })
        break
      case 'appointments':
        wx.navigateTo({
          url: '/pages/services/services?tab=appointments'
        })
        break
      case 'collections':
        this.showCollections()
        break
    }
  },

  // 保单点击
  onPolicyTap(e) {
    const policy = e.currentTarget.dataset.policy
    wx.navigateTo({
      url: `/pages/services/services?tab=policies&policyId=${policy.id}`
    })
  },

  // 菜单点击
  onMenuTap(e) {
    const menu = e.currentTarget.dataset.menu
    
    switch (menu) {
      case 'products':
        wx.switchTab({
          url: '/pages/products/products'
        })
        break
      case 'calculator':
        wx.showModal({
          title: '提示',
          content: '保费计算器功能开发中...',
          showCancel: false
        })
        break
      case 'consult':
        wx.switchTab({
          url: '/pages/services/services'
        })
        break
      case 'profile':
        if (!app.globalData.isLoggedIn) {
          this.showLoginPrompt()
          return
        }
        wx.navigateTo({
          url: '/pages/services/services?tab=profile'
        })
        break
      case 'security':
        wx.showModal({
          title: '提示',
          content: '账户安全功能开发中...',
          showCancel: false
        })
        break
      case 'notifications':
        wx.showModal({
          title: '提示',
          content: '消息通知功能开发中...',
          showCancel: false
        })
        break
      case 'help':
        wx.navigateTo({
          url: '/pages/services/services?tab=help'
        })
        break
      case 'contact':
        this.showContactOptions()
        break
      case 'about':
        wx.navigateTo({
          url: '/pages/services/services?tab=about'
        })
        break
    }
  },

  // 显示收藏列表
  showCollections() {
    const collections = wx.getStorageSync('collections') || []
    
    if (collections.length === 0) {
      wx.showModal({
        title: '提示',
        content: '您还没有收藏任何产品',
        confirmText: '去看看',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/products/products'
            })
          }
        }
      })
      return
    }
    
    // 跳转到收藏页面（可以在services页面中实现）
    wx.navigateTo({
      url: '/pages/services/services?tab=collections'
    })
  },

  // 显示联系方式选项
  showContactOptions() {
    wx.showActionSheet({
      itemList: ['微信客服', '电话咨询', '在线客服'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 微信客服
            wx.showModal({
              title: '微信客服',
              content: '请添加微信号：HKInsurance2024',
              confirmText: '复制微信号',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  wx.setClipboardData({
                    data: 'HKInsurance2024',
                    success: () => {
                      app.showToast('微信号已复制')
                    }
                  })
                }
              }
            })
            break
          case 1:
            // 电话咨询
            wx.showModal({
              title: '电话咨询',
              content: '客服热线：+852 1234 5678',
              confirmText: '拨打电话',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  wx.makePhoneCall({
                    phoneNumber: '+85212345678'
                  })
                }
              }
            })
            break
          case 2:
            // 在线客服
            wx.switchTab({
              url: '/pages/services/services'
            })
            break
        }
      }
    })
  },

  // 显示登录提示
  showLoginPrompt() {
    wx.showModal({
      title: '提示',
      content: '请先登录后使用此功能',
      confirmText: '立即登录',
      success: (res) => {
        if (res.confirm) {
          this.performWeChatLogin()
        }
      }
    })
  },

  // 退出登录
  onLogoutTap() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          app.logout()
          this.setData({
            userInfo: null,
            dashboardData: {}
          })
          app.showToast('已退出登录')
        }
      }
    })
  },

  // 头部功能
  onSettingsTap() {
    wx.showToast({ title: '设置功能开发中...', icon: 'none' });
  },

  onNotificationsTap() {
    wx.showToast({ title: '消息通知功能开发中...', icon: 'none' });
  },

  // 快捷功能
  onActionTap(e) {
    const action = e.currentTarget.dataset.action;

    switch (action) {
      case 'policies':
        this.onViewAllPolicies();
        break;
      case 'claims':
        wx.switchTab({ url: '/pages/claims/claims' });
        break;
      case 'wallet':
        wx.showToast({ title: '钱包功能开发中...', icon: 'none' });
        break;
      case 'coupons':
        wx.showToast({ title: '优惠券功能开发中...', icon: 'none' });
        break;
    }
  },

  // 保单相关
  onViewAllPolicies() {
    wx.showToast({ title: '保单列表功能开发中...', icon: 'none' });
  },

  onPolicyTap(e) {
    const policy = e.currentTarget.dataset.policy;
    wx.showToast({ title: `查看保单：${policy.policy_number}`, icon: 'none' });
  },

  onPolicyDetailTap(e) {
    e.stopPropagation();
    const policy = e.currentTarget.dataset.policy;
    wx.showToast({ title: `保单详情：${policy.policy_number}`, icon: 'none' });
  },

  onClaimTap(e) {
    e.stopPropagation();
    const policy = e.currentTarget.dataset.policy;
    console.log('申请理赔:', policy);

    wx.navigateTo({
      url: `/pages/claims/claims?policy_id=${policy.id}&action=apply`
    });
  },

  onRenewTap(e) {
    e.stopPropagation();
    const policy = e.currentTarget.dataset.policy;
    wx.showToast({ title: `再次购买：${policy.product_name}`, icon: 'none' });
  },

  onBrowseProducts() {
    wx.switchTab({ url: '/pages/products/products' });
  },

  // 服务功能
  onServiceTap(e) {
    const service = e.currentTarget.dataset.service;

    switch (service) {
      case 'customer':
        wx.showToast({ title: '正在连接在线客服...', icon: 'loading' });
        break;
      case 'hotline':
        wx.makePhoneCall({ phoneNumber: '**********' });
        break;
      case 'faq':
        wx.showToast({ title: '常见问题功能开发中...', icon: 'none' });
        break;
      case 'guide':
        wx.showToast({ title: '理赔指南功能开发中...', icon: 'none' });
        break;
    }
  },

  // 账户管理
  onAccountTap(e) {
    const type = e.currentTarget.dataset.type;

    switch (type) {
      case 'profile':
        wx.showToast({ title: '个人信息功能开发中...', icon: 'none' });
        break;
      case 'security':
        wx.showToast({ title: '安全设置功能开发中...', icon: 'none' });
        break;
      case 'notifications':
        wx.showToast({ title: '消息通知设置功能开发中...', icon: 'none' });
        break;
    }
  },

  // 设置页面
  onSettingsTap() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  },

  // 加载收藏数量
  loadFavoriteCount() {
    const favorites = wx.getStorageSync('favorite_products') || []
    this.setData({ favoriteCount: favorites.length })
  },

  // 加载比较数量
  loadCompareCount() {
    const compareList = wx.getStorageSync('compare_products') || []
    this.setData({ compareCount: compareList.length })
  },

  // 显示收藏列表
  showFavorites() {
    const favorites = wx.getStorageSync('favorite_products') || []

    if (favorites.length === 0) {
      wx.showModal({
        title: '暂无收藏',
        content: '您还没有收藏任何产品，去产品页面看看吧',
        confirmText: '去看看',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/products/products'
            })
          }
        }
      })
      return
    }

    // 显示收藏列表
    const itemList = favorites.slice(0, 6).map(item => item.name)
    if (favorites.length > 6) {
      itemList.push('查看更多...')
    }

    wx.showActionSheet({
      itemList: itemList,
      success: (res) => {
        if (res.tapIndex < favorites.length && res.tapIndex < 6) {
          // 跳转到产品详情
          const product = favorites[res.tapIndex]
          wx.navigateTo({
            url: `/pages/product-detail/product-detail?id=${product.id}`
          })
        } else if (res.tapIndex === 6) {
          // 查看更多 - 可以创建一个收藏列表页面
          wx.showToast({ title: '收藏列表页面开发中...', icon: 'none' })
        }
      }
    })
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '香港保险专家 - 专业的香港保险配置服务',
      path: '/pages/home/<USER>',
      imageUrl: '/images/share-profile.jpg'
    }
  }
})
