/* pages/profile/profile.wxss */
@import "../../common/placeholder.wxss";

/* 页面容器 */
.container {
  background: #f5f5f5;
  min-height: 100vh;
}

/* 用户信息头部 */
.profile-header {
  background: linear-gradient(135deg, #1976D2 0%, #42A5F5 100%);
  color: white;
  padding: 20rpx 32rpx 48rpx;
  position: relative;
  overflow: hidden;
}

.header-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.header-title {
  font-size: 32rpx;
  font-weight: bold;
}

.header-icons {
  display: flex;
  gap: 20rpx;
}

.icon-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.icon-btn .icon {
  font-size: 28rpx;
}

.notification-dot {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff4444;
  border-radius: 50%;
  border: 2rpx solid white;
}

.user-info,
.login-prompt {
  display: flex;
  align-items: center;
  gap: 24rpx;
  position: relative;
  z-index: 2;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  flex-shrink: 0;
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.user-phone {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
  margin-bottom: 16rpx;
}

.user-badges {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.vip-badge {
  background: linear-gradient(45deg, #FFD700, #FFA500);
  color: #333;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: bold;
}

.user-points {
  font-size: 22rpx;
  opacity: 0.9;
}

.arrow-icon {
  font-size: 24rpx;
  opacity: 0.7;
}

/* 快捷功能网格 */
.quick-actions-grid {
  display: flex;
  flex-wrap: wrap;
  background: white;
  margin: -30rpx 32rpx 32rpx;
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

.action-item {
  background: white;
  padding: 40rpx 20rpx;
  text-align: center;
  position: relative;
  width: 33.33%;
  border-right: 1rpx solid #F3F4F6;
  border-bottom: 1rpx solid #F3F4F6;
}

.action-item:nth-child(3n) {
  border-right: none;
}

.action-item:nth-child(n+4) {
  border-bottom: none;
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto 16rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  position: relative;
}

.policies-icon { background: #E3F2FD; }
.claims-icon { background: #F3E5F5; }
.wallet-icon { background: #E8F5E8; }
.coupons-icon { background: #FFF3E0; }

.action-text {
  display: block;
  font-size: 24rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.action-count,
.action-amount {
  display: block;
  font-size: 20rpx;
  color: #666;
}

/* 通用样式 */
.section {
  margin: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.view-all-btn {
  background: none;
  border: none;
  color: #1976D2;
  font-size: 24rpx;
  padding: 0;
}

/* 保单列表 */
.policies-list {
  background: white;
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.policy-item {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.policy-item:last-child {
  border-bottom: none;
}

.policy-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.policy-status-tag {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: bold;
}

.policy-status-tag.active {
  background: #E8F5E8;
  color: #2E7D32;
}

.policy-status-tag.expired {
  background: #FFEBEE;
  color: #C62828;
}

.policy-number {
  font-size: 20rpx;
  color: #666;
}

.policy-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.policy-period {
  display: block;
  font-size: 22rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.policy-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.policy-premium {
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
}

.policy-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  border: none;
}

.action-btn.detail {
  background: #F5F5F5;
  color: #666;
}

.action-btn.claim {
  background: #D1FAE5;
  color: #059669;
}

.action-btn.renew {
  background: #E3F2FD;
  color: #1976D2;
}

/* 空状态 */
.empty-policies {
  background: white;
  border-radius: 32rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.empty-icon {
  font-size: 80rpx;
  opacity: 0.3;
  margin-bottom: 20rpx;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
}

.empty-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.browse-btn {
  background: #1976D2;
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 26rpx;
}

/* 服务功能 */
.service-items,
.account-items {
  background: white;
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.service-item,
.account-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.service-item:last-child,
.account-item:last-child {
  border-bottom: none;
}

.service-icon,
.account-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  margin-right: 24rpx;
}

.customer-icon { background: #E3F2FD; }
.hotline-icon { background: #E8F5E8; }
.faq-icon { background: #F3E5F5; }
.guide-icon { background: #FFF3E0; }

.profile-icon { background: #E3F2FD; }
.security-icon { background: #FFEBEE; }
.notifications-icon { background: #F3E5F5; }

.service-title,
.account-title {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.service-status,
.service-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.phone-number {
  font-size: 22rpx;
  color: #666;
}

.status-text {
  font-size: 20rpx;
  color: #10B981;
}

/* 登录相关 */
.login-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  border-radius: 25rpx;
  padding: 16rpx 32rpx;
  font-size: 24rpx;
}

.logout-section {
  margin: 60rpx 32rpx 40rpx;
}

.logout-btn {
  background: #FFEBEE;
  color: #C62828;
  border: none;
  border-radius: 25rpx;
  padding: 24rpx;
  font-size: 28rpx;
  width: 100%;
}

.badge {
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 20rpx;
  font-weight: bold;
}

.badge.verified {
  background: rgba(16, 185, 129, 0.2);
  color: #10B981;
  border: 1rpx solid rgba(16, 185, 129, 0.3);
}

.badge.vip {
  background: rgba(245, 158, 11, 0.2);
  color: #F59E0B;
  border: 1rpx solid rgba(245, 158, 11, 0.3);
}

.login-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 48rpx;
  padding: 16rpx 32rpx;
  font-size: 24rpx;
  font-weight: 600;
}

/* 数据统计卡片 */
.stats-card {
  background: white;
  border-radius: 32rpx;
  margin: -48rpx 32rpx 32rpx;
  padding: 48rpx 32rpx;
  box-shadow: 0 16rpx 24rpx -4rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 3;
  display: flex;
  align-items: center;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #1E40AF;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #6B7280;
}

.stat-divider {
  width: 2rpx;
  height: 80rpx;
  background: #E5E7EB;
  margin: 0 32rpx;
}

/* 快捷功能 */
.quick-actions {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 32rpx 32rpx;
}

.quick-actions .action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 32rpx 16rpx;
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 12rpx -2rpx rgba(0, 0, 0, 0.1);
  width: 22%;
  margin-bottom: 16rpx;
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-bottom: 16rpx;
}

.action-icon.policies {
  background: #DBEAFE;
}

.action-icon.claims {
  background: #D1FAE5;
}

.action-icon.appointments {
  background: #E0E7FF;
}

.action-icon.collections {
  background: #FEE2E2;
}

.action-text {
  font-size: 24rpx;
  color: #374151;
  font-weight: 500;
}

/* 分区卡片 */
.section-card {
  background: white;
  border-radius: 32rpx;
  margin: 0 32rpx 32rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 12rpx -2rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1F2937;
}

.section-more {
  font-size: 24rpx;
  color: #1E40AF;
}

/* 保单列表 */
.policies-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.policy-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: #F9FAFB;
  border-radius: 16rpx;
  border: 1rpx solid #E5E7EB;
}

.policy-info {
  flex: 1;
  margin-right: 24rpx;
}

.policy-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1F2937;
  margin-bottom: 8rpx;
}

.policy-number {
  display: block;
  font-size: 20rpx;
  color: #9CA3AF;
  margin-bottom: 8rpx;
}

.policy-status {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 600;
}

.policy-status.status-active {
  background: #D1FAE5;
  color: #059669;
}

.policy-status.status-pending {
  background: #FEF3C7;
  color: #D97706;
}

.policy-status.status-expired {
  background: #F3F4F6;
  color: #6B7280;
}

.policy-amount {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: right;
}

.amount-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #1E40AF;
}

.amount-currency {
  font-size: 20rpx;
  color: #9CA3AF;
  margin-top: 4rpx;
}

/* 功能菜单 */
.menu-sections {
  padding: 0 32rpx;
}

.menu-section {
  margin-bottom: 32rpx;
}

.menu-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 24rpx;
  padding-left: 16rpx;
}

.menu-items {
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 12rpx -2rpx rgba(0, 0, 0, 0.1);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F3F4F6;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #F3F4F6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.menu-text {
  flex: 1;
  font-size: 28rpx;
  color: #374151;
}

.menu-arrow {
  font-size: 24rpx;
  color: #9CA3AF;
}

/* 退出登录 */
.logout-section {
  padding: 48rpx 32rpx;
}

.logout-btn {
  width: 100%;
  background: #FEE2E2;
  color: #DC2626;
  border: 2rpx solid #FECACA;
  border-radius: 24rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
}
