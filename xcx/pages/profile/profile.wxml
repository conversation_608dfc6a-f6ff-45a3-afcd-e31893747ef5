<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 用户信息头部 -->
  <view class="profile-header gradient-bg">
    <view class="header-top">
      <text class="header-title">个人中心</text>
      <view class="header-icons">
        <view class="icon-btn" bindtap="onSettingsTap">
          <text class="icon">⚙️</text>
        </view>
        <view class="icon-btn" bindtap="onNotificationsTap">
          <text class="icon">🔔</text>
          <view class="notification-dot" wx:if="{{hasUnreadNotifications}}"></view>
        </view>
      </view>
    </view>

    <view class="user-info" wx:if="{{userInfo}}">
      <view class="user-avatar-placeholder" wx:if="{{!userInfo.avatar}}">
        <text class="placeholder-icon">👤</text>
      </view>
      <image wx:else src="{{userInfo.avatar}}" class="user-avatar"></image>
      <view class="user-details">
        <text class="user-name">{{userInfo.nickname || userInfo.user.username}}</text>
        <text class="user-phone">手机号：{{userInfo.phone || '138****8888'}}</text>
        <view class="user-badges">
          <view class="vip-badge">
            <text class="badge-text">VIP会员</text>
          </view>
          <text class="user-points">积分：{{userPoints || '1,280'}}</text>
        </view>
      </view>
      <text class="arrow-icon">></text>
    </view>

    <!-- 未登录状态 -->
    <view class="login-prompt" wx:else>
      <view class="user-avatar-placeholder">
        <text class="placeholder-icon">👤</text>
      </view>
      <view class="user-details">
        <text class="user-name">点击登录</text>
        <text class="user-phone">登录后享受更多服务</text>
      </view>
      <button class="login-btn" bindtap="onLoginTap">立即登录</button>
    </view>
  </view>

  <!-- 快捷功能网格 -->
  <view class="quick-actions-grid">
    <view class="action-item" bindtap="onActionTap" data-action="policies">
      <view class="action-icon policies-icon">📋</view>
      <text class="action-text">我的保单</text>
      <text class="action-count">{{policyCount || 3}}</text>
    </view>
    <view class="action-item" bindtap="onActionTap" data-action="claims">
      <view class="action-icon claims-icon">🛡️</view>
      <text class="action-text">理赔服务</text>
      <view class="notification-dot" wx:if="{{hasNewClaims}}"></view>
    </view>
    <view class="action-item" bindtap="onActionTap" data-action="wallet">
      <view class="action-icon wallet-icon">💰</view>
      <text class="action-text">我的钱包</text>
      <text class="action-amount">¥{{walletBalance || '0.00'}}</text>
    </view>
    <view class="action-item" bindtap="onActionTap" data-action="coupons">
      <view class="action-icon coupons-icon">🎫</view>
      <text class="action-text">优惠券</text>
      <text class="action-count">{{couponCount || 2}}</text>
    </view>
    <view class="action-item" bindtap="onActionTap" data-action="favorites">
      <view class="action-icon favorites-icon">❤️</view>
      <text class="action-text">我的收藏</text>
      <text class="action-count">{{favoriteCount || 0}}</text>
    </view>
    <view class="action-item" bindtap="onActionTap" data-action="compare">
      <view class="action-icon compare-icon">📊</view>
      <text class="action-text">产品比较</text>
      <text class="action-count">{{compareCount || 0}}</text>
    </view>
  </view>

  <!-- 我的保单 -->
  <view class="section" wx:if="{{userInfo}}">
    <view class="section-header">
      <text class="section-title">我的保单</text>
      <button class="view-all-btn" bindtap="onViewAllPolicies">查看全部</button>
    </view>

    <view class="policies-list">
      <view class="policy-item" wx:for="{{recentPolicies}}" wx:key="id" bindtap="onPolicyTap" data-policy="{{item}}">
        <view class="policy-header">
          <view class="policy-status-tag {{item.status}}">{{item.statusText}}</view>
          <text class="policy-number">保单号：{{item.policy_number}}</text>
        </view>
        <text class="policy-name">{{item.product_name}}</text>
        <text class="policy-period">保障期间：{{item.start_date}} 至 {{item.end_date}}</text>
        <view class="policy-footer">
          <text class="policy-premium">保费：{{item.premium}}</text>
          <view class="policy-actions">
            <button class="action-btn detail" bindtap="onPolicyDetailTap" data-policy="{{item}}">查看详情</button>
            <button class="action-btn claim" wx:if="{{item.status === 'active'}}" bindtap="onClaimTap" data-policy="{{item}}">申请理赔</button>
            <button class="action-btn renew" wx:if="{{item.status === 'expired'}}" bindtap="onRenewTap" data-policy="{{item}}">再次购买</button>
          </view>
        </view>
      </view>
    </view>

    <view class="empty-policies" wx:if="{{recentPolicies.length === 0}}">
      <text class="empty-icon">📄</text>
      <text class="empty-text">暂无保单</text>
      <text class="empty-desc">您还没有购买任何保险产品</text>
      <button class="browse-btn" bindtap="onBrowseProducts">浏览产品</button>
    </view>
  </view>

  <!-- 服务功能 -->
  <view class="section">
    <text class="section-title">服务功能</text>
    <view class="service-items">
      <view class="service-item" bindtap="onServiceTap" data-service="customer">
        <view class="service-icon customer-icon">🎧</view>
        <text class="service-title">在线客服</text>
        <view class="service-status">
          <view class="notification-dot" wx:if="{{hasUnreadMessages}}"></view>
          <text class="arrow-icon">></text>
        </view>
      </view>

      <view class="service-item" bindtap="onServiceTap" data-service="hotline">
        <view class="service-icon hotline-icon">📞</view>
        <text class="service-title">客服热线</text>
        <view class="service-info">
          <text class="phone-number">************</text>
          <text class="arrow-icon">></text>
        </view>
      </view>

      <view class="service-item" bindtap="onServiceTap" data-service="faq">
        <view class="service-icon faq-icon">❓</view>
        <text class="service-title">常见问题</text>
        <text class="arrow-icon">></text>
      </view>

      <view class="service-item" bindtap="onServiceTap" data-service="guide">
        <view class="service-icon guide-icon">📋</view>
        <text class="service-title">理赔指南</text>
        <text class="arrow-icon">></text>
      </view>
    </view>
  </view>

  <!-- 账户管理 -->
  <view class="section">
    <text class="section-title">账户管理</text>
    <view class="account-items">
      <view class="account-item" bindtap="onAccountTap" data-type="profile">
        <view class="account-icon profile-icon">👤</view>
        <text class="account-title">个人信息</text>
        <text class="arrow-icon">></text>
      </view>

      <view class="account-item" bindtap="onAccountTap" data-type="security">
        <view class="account-icon security-icon">🔒</view>
        <text class="account-title">安全设置</text>
        <text class="arrow-icon">></text>
      </view>

      <view class="account-item" bindtap="onAccountTap" data-type="notifications">
        <view class="account-icon notifications-icon">🔔</view>
        <text class="account-title">消息通知</text>
        <text class="arrow-icon">></text>
      </view>

      <view class="account-item" bindtap="onSettingsTap">
        <view class="account-icon settings-icon">⚙️</view>
        <text class="account-title">设置</text>
        <text class="arrow-icon">></text>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section" wx:if="{{userInfo}}">
    <button class="logout-btn" bindtap="onLogoutTap">退出登录</button>
  </view>

  <!-- 底部安全间距 -->
  <view class="bottom-safe-area"></view>
</view>
