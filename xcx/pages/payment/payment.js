// pages/payment/payment.js
Page({
  data: {
    // 订单信息
    orderInfo: {
      productName: '欧洲旅游保险计划A',
      tag: '申根签证',
      insuredName: '张小明',
      coveragePeriod: '2024-03-15 至 2024-03-20 (5天)',
      destination: '法国',
      medicalCoverage: '30万欧元',
      insuranceFee: '145.00',
      serviceFee: '0.00',
      discount: '10.00',
      totalAmount: '135.00',
      originalPrice: '145.00'
    },
    
    // 支付方式
    selectedPayment: 'wechat',
    
    // 协议确认
    agreementChecked: true,

    // 优惠券
    showCouponModal: false,
    selectedCoupon: null,
    availableCoupons: [
      {
        id: 1,
        name: '新用户专享券',
        discount: 20,
        min_amount: 100,
        expire_date: '2024-12-31'
      },
      {
        id: 2,
        name: '保险优惠券',
        discount: 10,
        min_amount: 50,
        expire_date: '2024-11-30'
      }
    ],

    // 倒计时
    countdownTime: '14:58',
    countdownTimer: null,
    remainingSeconds: 898 // 14分58秒
  },

  onLoad(options) {
    console.log('支付页面加载', options);
    
    // 如果有传入的订单信息，更新数据
    if (options.orderData) {
      try {
        const orderData = JSON.parse(decodeURIComponent(options.orderData));
        this.setData({
          orderInfo: { ...this.data.orderInfo, ...orderData }
        });
      } catch (e) {
        console.error('解析订单数据失败', e);
      }
    }
    
    // 启动倒计时
    this.startCountdown();
  },

  onUnload() {
    // 清除倒计时
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer);
    }
  },

  // 启动支付倒计时
  startCountdown() {
    const timer = setInterval(() => {
      let { remainingSeconds } = this.data;
      
      if (remainingSeconds <= 0) {
        clearInterval(timer);
        this.handlePaymentTimeout();
        return;
      }
      
      remainingSeconds--;
      const minutes = Math.floor(remainingSeconds / 60);
      const seconds = remainingSeconds % 60;
      const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
      
      this.setData({
        remainingSeconds,
        countdownTime: timeString
      });
    }, 1000);
    
    this.setData({ countdownTimer: timer });
  },

  // 支付超时处理
  handlePaymentTimeout() {
    wx.showModal({
      title: '支付超时',
      content: '支付时间已超时，请重新下单',
      showCancel: false,
      success: () => {
        wx.navigateBack();
      }
    });
  },

  // 选择支付方式
  onPaymentSelect(e) {
    const method = e.currentTarget.dataset.method;
    this.setData({ selectedPayment: method });
  },

  // 优惠券选择
  onCouponTap() {
    wx.showToast({
      title: '优惠券功能开发中...',
      icon: 'none'
    });
  },

  // 协议确认切换
  onAgreementToggle() {
    this.setData({
      agreementChecked: !this.data.agreementChecked
    });
  },

  // 查看协议
  onAgreementTap(e) {
    const type = e.currentTarget.dataset.type;
    const title = type === 'payment' ? '支付服务协议' : '自动续保条款';
    
    wx.showModal({
      title: title,
      content: '协议内容详情...',
      showCancel: false
    });
  },

  // 立即支付
  onPayNow() {
    if (!this.data.agreementChecked) {
      wx.showToast({
        title: '请先同意服务协议',
        icon: 'none'
      });
      return;
    }

    const { selectedPayment, orderInfo } = this.data;
    
    wx.showLoading({
      title: '正在支付...'
    });

    // 模拟支付流程
    setTimeout(() => {
      wx.hideLoading();
      
      if (selectedPayment === 'wechat') {
        this.handleWechatPay();
      } else if (selectedPayment === 'alipay') {
        this.handleAlipayPay();
      } else if (selectedPayment === 'bank') {
        this.handleBankPay();
      } else if (selectedPayment === 'apple') {
        this.handleApplePay();
      }
    }, 1500);
  },

  // 微信支付
  handleWechatPay() {
    // 实际项目中这里会调用微信支付API
    wx.requestPayment({
      timeStamp: Date.now().toString(),
      nonceStr: 'random_string',
      package: 'prepay_id=mock_prepay_id',
      signType: 'MD5',
      paySign: 'mock_pay_sign',
      success: (res) => {
        this.handlePaymentSuccess();
      },
      fail: (res) => {
        // 模拟支付成功（实际开发中移除）
        this.handlePaymentSuccess();
      }
    });
  },

  // 支付宝支付
  handleAlipayPay() {
    wx.showToast({
      title: '支付宝支付功能开发中...',
      icon: 'none'
    });
  },

  // 银行卡支付
  handleBankPay() {
    wx.showToast({
      title: '银行卡支付功能开发中...',
      icon: 'none'
    });
  },

  // 支付成功处理
  handlePaymentSuccess() {
    // 清除倒计时
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer);
    }

    wx.showToast({
      title: '支付成功',
      icon: 'success',
      duration: 2000
    });

    setTimeout(() => {
      // 跳转到支付成功页面或返回首页
      wx.redirectTo({
        url: '/pages/payment-success/payment-success'
      });
    }, 2000);
  },

  // 优惠券相关方法
  onCouponTap() {
    this.setData({ showCouponModal: true });
  },

  onCloseCouponModal() {
    this.setData({ showCouponModal: false });
  },

  onSelectCoupon(e) {
    const coupon = e.currentTarget.dataset.coupon;

    this.setData({
      selectedCoupon: coupon || null,
      showCouponModal: false
    });

    // 重新计算总价
    this.calculateTotalAmount();

    if (coupon) {
      wx.showToast({
        title: `已使用¥${coupon.discount}优惠券`,
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '已取消使用优惠券',
        icon: 'success'
      });
    }
  },

  // 计算总金额
  calculateTotalAmount() {
    const { orderInfo, selectedCoupon } = this.data;
    const originalPrice = parseFloat(orderInfo.originalPrice || orderInfo.insuranceFee);
    const serviceFee = parseFloat(orderInfo.serviceFee || 0);
    const discount = selectedCoupon ? selectedCoupon.discount : 0;

    const totalAmount = Math.max(0, originalPrice + serviceFee - discount);

    this.setData({
      'orderInfo.discount': discount.toFixed(2),
      'orderInfo.totalAmount': totalAmount.toFixed(2)
    });
  },

  // Apple Pay支付
  handleApplePay() {
    wx.showToast({
      title: 'Apple Pay功能开发中...',
      icon: 'none'
    });
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '香港保险支付 - 安全便捷的保险购买体验',
      path: '/pages/home/<USER>',
      imageUrl: '/images/share-payment.jpg'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '香港保险支付 - 安全便捷的保险购买体验',
      imageUrl: '/images/share-payment.jpg'
    };
  }
});
