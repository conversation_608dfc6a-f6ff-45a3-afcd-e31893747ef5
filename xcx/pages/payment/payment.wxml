<!--pages/payment/payment.wxml-->
<view class="container">
  <!-- 进度条 -->
  <view class="progress-bar">
    <view class="progress-steps">
      <view class="step-item completed">
        <view class="step-circle">
          <text class="step-icon">✓</text>
        </view>
        <text class="step-text">选择产品</text>
      </view>
      <view class="progress-line completed"></view>
      <view class="step-item completed">
        <view class="step-circle">
          <text class="step-icon">✓</text>
        </view>
        <text class="step-text">填写信息</text>
      </view>
      <view class="progress-line active"></view>
      <view class="step-item active">
        <view class="step-circle">
          <text class="step-number">3</text>
        </view>
        <text class="step-text">确认支付</text>
      </view>
    </view>
  </view>

  <!-- 订单信息 -->
  <view class="section">
    <view class="section-header">
      <text class="section-icon">📄</text>
      <text class="section-title">订单信息</text>
    </view>
    
    <view class="order-card">
      <view class="product-info">
        <view class="product-header">
          <text class="product-name">{{orderInfo.productName}}</text>
          <view class="product-tag">{{orderInfo.tag}}</view>
        </view>
        
        <view class="order-details">
          <view class="detail-item">
            <text class="detail-label">被保险人</text>
            <text class="detail-value">{{orderInfo.insuredName}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">保障期间</text>
            <text class="detail-value">{{orderInfo.coveragePeriod}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">目的地</text>
            <text class="detail-value">{{orderInfo.destination}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">医疗保障</text>
            <text class="detail-value">{{orderInfo.medicalCoverage}}</text>
          </view>
        </view>
      </view>
      
      <!-- 费用明细 -->
      <view class="cost-breakdown">
        <text class="breakdown-title">费用明细</text>
        <view class="cost-items">
          <view class="cost-item">
            <text class="cost-label">保险费用</text>
            <text class="cost-value">¥{{orderInfo.insuranceFee}}</text>
          </view>
          <view class="cost-item">
            <text class="cost-label">服务费</text>
            <text class="cost-value">¥{{orderInfo.serviceFee}}</text>
          </view>
          <view class="cost-item discount">
            <text class="cost-label">优惠券抵扣</text>
            <text class="cost-value">-¥{{orderInfo.discount}}</text>
          </view>
          <view class="cost-item total">
            <text class="cost-label">应付金额</text>
            <text class="cost-value">¥{{orderInfo.totalAmount}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 支付方式 -->
  <view class="section">
    <view class="section-header">
      <text class="section-icon">💳</text>
      <text class="section-title">选择支付方式</text>
    </view>
    
    <view class="payment-methods">
      <view class="payment-item {{selectedPayment === 'wechat' ? 'selected' : ''}}" bindtap="onPaymentSelect" data-method="wechat">
        <view class="payment-info">
          <view class="payment-icon wechat-icon">
            <text class="icon-text">微</text>
          </view>
          <view class="payment-details">
            <text class="payment-name">微信支付</text>
            <text class="payment-desc">推荐使用</text>
          </view>
        </view>
        <view class="payment-radio {{selectedPayment === 'wechat' ? 'checked' : ''}}">
          <view class="radio-dot" wx:if="{{selectedPayment === 'wechat'}}"></view>
        </view>
      </view>
      
      <view class="payment-item {{selectedPayment === 'alipay' ? 'selected' : ''}}" bindtap="onPaymentSelect" data-method="alipay">
        <view class="payment-info">
          <view class="payment-icon alipay-icon">
            <text class="icon-text">支</text>
          </view>
          <view class="payment-details">
            <text class="payment-name">支付宝</text>
            <text class="payment-desc">安全便捷</text>
          </view>
        </view>
        <view class="payment-radio {{selectedPayment === 'alipay' ? 'checked' : ''}}">
          <view class="radio-dot" wx:if="{{selectedPayment === 'alipay'}}"></view>
        </view>
      </view>
      
      <view class="payment-item {{selectedPayment === 'bank' ? 'selected' : ''}}" bindtap="onPaymentSelect" data-method="bank">
        <view class="payment-info">
          <view class="payment-icon bank-icon">
            <text class="icon-text">💳</text>
          </view>
          <view class="payment-details">
            <text class="payment-name">银行卡支付</text>
            <text class="payment-desc">储蓄卡/信用卡</text>
          </view>
        </view>
        <view class="payment-radio {{selectedPayment === 'bank' ? 'checked' : ''}}">
          <view class="radio-dot" wx:if="{{selectedPayment === 'bank'}}"></view>
        </view>
      </view>

      <view class="payment-item {{selectedPayment === 'apple' ? 'selected' : ''}}" bindtap="onPaymentSelect" data-method="apple">
        <view class="payment-info">
          <view class="payment-icon apple-icon">
            <text class="icon-text">🍎</text>
          </view>
          <view class="payment-details">
            <text class="payment-name">Apple Pay</text>
            <text class="payment-desc">快速安全</text>
          </view>
        </view>
        <view class="payment-radio {{selectedPayment === 'apple' ? 'checked' : ''}}">
          <view class="radio-dot" wx:if="{{selectedPayment === 'apple'}}"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 优惠券 -->
  <view class="section">
    <view class="coupon-card" bindtap="onCouponTap">
      <view class="coupon-info">
        <text class="coupon-icon">🎫</text>
        <text class="coupon-title">优惠券</text>
        <text class="coupon-count" wx:if="{{availableCoupons.length > 0}}">{{availableCoupons.length}}张可用</text>
      </view>
      <view class="coupon-status">
        <text class="coupon-text" wx:if="{{selectedCoupon}}">已使用 ¥{{selectedCoupon.discount}} 优惠券</text>
        <text class="coupon-text" wx:else>选择优惠券</text>
        <text class="arrow-icon">></text>
      </view>
    </view>

    <!-- 优惠券列表弹窗 -->
    <view class="coupon-modal" wx:if="{{showCouponModal}}">
      <view class="modal-overlay" bindtap="onCloseCouponModal"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">选择优惠券</text>
          <text class="modal-close" bindtap="onCloseCouponModal">×</text>
        </view>
        <view class="coupon-list">
          <view class="coupon-item {{!selectedCoupon ? 'selected' : ''}}" bindtap="onSelectCoupon" data-coupon="">
            <text class="coupon-name">不使用优惠券</text>
            <text class="coupon-check" wx:if="{{!selectedCoupon}}">✓</text>
          </view>
          <view class="coupon-item {{selectedCoupon && selectedCoupon.id === item.id ? 'selected' : ''}}"
                wx:for="{{availableCoupons}}" wx:key="id" bindtap="onSelectCoupon" data-coupon="{{item}}">
            <view class="coupon-info-detail">
              <text class="coupon-name">{{item.name}}</text>
              <text class="coupon-desc">满{{item.min_amount}}元可用，有效期至{{item.expire_date}}</text>
            </view>
            <view class="coupon-amount">
              <text class="coupon-symbol">¥</text>
              <text class="coupon-value">{{item.discount}}</text>
            </view>
            <text class="coupon-check" wx:if="{{selectedCoupon && selectedCoupon.id === item.id}}">✓</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 安全保障 -->
  <view class="section">
    <view class="security-card">
      <view class="security-header">
        <text class="security-icon">🛡️</text>
        <text class="security-title">安全保障</text>
      </view>
      <view class="security-features">
        <text class="security-item">• 采用银行级SSL加密技术</text>
        <text class="security-item">• 支付信息不会被存储</text>
        <text class="security-item">• 7天无理由退保</text>
      </view>
    </view>
  </view>

  <!-- 服务协议 -->
  <view class="section">
    <view class="agreement-section">
      <view class="agreement-checkbox {{agreementChecked ? 'checked' : ''}}" bindtap="onAgreementToggle">
        <text class="checkbox-icon" wx:if="{{agreementChecked}}">✓</text>
      </view>
      <view class="agreement-text">
        <text>我已阅读并同意</text>
        <text class="agreement-link" bindtap="onAgreementTap" data-type="payment">《支付服务协议》</text>
        <text>和</text>
        <text class="agreement-link" bindtap="onAgreementTap" data-type="auto">《自动续保条款》</text>
      </view>
    </view>
  </view>

  <!-- 支付倒计时 -->
  <view class="section">
    <view class="countdown-card">
      <text class="countdown-icon">⏰</text>
      <text class="countdown-text">
        请在 <text class="countdown-time">{{countdownTime}}</text> 内完成支付
      </text>
    </view>
  </view>

  <!-- 底部支付按钮 -->
  <view class="payment-footer">
    <view class="amount-info">
      <view class="amount-left">
        <text class="amount-label">应付金额</text>
        <text class="amount-value">¥{{orderInfo.totalAmount}}</text>
      </view>
      <view class="amount-right">
        <text class="original-price">原价 ¥{{orderInfo.originalPrice}}</text>
        <text class="discount-info">已优惠 ¥{{orderInfo.discount}}</text>
      </view>
    </view>
    <button class="pay-button {{!agreementChecked ? 'disabled' : ''}}" bindtap="onPayNow" disabled="{{!agreementChecked}}">
      <text class="pay-icon">{{selectedPayment === 'wechat' ? '微' : selectedPayment === 'alipay' ? '支' : '💳'}}</text>
      <text class="pay-text">立即支付</text>
    </button>
    <text class="pay-notice">支付即表示同意相关条款，保险将在出发日期生效</text>
  </view>
</view>
