/* pages/payment/payment.wxss */
@import "../../common/placeholder.wxss";

/* 页面容器 */
.container {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 300rpx;
}

/* 进度条 */
.progress-bar {
  background: white;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.progress-steps {
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 0 0 auto;
}

.step-circle {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  background: #E5E7EB;
  color: #6B7280;
  font-weight: bold;
}

.step-item.completed .step-circle {
  background: #10B981;
  color: white;
}

.step-item.active .step-circle {
  background: #1976D2;
  color: white;
}

.step-icon {
  font-size: 24rpx;
}

.step-number {
  font-size: 24rpx;
}

.step-text {
  font-size: 20rpx;
  color: #6B7280;
}

.step-item.completed .step-text,
.step-item.active .step-text {
  color: #333;
}

.progress-line {
  flex: 1;
  height: 4rpx;
  background: #E5E7EB;
  margin: 0 20rpx;
  max-width: 120rpx;
}

.progress-line.completed {
  background: #10B981;
}

.progress-line.active {
  background: #1976D2;
}

/* 通用样式 */
.section {
  margin: 32rpx;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 订单信息 */
.order-card {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.product-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.product-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.product-tag {
  background: #1976D2;
  color: white;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
}

.order-details {
  background: #F8FAFC;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 24rpx;
  color: #666;
}

.detail-value {
  font-size: 24rpx;
  color: #333;
}

/* 费用明细 */
.cost-breakdown {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 24rpx;
}

.breakdown-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.cost-item:last-child {
  margin-bottom: 0;
}

.cost-label {
  font-size: 24rpx;
  color: #666;
}

.cost-value {
  font-size: 24rpx;
  color: #333;
}

.cost-item.discount .cost-value {
  color: #10B981;
}

.cost-item.total {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 16rpx;
  margin-top: 16rpx;
}

.cost-item.total .cost-label,
.cost-item.total .cost-value {
  font-size: 32rpx;
  font-weight: bold;
}

.cost-item.total .cost-value {
  color: #1976D2;
}

/* 支付方式 */
.payment-methods {
  background: white;
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.payment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s;
}

.payment-item:last-child {
  border-bottom: none;
}

.payment-item.selected {
  background: #EBF8FF;
  border-color: #1976D2;
}

.payment-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.payment-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}

.wechat-icon {
  background: #07C160;
}

.alipay-icon {
  background: #1677FF;
}

.bank-icon {
  background: #722ED1;
}

.payment-details {
  flex: 1;
}

.payment-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.payment-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
}

.payment-radio {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #E5E7EB;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.payment-radio.checked {
  border-color: #1976D2;
}

.radio-dot {
  width: 20rpx;
  height: 20rpx;
  background: #1976D2;
  border-radius: 50%;
}

/* 优惠券 */
.coupon-card {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.coupon-info {
  display: flex;
  align-items: center;
}

.coupon-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.coupon-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.coupon-count {
  font-size: 20rpx;
  color: #10b981;
  background: #f0fdf4;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-left: 16rpx;
}

.coupon-status {
  display: flex;
  align-items: center;
}

.coupon-text {
  font-size: 24rpx;
  color: #10B981;
  margin-right: 12rpx;
}

.arrow-icon {
  font-size: 24rpx;
  color: #999;
}

/* 优惠券弹窗 */
.coupon-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
}

.coupon-list {
  max-height: 60vh;
  overflow-y: auto;
  padding: 16rpx 0;
}

.coupon-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f8f8f8;
  position: relative;
}

.coupon-item.selected {
  background: #f0f9ff;
}

.coupon-info-detail {
  flex: 1;
}

.coupon-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.coupon-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

.coupon-amount {
  display: flex;
  align-items: baseline;
  margin-right: 32rpx;
}

.coupon-symbol {
  font-size: 24rpx;
  color: #ef4444;
  font-weight: bold;
}

.coupon-value {
  font-size: 48rpx;
  color: #ef4444;
  font-weight: bold;
  line-height: 1;
}

.coupon-check {
  font-size: 32rpx;
  color: #10b981;
  position: absolute;
  right: 32rpx;
}

/* 安全保障 */
.security-card {
  background: #F0FDF4;
  border: 2rpx solid #10B981;
  border-radius: 32rpx;
  padding: 32rpx;
}

.security-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.security-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.security-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #065F46;
}

.security-features {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.security-item {
  font-size: 24rpx;
  color: #065F46;
  line-height: 1.5;
}

/* 服务协议 */
.agreement-section {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.agreement-checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #E5E7EB;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.agreement-checkbox.checked {
  background: #1976D2;
  border-color: #1976D2;
}

.checkbox-icon {
  font-size: 24rpx;
  color: white;
}

.agreement-text {
  flex: 1;
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
}

.agreement-link {
  color: #1976D2;
  text-decoration: underline;
}

/* 支付倒计时 */
.countdown-card {
  background: #FEF3C7;
  border: 2rpx solid #F59E0B;
  border-radius: 32rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.countdown-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.countdown-text {
  font-size: 24rpx;
  color: #92400E;
}

.countdown-time {
  font-weight: bold;
  color: #DC2626;
}

/* 底部支付按钮 */
.payment-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1rpx solid #E5E7EB;
  padding: 32rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  z-index: 100;
}

.amount-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.amount-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.amount-value {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #1976D2;
}

.amount-right {
  text-align: right;
}

.original-price {
  display: block;
  font-size: 20rpx;
  color: #999;
  text-decoration: line-through;
  margin-bottom: 4rpx;
}

.discount-info {
  display: block;
  font-size: 24rpx;
  color: #10B981;
}

.pay-button {
  width: 100%;
  background: #10B981;
  color: white;
  border: none;
  border-radius: 32rpx;
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.pay-button.disabled {
  background: #E5E7EB;
  color: #9CA3AF;
}

.pay-icon {
  margin-right: 12rpx;
  font-size: 28rpx;
}

.pay-notice {
  text-align: center;
  font-size: 20rpx;
  color: #666;
  line-height: 1.5;
}
