<!--pages/home/<USER>
<view class="container">
  <!-- 蓝色渐变头部区域 - 完全按照原型图设计 -->
  <view class="gradient-header">
    <!-- 标题区域 -->
    <view class="header-title-section">
      <text class="main-title">香港保险专家</text>
      <text class="main-subtitle">专业的香港保险配置服务</text>
    </view>

    <!-- 轮播图区域（在头部内，半透明白色背景） -->
    <view class="banner-in-header">
      <swiper class="header-banner-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{5000}}" duration="{{500}}">
        <swiper-item wx:for="{{banners}}" wx:key="id">
          <view class="header-banner-item" bindtap="onBannerTap" data-banner="{{item}}">
            <view class="header-banner-image-placeholder">
              <text class="header-placeholder-icon">{{item.icon || '🏦'}}</text>
            </view>
            <view class="header-banner-content">
              <text class="header-banner-title">{{item.title}}</text>
              <text class="header-banner-subtitle">{{item.subtitle}}</text>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 专业咨询表单（在头部内，白色卡片） -->
    <view class="consult-card-in-header">
      <view class="consult-header">
        <view class="consult-icon">👨‍💼</view>
        <text class="consult-title">专业咨询</text>
      </view>
      <view class="consult-form">
        <view class="form-row">
          <picker bindchange="onProductTypeChange" value="{{productTypeIndex}}" range="{{productTypes}}" range-key="name">
            <view class="picker-item">
              <text class="picker-text">{{productTypes[productTypeIndex].name}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
          <picker bindchange="onAgeRangeChange" value="{{ageRangeIndex}}" range="{{ageRanges}}">
            <view class="picker-item">
              <text class="picker-text">{{ageRanges[ageRangeIndex]}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>
        <button class="consult-btn" bindtap="onConsultNow">立即咨询方案</button>
      </view>
    </view>
  </view>

  <!-- 香港保险产品分类 - 4个图标网格 -->
  <view class="section">
    <text class="section-title">香港保险产品</text>
    <view class="categories-grid">
      <view class="category-item" bindtap="onCategoryTap" data-category-id="1" data-category-name="储蓄分红">
        <view class="category-icon-wrapper blue-bg">
          <text class="category-icon">🏦</text>
        </view>
        <text class="category-name">储蓄分红</text>
      </view>
      <view class="category-item" bindtap="onCategoryTap" data-category-id="2" data-category-name="重疾保险">
        <view class="category-icon-wrapper green-bg">
          <text class="category-icon">🛡️</text>
        </view>
        <text class="category-name">重疾保险</text>
      </view>
      <view class="category-item" bindtap="onCategoryTap" data-category-id="3" data-category-name="高端医疗">
        <view class="category-icon-wrapper purple-bg">
          <text class="category-icon">🏥</text>
        </view>
        <text class="category-name">高端医疗</text>
      </view>
      <view class="category-item" bindtap="onCategoryTap" data-category-id="4" data-category-name="投资理财">
        <view class="category-icon-wrapper red-bg">
          <text class="category-icon">📈</text>
        </view>
        <text class="category-name">投资理财</text>
      </view>
    </view>
  </view>

  <!-- 热门产品 - 垂直列表 -->
  <view class="section">
    <text class="section-title">热门产品</text>
    <view class="hot-products-list">
      <view class="hot-product-item" wx:for="{{hotProducts}}" wx:key="id" bindtap="onProductTap" data-product="{{item}}">
        <view class="product-image-wrapper">
          <view class="product-image-placeholder">
            <text class="placeholder-icon">{{item.icon}}</text>
          </view>
        </view>
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <text class="product-desc">{{item.description}}</text>
        </view>
        <view class="product-highlight">
          <text class="highlight-value">{{item.highlight_value}}</text>
          <text class="highlight-label">{{item.highlight_label}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 香港保险优势 - 2x2网格 -->
  <view class="section">
    <text class="section-title">香港保险优势</text>
    <view class="advantages-grid">
      <view class="advantage-item" wx:for="{{advantages}}" wx:key="id">
        <view class="advantage-icon {{item.color}}">
          <text class="icon-text">{{item.icon}}</text>
        </view>
        <text class="advantage-title">{{item.title}}</text>
        <text class="advantage-desc">{{item.description}}</text>
      </view>
    </view>
  </view>

  <!-- 底部安全间距 -->
  <view class="bottom-safe-area"></view>
</view>
