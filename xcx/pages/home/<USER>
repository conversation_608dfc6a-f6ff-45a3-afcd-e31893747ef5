// pages/home/<USER>
const app = getApp()

Page({
  data: {
    // 搜索相关
    showSearch: false,
    searchKeyword: '',
    searchFocus: false,
    searchSuggestions: [],
    hotSearches: ['储蓄分红', '重疾保险', '高端医疗', '人寿保险', '万能险'],

    // 热门产品
    hotProducts: [
      {
        id: 1,
        name: '储蓄分红保险',
        description: '长期储蓄，稳健增值',
        icon: '🏦',
        image: '',
        highlight_value: '6.5%',
        highlight_label: '预期年化'
      },
      {
        id: 2,
        name: '重疾保险',
        description: '保障范围广，保费低',
        icon: '🛡️',
        image: '',
        highlight_value: '100+',
        highlight_label: '疾病种类'
      },
      {
        id: 3,
        name: '高端医疗保险',
        description: '全球医疗网络',
        icon: '🏥',
        image: '',
        highlight_value: '全球',
        highlight_label: '医疗保障'
      }
    ],

    // 香港保险优势
    advantages: [
      {
        id: 1,
        title: '收益率高',
        description: '6%-7%年化',
        icon: '📈',
        color: 'blue-bg'
      },
      {
        id: 2,
        title: '全球配置',
        description: '美元资产',
        icon: '🌍',
        color: 'green-bg'
      },
      {
        id: 3,
        title: '专业服务',
        description: '持牌顾问',
        icon: '👨‍💼',
        color: 'purple-bg'
      },
      {
        id: 4,
        title: '法律保障',
        description: '香港法律',
        icon: '⚖️',
        color: 'orange-bg'
      }
    ],

    banners: [
      {
        id: 1,
        title: '储蓄分红保险',
        subtitle: '预期年化收益6%-7%',
        image: '', // 使用占位符
        link_type: 'category',
        link_value: 'savings'
      },
      {
        id: 2,
        title: '重疾保险优惠',
        subtitle: '保障100+种疾病',
        image: '', // 使用占位符
        link_type: 'category',
        link_value: 'critical'
      },
      {
        id: 3,
        title: '高端医疗保险',
        subtitle: '全球医疗网络',
        image: '', // 使用占位符
        link_type: 'category',
        link_value: 'medical'
      }
    ],
    categories: [
      {
        id: 1,
        name: '储蓄分红',
        emoji: '🏦',
        color: '#1976D2'
      },
      {
        id: 2,
        name: '重疾保险',
        emoji: '🛡️',
        color: '#10B981'
      },
      {
        id: 3,
        name: '高端医疗',
        emoji: '🏥',
        color: '#7C3AED'
      },
      {
        id: 4,
        name: '投资理财',
        emoji: '📈',
        color: '#DC2626'
      }
    ],
    hotProducts: [
      {
        id: 1,
        name: '储蓄分红保险',
        subtitle: '长期储蓄，稳健增值',
        description: '长期储蓄，稳健增值',
        expected_return: 6.5,
        category_name: '储蓄分红险',
        main_image: ''
      },
      {
        id: 2,
        name: '重疾保险',
        subtitle: '保障范围广，保费低',
        description: '保障100+种疾病，含早期疾病保障',
        expected_return: null,
        category_name: '重疾保险',
        main_image: ''
      },
      {
        id: 3,
        name: '高端医疗保险',
        subtitle: '全球医疗网络，免垫付直付服务',
        description: '全球医疗保障，年度保障无限额',
        expected_return: null,
        category_name: '高端医疗',
        main_image: ''
      }
    ],
    featuredProducts: [],
    statistics: {},
    
    // 快速咨询表单
    productTypes: [
      { name: '选择产品类型', value: '' },
      { name: '储蓄分红险', value: 'savings' },
      { name: '重疾险', value: 'critical' },
      { name: '高端医疗', value: 'medical' },
      { name: '人寿保险', value: 'life' }
    ],
    productTypeIndex: 0,
    
    ageRanges: ['选择年龄范围', '18-30岁', '31-45岁', '46-60岁', '60岁以上'],
    ageRangeIndex: 0,

    loading: false,
    error: ''
  },

  onLoad() {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '香港保险专家'
    })
    this.loadHomeData()
  },

  onShow() {
    // 页面显示时刷新数据
    if (this.data.banners.length === 0 || this.data.error) {
      this.loadHomeData()
    }
  },

  onReady() {
    // 页面渲染完成后的操作
    console.log('首页渲染完成')
  },

  onPullDownRefresh() {
    this.loadHomeData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 加载首页数据
  async loadHomeData() {
    if (this.data.loading) return
    
    this.setData({ loading: true })
    app.showLoading('加载中...')

    try {
      const data = await app.request({
        url: '/insurance/api/home/'
      })

      this.setData({
        banners: data.banners && data.banners.length > 0 ? data.banners : this.data.banners,
        categories: data.categories || this.data.categories,
        hotProducts: data.hot_products && data.hot_products.length > 0 ? data.hot_products : this.data.hotProducts,
        featuredProducts: data.featured_products || [],
        statistics: data.statistics || {}
      })
    } catch (error) {
      console.error('加载首页数据失败:', error)
      this.setData({
        error: '网络连接失败，请检查网络后重试'
      })
      app.showToast('加载失败，请重试')
    } finally {
      this.setData({ loading: false })
      app.hideLoading()
    }
  },



  // 产品类型选择
  onProductTypeChange(e) {
    this.setData({
      productTypeIndex: parseInt(e.detail.value)
    })
  },

  // 年龄范围选择
  onAgeRangeChange(e) {
    this.setData({
      ageRangeIndex: parseInt(e.detail.value)
    })
  },

  // 立即咨询
  onConsultTap() {
    const { productTypeIndex, ageRangeIndex } = this.data

    if (productTypeIndex === 0) {
      app.showToast('请选择产品类型')
      return
    }

    if (ageRangeIndex === 0) {
      app.showToast('请选择年龄范围')
      return
    }

    // 跳转到咨询页面
    const productType = this.data.productTypes[productTypeIndex].value
    const ageRange = this.data.ageRanges[ageRangeIndex]

    wx.navigateTo({
      url: `/pages/services/services?type=consult&productType=${productType}&ageRange=${encodeURIComponent(ageRange)}`
    })
  },

  // 立即咨询方案 (新增)
  onConsultNow() {
    const { productTypeIndex, ageRangeIndex } = this.data

    if (productTypeIndex === 0) {
      app.showToast('请选择产品类型')
      return
    }

    if (ageRangeIndex === 0) {
      app.showToast('请选择年龄范围')
      return
    }

    // 跳转到咨询页面
    const productType = this.data.productTypes[productTypeIndex].value
    const ageRange = this.data.ageRanges[ageRangeIndex]

    wx.navigateTo({
      url: `/pages/services/services?type=consult&productType=${productType}&ageRange=${encodeURIComponent(ageRange)}`
    })
  },

  // 优势详情点击
  onAdvantageDetail(e) {
    const type = e.currentTarget.dataset.type
    let title = ''
    let content = ''

    switch (type) {
      case 'yield':
        title = '收益率高'
        content = '香港保险产品预期年化收益率可达6%-7%，远超内地同类产品'
        break
      case 'global':
        title = '全球配置'
        content = '美元资产配置，有效分散风险，实现全球资产配置'
        break
      case 'service':
        title = '专业服务'
        content = '持牌专业顾问提供一对一服务，全程跟踪保障'
        break
      case 'legal':
        title = '法律保障'
        content = '受香港法律保护，监管严格，保障客户权益'
        break
    }

    wx.showModal({
      title: title,
      content: content,
      showCancel: false,
      confirmText: '我知道了'
    })
  },

  // 分类点击
  onCategoryTap(e) {
    const { categoryId, categoryName } = e.currentTarget.dataset
    if (!categoryId || !categoryName) return

    wx.navigateTo({
      url: `/pages/products/products?categoryId=${categoryId}&categoryName=${encodeURIComponent(categoryName)}`
    })
  },

  // 产品点击
  onProductTap(e) {
    const product = e.currentTarget.dataset.product
    if (!product) return

    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${product.id}`
    })
  },

  // 查看更多产品
  onViewMoreProducts() {
    wx.switchTab({
      url: '/pages/products/products'
    })
  },

  // 错误重试
  onRetry() {
    this.setData({ error: '' })
    this.loadHomeData()
  },

  // 搜索功能
  onSearchTap() {
    wx.navigateTo({
      url: '/pages/products/products?focus=true'
    })
  },

  // 轮播图点击事件处理
  onBannerTap(e) {
    const banner = e.currentTarget.dataset.banner
    if (!banner) return

    switch (banner.link_type) {
      case 'product':
        wx.navigateTo({
          url: `/pages/product-detail/product-detail?id=${banner.link_value}`
        })
        break
      case 'category':
        wx.navigateTo({
          url: `/pages/products/products?category=${banner.link_value}`
        })
        break
      case 'url':
        // 外部链接处理
        wx.showModal({
          title: '提示',
          content: '即将跳转到外部链接',
          success: (res) => {
            if (res.confirm) {
              console.log('跳转到:', banner.link_value)
            }
          }
        })
        break
    }
  },

  // 热门产品点击
  onProductTap(e) {
    const product = e.currentTarget.dataset.product
    console.log('点击热门产品:', product)

    // 跳转到产品详情页
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${product.id}`
    })
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '香港保险专家 - 专业的香港保险配置服务',
      path: '/pages/home/<USER>',
      imageUrl: '/images/share-banner.jpg'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '香港保险专家 - 专业的香港保险配置服务',
      imageUrl: '/images/share-banner.jpg'
    }
  },

  // 搜索相关方法
  onSearchTap() {
    this.setData({
      showSearch: true,
      searchFocus: true
    })
  },

  onSearchCancel() {
    this.setData({
      showSearch: false,
      searchKeyword: '',
      searchSuggestions: [],
      searchFocus: false
    })
  },

  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({
      searchKeyword: keyword
    })

    // 实时搜索建议
    if (keyword.trim()) {
      this.getSearchSuggestions(keyword)
    } else {
      this.setData({
        searchSuggestions: []
      })
    }
  },

  onSearchConfirm(e) {
    const keyword = e.detail.value.trim()
    if (keyword) {
      this.performSearch(keyword)
    }
  },

  onSuggestionTap(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.performSearch(keyword)
  },

  onHotSearchTap(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.performSearch(keyword)
  },

  // 执行搜索
  performSearch(keyword) {
    // 保存搜索历史
    this.saveSearchHistory(keyword)

    // 跳转到产品页面并传递搜索关键词
    wx.navigateTo({
      url: `/pages/products/products?search=${encodeURIComponent(keyword)}`
    })

    // 重置搜索状态
    this.setData({
      showSearch: false,
      searchKeyword: '',
      searchSuggestions: []
    })
  },

  // 获取搜索建议
  getSearchSuggestions(keyword) {
    // 模拟搜索建议数据
    const allSuggestions = [
      '储蓄分红保险', '重疾保险', '高端医疗保险', '人寿保险', '万能险',
      '友邦保险', '保诚保险', '宏利保险', '中银人寿',
      '年金保险', '教育金', '养老保险', '意外险'
    ]

    const suggestions = allSuggestions.filter(item =>
      item.toLowerCase().includes(keyword.toLowerCase())
    ).slice(0, 5)

    this.setData({
      searchSuggestions: suggestions
    })
  },

  // 保存搜索历史
  saveSearchHistory(keyword) {
    try {
      let history = wx.getStorageSync('search_history') || []

      // 移除重复项
      history = history.filter(item => item !== keyword)

      // 添加到开头
      history.unshift(keyword)

      // 限制历史记录数量
      if (history.length > 10) {
        history = history.slice(0, 10)
      }

      wx.setStorageSync('search_history', history)
    } catch (e) {
      console.error('保存搜索历史失败:', e)
    }
  }
})
