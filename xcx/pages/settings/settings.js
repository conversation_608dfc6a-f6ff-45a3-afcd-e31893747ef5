// pages/settings/settings.js
const app = getApp()

Page({
  data: {
    userInfo: {},
    notificationSettings: {
      push: true,
      email: false,
      sms: true
    },
    languageOptions: ['简体中文', 'English', '繁體中文'],
    currentLanguage: 0,
    showLanguageModal: false,
    currencyOptions: ['人民币 (CNY)', '港币 (HKD)', '美元 (USD)'],
    currentCurrency: 1,
    showCurrencyModal: false,
    cacheSize: '12.5MB',
    appVersion: '1.0.0'
  },

  onLoad() {
    this.loadUserInfo()
    this.loadSettings()
    this.calculateCacheSize()
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = app.globalData.userInfo || wx.getStorageSync('userInfo') || {}
    this.setData({ userInfo })
  },

  // 加载设置
  loadSettings() {
    const settings = wx.getStorageSync('appSettings') || {}
    this.setData({
      notificationSettings: settings.notifications || this.data.notificationSettings,
      currentLanguage: settings.language || 0,
      currentCurrency: settings.currency || 1
    })
  },

  // 保存设置
  saveSettings() {
    const settings = {
      notifications: this.data.notificationSettings,
      language: this.data.currentLanguage,
      currency: this.data.currentCurrency
    }
    wx.setStorageSync('appSettings', settings)
  },

  // 计算缓存大小
  calculateCacheSize() {
    // 模拟计算缓存大小
    const sizes = ['8.2MB', '12.5MB', '15.8MB', '20.1MB']
    const randomSize = sizes[Math.floor(Math.random() * sizes.length)]
    this.setData({ cacheSize: randomSize })
  },

  // 个人信息
  onPersonalInfo() {
    wx.showToast({
      title: '个人信息页面开发中...',
      icon: 'none'
    })
  },

  // 安全设置
  onSecuritySettings() {
    wx.showToast({
      title: '安全设置页面开发中...',
      icon: 'none'
    })
  },

  // 支付设置
  onPaymentSettings() {
    wx.showToast({
      title: '支付设置页面开发中...',
      icon: 'none'
    })
  },

  // 推送通知开关
  onPushChange(e) {
    this.setData({
      'notificationSettings.push': e.detail.value
    })
    this.saveSettings()
  },

  // 邮件通知开关
  onEmailChange(e) {
    this.setData({
      'notificationSettings.email': e.detail.value
    })
    this.saveSettings()
  },

  // 短信通知开关
  onSmsChange(e) {
    this.setData({
      'notificationSettings.sms': e.detail.value
    })
    this.saveSettings()
  },

  // 语言设置
  onLanguageSettings() {
    this.setData({ showLanguageModal: true })
  },

  onCloseLanguageModal() {
    this.setData({ showLanguageModal: false })
  },

  onSelectLanguage(e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      currentLanguage: index,
      showLanguageModal: false
    })
    this.saveSettings()
    
    wx.showToast({
      title: `已切换到${this.data.languageOptions[index]}`,
      icon: 'success'
    })
  },

  // 货币设置
  onCurrencySettings() {
    this.setData({ showCurrencyModal: true })
  },

  onCloseCurrencyModal() {
    this.setData({ showCurrencyModal: false })
  },

  onSelectCurrency(e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      currentCurrency: index,
      showCurrencyModal: false
    })
    this.saveSettings()
    
    wx.showToast({
      title: `已切换到${this.data.currencyOptions[index]}`,
      icon: 'success'
    })
  },

  // 清除缓存
  onClearCache() {
    wx.showModal({
      title: '清除缓存',
      content: `确定要清除 ${this.data.cacheSize} 的缓存数据吗？`,
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '清除中...'
          })
          
          // 模拟清除缓存
          setTimeout(() => {
            wx.hideLoading()
            this.setData({ cacheSize: '0MB' })
            wx.showToast({
              title: '缓存已清除',
              icon: 'success'
            })
            
            // 重新计算缓存大小
            setTimeout(() => {
              this.calculateCacheSize()
            }, 2000)
          }, 1500)
        }
      }
    })
  },

  // 帮助中心
  onHelp() {
    wx.showToast({
      title: '帮助中心页面开发中...',
      icon: 'none'
    })
  },

  // 意见反馈
  onFeedback() {
    wx.showToast({
      title: '意见反馈页面开发中...',
      icon: 'none'
    })
  },

  // 关于我们
  onAbout() {
    wx.showModal({
      title: '关于我们',
      content: `香港保险专家 v${this.data.appVersion}\n\n专业的香港保险配置服务平台，为您提供最优质的保险产品和服务。`,
      showCancel: false
    })
  },

  // 退出登录
  onLogout() {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除登录状态
          app.globalData.isLoggedIn = false
          app.globalData.userInfo = {}
          wx.removeStorageSync('userInfo')
          wx.removeStorageSync('isLoggedIn')
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
          
          // 跳转到登录页面
          setTimeout(() => {
            wx.redirectTo({
              url: '/pages/login/login'
            })
          }, 1500)
        }
      }
    })
  }
})
