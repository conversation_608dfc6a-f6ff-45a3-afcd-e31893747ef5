/* pages/settings/settings.wxss */

/* 页面容器 */
.container {
  background: #F5F5F5;
  min-height: 100vh;
  padding-bottom: calc(env(safe-area-inset-bottom) + 100rpx);
}

/* 区块样式 */
.section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 28rpx;
  color: #6B7280;
  padding: 32rpx 32rpx 16rpx;
  display: block;
}

/* 设置列表 */
.settings-list {
  background: white;
  border-radius: 24rpx;
  margin: 0 32rpx;
  overflow: hidden;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
  transition: background-color 0.3s ease;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background-color: #F9FAFB;
}

.item-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.item-icon {
  width: 64rpx;
  height: 64rpx;
  background: #F3F4F6;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 24rpx;
}

.item-title {
  font-size: 32rpx;
  color: #1F2937;
  font-weight: 500;
}

.item-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.item-value {
  font-size: 28rpx;
  color: #6B7280;
}

.arrow-icon {
  font-size: 28rpx;
  color: #9CA3AF;
}

/* 退出登录按钮 */
.logout-btn {
  background: #FEE2E2;
  color: #DC2626;
  border: none;
  border-radius: 24rpx;
  padding: 32rpx;
  margin: 0 32rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.logout-btn:active {
  background: #FECACA;
  transform: scale(0.98);
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: 32rpx;
  margin: 0 48rpx;
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.modal-overlay.show .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1F2937;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  background: #F3F4F6;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #6B7280;
}

.modal-body {
  max-height: 60vh;
  overflow-y: auto;
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
  transition: background-color 0.3s ease;
}

.option-item:last-child {
  border-bottom: none;
}

.option-item:active {
  background-color: #F9FAFB;
}

.option-item.selected {
  background-color: #EBF8FF;
}

.option-text {
  font-size: 32rpx;
  color: #1F2937;
}

.option-item.selected .option-text {
  color: #1976D2;
  font-weight: 600;
}

.option-check {
  font-size: 28rpx;
  color: #1976D2;
  font-weight: bold;
}

/* 开关样式优化 */
switch {
  transform: scale(0.8);
}
