<!--pages/settings/settings.wxml-->
<view class="container">
  <!-- 账户设置 -->
  <view class="section">
    <text class="section-title">账户设置</text>
    <view class="settings-list">
      <view class="setting-item" bindtap="onPersonalInfo">
        <view class="item-left">
          <view class="item-icon">👤</view>
          <text class="item-title">个人信息</text>
        </view>
        <view class="item-right">
          <text class="item-value">{{userInfo.nickname || '未设置'}}</text>
          <text class="arrow-icon">></text>
        </view>
      </view>

      <view class="setting-item" bindtap="onSecuritySettings">
        <view class="item-left">
          <view class="item-icon">🔒</view>
          <text class="item-title">安全设置</text>
        </view>
        <view class="item-right">
          <text class="item-value">密码、验证</text>
          <text class="arrow-icon">></text>
        </view>
      </view>

      <view class="setting-item" bindtap="onPaymentSettings">
        <view class="item-left">
          <view class="item-icon">💳</view>
          <text class="item-title">支付设置</text>
        </view>
        <view class="item-right">
          <text class="item-value">支付方式</text>
          <text class="arrow-icon">></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 通知设置 -->
  <view class="section">
    <text class="section-title">通知设置</text>
    <view class="settings-list">
      <view class="setting-item">
        <view class="item-left">
          <view class="item-icon">🔔</view>
          <text class="item-title">推送通知</text>
        </view>
        <view class="item-right">
          <switch checked="{{notificationSettings.push}}" bindchange="onPushChange" color="#1976D2" />
        </view>
      </view>

      <view class="setting-item">
        <view class="item-left">
          <view class="item-icon">📧</view>
          <text class="item-title">邮件通知</text>
        </view>
        <view class="item-right">
          <switch checked="{{notificationSettings.email}}" bindchange="onEmailChange" color="#1976D2" />
        </view>
      </view>

      <view class="setting-item">
        <view class="item-left">
          <view class="item-icon">📱</view>
          <text class="item-title">短信通知</text>
        </view>
        <view class="item-right">
          <switch checked="{{notificationSettings.sms}}" bindchange="onSmsChange" color="#1976D2" />
        </view>
      </view>
    </view>
  </view>

  <!-- 应用设置 -->
  <view class="section">
    <text class="section-title">应用设置</text>
    <view class="settings-list">
      <view class="setting-item" bindtap="onLanguageSettings">
        <view class="item-left">
          <view class="item-icon">🌐</view>
          <text class="item-title">语言设置</text>
        </view>
        <view class="item-right">
          <text class="item-value">{{languageOptions[currentLanguage]}}</text>
          <text class="arrow-icon">></text>
        </view>
      </view>

      <view class="setting-item" bindtap="onCurrencySettings">
        <view class="item-left">
          <view class="item-icon">💰</view>
          <text class="item-title">货币设置</text>
        </view>
        <view class="item-right">
          <text class="item-value">{{currencyOptions[currentCurrency]}}</text>
          <text class="arrow-icon">></text>
        </view>
      </view>

      <view class="setting-item" bindtap="onClearCache">
        <view class="item-left">
          <view class="item-icon">🗑️</view>
          <text class="item-title">清除缓存</text>
        </view>
        <view class="item-right">
          <text class="item-value">{{cacheSize}}</text>
          <text class="arrow-icon">></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 帮助与反馈 -->
  <view class="section">
    <text class="section-title">帮助与反馈</text>
    <view class="settings-list">
      <view class="setting-item" bindtap="onHelp">
        <view class="item-left">
          <view class="item-icon">❓</view>
          <text class="item-title">帮助中心</text>
        </view>
        <view class="item-right">
          <text class="arrow-icon">></text>
        </view>
      </view>

      <view class="setting-item" bindtap="onFeedback">
        <view class="item-left">
          <view class="item-icon">💬</view>
          <text class="item-title">意见反馈</text>
        </view>
        <view class="item-right">
          <text class="arrow-icon">></text>
        </view>
      </view>

      <view class="setting-item" bindtap="onAbout">
        <view class="item-left">
          <view class="item-icon">ℹ️</view>
          <text class="item-title">关于我们</text>
        </view>
        <view class="item-right">
          <text class="item-value">v{{appVersion}}</text>
          <text class="arrow-icon">></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="section">
    <button class="logout-btn" bindtap="onLogout">退出登录</button>
  </view>

  <!-- 底部安全间距 -->
  <view class="bottom-safe-area"></view>
</view>

<!-- 语言选择弹窗 -->
<view class="modal-overlay {{showLanguageModal ? 'show' : ''}}" bindtap="onCloseLanguageModal">
  <view class="modal-content" catchtap="">
    <view class="modal-header">
      <text class="modal-title">选择语言</text>
      <button class="modal-close" bindtap="onCloseLanguageModal">✕</button>
    </view>
    <view class="modal-body">
      <view class="option-item {{currentLanguage === index ? 'selected' : ''}}"
            wx:for="{{languageOptions}}" wx:key="*this" wx:for-index="index"
            bindtap="onSelectLanguage" data-index="{{index}}">
        <text class="option-text">{{item}}</text>
        <view class="option-check" wx:if="{{currentLanguage === index}}">✓</view>
      </view>
    </view>
  </view>
</view>

<!-- 货币选择弹窗 -->
<view class="modal-overlay {{showCurrencyModal ? 'show' : ''}}" bindtap="onCloseCurrencyModal">
  <view class="modal-content" catchtap="">
    <view class="modal-header">
      <text class="modal-title">选择货币</text>
      <button class="modal-close" bindtap="onCloseCurrencyModal">✕</button>
    </view>
    <view class="modal-body">
      <view class="option-item {{currentCurrency === index ? 'selected' : ''}}"
            wx:for="{{currencyOptions}}" wx:key="*this" wx:for-index="index"
            bindtap="onSelectCurrency" data-index="{{index}}">
        <text class="option-text">{{item}}</text>
        <view class="option-check" wx:if="{{currentCurrency === index}}">✓</view>
      </view>
    </view>
  </view>
</view>
