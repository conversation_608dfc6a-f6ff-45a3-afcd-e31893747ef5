// pages/products/products.js
const app = getApp()

Page({
  data: {
    products: [],
    currentFilter: 'all',
    sortBy: 'default',
    sortText: '默认排序',
    showSortModal: false,

    // 高级筛选
    showFilterModal: false,
    filterOptions: {
      minPrice: '',
      maxPrice: '',
      priceRange: '',
      companies: [],
      coveragePeriods: [],
      features: []
    },

    // 搜索功能
    searchKeyword: '',
    searchSuggestions: [],
    searchHistory: [],
    showSearchHistory: false,
    totalCount: 126,

    // 分页
    page: 1,
    hasMore: true,
    loading: false,

    // 筛选参数
    categoryId: '',
    categoryName: '',

    // 排序选项
    sortOptions: [
      { id: 'default', name: '默认排序', ordering: '-is_featured,-is_hot,sort_order' },
      { id: 'return', name: '收益率从高到低', ordering: '-expected_return' },
      { id: 'premium', name: '保费从低到高', ordering: 'min_premium' },
      { id: 'popular', name: '热度从高到低', ordering: '-view_count' }
    ],
    currentSortIndex: 0,

    // 筛选选项
    companyOptions: [
      { label: '友邦保险', value: 'aia' },
      { label: '保诚保险', value: 'prudential' },
      { label: '宏利保险', value: 'manulife' },
      { label: '安盛保险', value: 'axa' },
      { label: '富通保险', value: 'ftlife' }
    ],
    coveragePeriodOptions: [
      { label: '短期(1年以下)', value: 'short' },
      { label: '中期(1-5年)', value: 'medium' },
      { label: '长期(5-10年)', value: 'long' },
      { label: '终身', value: 'lifetime' }
    ],
    featureOptions: [
      { label: '保证续保', value: 'guaranteed_renewal' },
      { label: '全球保障', value: 'global_coverage' },
      { label: '免体检', value: 'no_medical_exam' },
      { label: '快速理赔', value: 'fast_claim' },
      { label: '分红功能', value: 'dividend' }
    ]
  },

  onLoad(options) {
    // 获取页面参数
    if (options.categoryId) {
      this.setData({
        categoryId: options.categoryId,
        categoryName: decodeURIComponent(options.categoryName || ''),
        currentFilter: options.categoryId
      })
    }

    if (options.category) {
      this.setData({
        currentFilter: options.category
      })
    }

    // 处理搜索参数
    if (options.search) {
      const searchKeyword = decodeURIComponent(options.search)
      this.setData({
        searchKeyword: searchKeyword,
        showSearchHistory: false
      })
    }

    // 检查是否需要自动聚焦搜索框
    if (options.focus === 'true') {
      this.setData({ autoFocusSearch: true })
    }

    // 加载搜索历史
    this.loadSearchHistory()

    this.loadProducts(true)
  },

  onReady() {
    // 自动聚焦搜索框
    if (this.data.autoFocusSearch) {
      setTimeout(() => {
        this.setData({ showSearchHistory: true })
        this.loadSearchHistory()
      }, 300)
    }
  },

  onShow() {
    // 页面显示时刷新数据
    if (this.data.products.length === 0) {
      this.loadProducts(true)
    }
  },

  onReachBottom() {
    // 触底加载更多
    if (this.data.hasMore && !this.data.loading) {
      this.loadProducts(false)
    }
  },

  onPullDownRefresh() {
    this.loadProducts(true).finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 加载产品列表
  async loadProducts(reset = false, extraParams = {}) {
    if (this.data.loading) return

    this.setData({ loading: true })

    if (reset) {
      this.setData({
        page: 1,
        products: [],
        hasMore: true
      })
    }

    try {
      const params = {
        page: this.data.page,
        page_size: 10,
        ...extraParams
      }

      // 添加搜索关键词
      if (this.data.searchKeyword) {
        params.search = this.data.searchKeyword
      }

      // 添加筛选条件
      if (this.data.currentFilter !== 'all') {
        if (this.data.categoryId) {
          params.category = this.data.categoryId
        } else {
          // 根据筛选类型设置参数
          switch (this.data.currentFilter) {
            case 'savings':
              params.category__name = '储蓄分红险'
              break
            case 'critical':
              params.category__name = '重疾保险'
              break
            case 'medical':
              params.category__name = '高端医疗'
              break
            case 'life':
              params.category__name = '人寿保险'
              break
          }
        }
      }

      // 添加排序
      if (this.data.sortBy !== 'default') {
        params.ordering = this.data.sortOptions[this.data.sortBy].ordering
      }

      // 添加高级筛选参数
      const { filterOptions } = this.data

      // 价格区间
      if (filterOptions.minPrice) {
        params.min_premium__gte = filterOptions.minPrice
      }
      if (filterOptions.maxPrice) {
        params.min_premium__lte = filterOptions.maxPrice
      }

      // 保险公司
      if (filterOptions.companies.length > 0) {
        params.company__in = filterOptions.companies.join(',')
      }

      // 保障期限
      if (filterOptions.coveragePeriods.length > 0) {
        params.coverage_period__in = filterOptions.coveragePeriods.join(',')
      }

      // 特色功能
      if (filterOptions.features.length > 0) {
        params.features__in = filterOptions.features.join(',')
      }

      const response = await app.request({
        url: '/insurance/api/products/',
        data: params
      })

      const newProducts = (response.results || []).map(product => {
        // 为每个产品添加标签
        const tags = []

        // 根据产品特性添加标签
        if (product.is_hot) {
          tags.push({ type: 'hot', text: '热销' })
        }
        if (product.is_featured) {
          tags.push({ type: 'featured', text: '推荐' })
        }
        if (product.expected_return && product.expected_return >= 6) {
          tags.push({ type: 'high-return', text: '高收益' })
        }
        if (product.category_name === '储蓄分红险') {
          tags.push({ type: 'savings', text: '储蓄分红' })
        } else if (product.category_name === '重疾保险') {
          tags.push({ type: 'critical', text: '重疾保险' })
        } else if (product.category_name === '高端医疗') {
          tags.push({ type: 'medical', text: '医疗保险' })
        } else if (product.category_name === '人寿保险') {
          tags.push({ type: 'life', text: '人寿保险' })
        }

        return {
          ...product,
          tags: tags
        }
      })

      const products = reset ? newProducts : [...this.data.products, ...newProducts]

      this.setData({
        products: products,
        hasMore: !!response.next,
        page: this.data.page + 1
      })

    } catch (error) {
      console.error('加载产品列表失败:', error)
      app.showToast('加载失败，请重试')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 筛选点击
  onFilterTap(e) {
    const filter = e.currentTarget.dataset.filter
    if (filter === this.data.currentFilter) return

    this.setData({
      currentFilter: filter,
      categoryId: '', // 清除分类ID
      categoryName: ''
    })

    this.loadProducts(true)
  },

  // 排序点击
  onSortTap() {
    this.setData({
      showSortModal: true
    })
  },

  // 关闭排序弹窗
  closeSortModal() {
    this.setData({
      showSortModal: false
    })
  },

  // 选择排序方式
  onSortSelect(e) {
    const sort = e.currentTarget.dataset.sort
    const sortOption = this.data.sortOptions[sort]

    this.setData({
      sortBy: sort,
      sortText: sortOption.text,
      showSortModal: false
    })

    this.loadProducts(true)
  },



  // 产品点击
  onProductTap(e) {
    const product = e.currentTarget.dataset.product
    if (!product) return

    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${product.id}`
    })
  },

  // 产品详情点击
  onProductDetailTap(e) {
    e.stopPropagation() // 阻止事件冒泡
    const product = e.currentTarget.dataset.product
    if (!product) return

    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${product.id}`
    })
  },

  // 咨询点击
  onConsultTap(e) {
    e.stopPropagation() // 阻止事件冒泡
    const product = e.currentTarget.dataset.product
    if (!product) return

    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再咨询',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            })
          }
        }
      })
      return
    }

    wx.navigateTo({
      url: `/pages/services/services?type=consult&productId=${product.id}&productName=${encodeURIComponent(product.name)}`
    })
  },

  // 加载更多
  loadMore() {
    this.loadProducts(false)
  },

  // 重置筛选
  onResetFilter() {
    this.setData({
      currentFilter: 'all',
      sortBy: 'default',
      sortText: '默认排序',
      categoryId: '',
      categoryName: ''
    })

    this.loadProducts(true)
  },

  // 搜索功能
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({ searchKeyword: keyword });

    // 防抖搜索
    clearTimeout(this.searchTimer);
    this.searchTimer = setTimeout(() => {
      if (keyword.length >= 2) {
        this.getSearchSuggestions(keyword);
      } else {
        this.setData({ searchSuggestions: [] });
      }
      this.searchProducts(keyword);
    }, 500);
  },

  onClearSearch() {
    this.setData({
      searchKeyword: '',
      searchSuggestions: [],
      showSearchHistory: false
    });
    this.loadProducts(true);
  },

  onSearchFocus() {
    this.loadSearchHistory();
    if (this.data.searchHistory.length > 0) {
      this.setData({ showSearchHistory: true });
    }
  },

  // 加载搜索历史
  loadSearchHistory() {
    try {
      const history = wx.getStorageSync('search_history') || []
      this.setData({
        searchHistory: history
      })
    } catch (e) {
      console.error('加载搜索历史失败:', e)
    }
  },

  onSearchBlur() {
    // 延迟隐藏，允许点击搜索历史
    setTimeout(() => {
      this.setData({ showSearchHistory: false });
    }, 200);
  },

  // 搜索建议点击
  onSuggestionTap(e) {
    const keyword = e.currentTarget.dataset.keyword;
    this.setData({
      searchKeyword: keyword,
      searchSuggestions: []
    });
    this.searchProducts(keyword);
  },

  // 搜索建议
  async getSearchSuggestions(keyword) {
    try {
      // 模拟搜索建议API
      const suggestions = [
        `${keyword} 储蓄险`,
        `${keyword} 重疾险`,
        `${keyword} 医疗险`,
        `${keyword} 人寿险`
      ].filter(item => item !== keyword);

      this.setData({ searchSuggestions: suggestions.slice(0, 4) });
    } catch (error) {
      console.error('获取搜索建议失败:', error);
    }
  },

  // 搜索历史管理
  addSearchHistory(keyword) {
    if (!keyword || keyword.length < 2) return;

    let history = wx.getStorageSync('search_history') || [];
    history = history.filter(item => item !== keyword);
    history.unshift(keyword);
    history = history.slice(0, 8); // 保留最近8条

    wx.setStorageSync('search_history', history);
    this.setData({ searchHistory: history });
  },

  loadSearchHistory() {
    const history = wx.getStorageSync('search_history') || [];
    this.setData({ searchHistory: history });
  },

  onSearchHistoryTap(e) {
    const keyword = e.currentTarget.dataset.keyword;
    this.setData({
      searchKeyword: keyword,
      showSearchHistory: false
    });
    this.searchProducts(keyword);
  },

  clearSearchHistory() {
    wx.removeStorageSync('search_history');
    this.setData({ searchHistory: [] });
    wx.showToast({ title: '已清空搜索历史', icon: 'success' });
  },

  // 重置筛选
  onResetFilter() {
    this.setData({
      currentFilter: 'all',
      searchKeyword: '',
      sortBy: 'default',
      sortText: '默认排序'
    });
    this.loadProducts(true);
  },

  // 刷新页面
  onRefresh() {
    this.loadProducts(true);
  },

  searchProducts(keyword) {
    if (!keyword.trim()) {
      this.loadProducts(true);
      return;
    }

    // 添加到搜索历史
    this.addSearchHistory(keyword);

    // 隐藏搜索建议和历史
    this.setData({
      searchSuggestions: [],
      showSearchHistory: false
    });

    // 调用搜索API
    this.loadProducts(true, { search: keyword });
  },

  // 高级筛选相关方法
  onMoreFilterTap() {
    this.setData({ showFilterModal: true });
  },

  closeFilterModal() {
    this.setData({ showFilterModal: false });
  },

  // 价格输入
  onMinPriceInput(e) {
    this.setData({
      'filterOptions.minPrice': e.detail.value,
      'filterOptions.priceRange': ''
    });
  },

  onMaxPriceInput(e) {
    this.setData({
      'filterOptions.maxPrice': e.detail.value,
      'filterOptions.priceRange': ''
    });
  },

  // 价格区间选择
  onPriceRangeSelect(e) {
    const range = e.currentTarget.dataset.range;
    const [minPrice, maxPrice] = range.split('-');

    this.setData({
      'filterOptions.priceRange': range,
      'filterOptions.minPrice': minPrice,
      'filterOptions.maxPrice': maxPrice
    });
  },

  // 保险公司选择
  onCompanyToggle(e) {
    const company = e.currentTarget.dataset.company;
    const companies = [...this.data.filterOptions.companies];
    const index = companies.indexOf(company);

    if (index > -1) {
      companies.splice(index, 1);
    } else {
      companies.push(company);
    }

    this.setData({
      'filterOptions.companies': companies
    });
  },

  // 保障期限选择
  onCoveragePeriodToggle(e) {
    const period = e.currentTarget.dataset.period;
    const periods = [...this.data.filterOptions.coveragePeriods];
    const index = periods.indexOf(period);

    if (index > -1) {
      periods.splice(index, 1);
    } else {
      periods.push(period);
    }

    this.setData({
      'filterOptions.coveragePeriods': periods
    });
  },

  // 特色功能选择
  onFeatureToggle(e) {
    const feature = e.currentTarget.dataset.feature;
    const features = [...this.data.filterOptions.features];
    const index = features.indexOf(feature);

    if (index > -1) {
      features.splice(index, 1);
    } else {
      features.push(feature);
    }

    this.setData({
      'filterOptions.features': features
    });
  },

  // 重置筛选
  onResetFilter() {
    this.setData({
      filterOptions: {
        minPrice: '',
        maxPrice: '',
        priceRange: '',
        companies: [],
        coveragePeriods: [],
        features: []
      }
    });
  },

  // 确认筛选
  onConfirmFilter() {
    this.setData({ showFilterModal: false });
    this.loadProducts(true);
    wx.showToast({ title: '筛选已应用', icon: 'success' });
  },

  // 分享
  onShareAppMessage() {
    const { categoryName } = this.data
    const title = categoryName ? `${categoryName} - 香港保险产品` : '香港保险产品列表'

    return {
      title: title,
      path: `/pages/products/products?categoryId=${this.data.categoryId}&categoryName=${encodeURIComponent(categoryName)}`,
      imageUrl: '/images/share-products.jpg'
    }
  }
})
