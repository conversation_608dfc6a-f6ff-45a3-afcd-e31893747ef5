<!--pages/products/products.wxml-->
<view class="container">
  <!-- 顶部搜索栏 -->
  <view class="search-header">
    <view class="search-box">
      <text class="search-icon">🔍</text>
      <input class="search-input" placeholder="搜索保险产品" value="{{searchKeyword}}"
             bindinput="onSearchInput" bindfocus="onSearchFocus" bindblur="onSearchBlur" />
      <text class="clear-icon" wx:if="{{searchKeyword}}" bindtap="onClearSearch">✕</text>
    </view>

    <!-- 搜索建议 -->
    <view class="search-suggestions" wx:if="{{searchSuggestions.length > 0}}">
      <view class="suggestion-item" wx:for="{{searchSuggestions}}" wx:key="*this"
            bindtap="onSuggestionTap" data-keyword="{{item}}">
        <text class="suggestion-icon">🔍</text>
        <text class="suggestion-text">{{item}}</text>
      </view>
    </view>

    <!-- 搜索历史 -->
    <view class="search-history" wx:if="{{showSearchHistory && searchHistory.length > 0}}">
      <view class="history-header">
        <text class="history-title">搜索历史</text>
        <text class="clear-history" bindtap="clearSearchHistory">清空</text>
      </view>
      <view class="history-items">
        <view class="history-item" wx:for="{{searchHistory}}" wx:key="*this"
              bindtap="onSearchHistoryTap" data-keyword="{{item}}">
          <text class="history-icon">🕐</text>
          <text class="history-text">{{item}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 筛选栏 -->
  <view class="filter-bar">
    <scroll-view class="filter-scroll" scroll-x="true">
      <view class="filter-item {{currentFilter === 'all' ? 'active' : ''}}" bindtap="onFilterTap" data-filter="all">
        全部
      </view>
      <view class="filter-item {{currentFilter === 'savings' ? 'active' : ''}}" bindtap="onFilterTap" data-filter="savings">
        储蓄分红
      </view>
      <view class="filter-item {{currentFilter === 'critical' ? 'active' : ''}}" bindtap="onFilterTap" data-filter="critical">
        重疾保险
      </view>
      <view class="filter-item {{currentFilter === 'medical' ? 'active' : ''}}" bindtap="onFilterTap" data-filter="medical">
        高端医疗
      </view>
      <view class="filter-item {{currentFilter === 'life' ? 'active' : ''}}" bindtap="onFilterTap" data-filter="life">
        人寿保险
      </view>
    </scroll-view>
  </view>

  <!-- 排序和筛选工具 -->
  <view class="toolbar">
    <view class="toolbar-left">
      <view class="sort-item" bindtap="onSortTap">
        <text class="sort-icon">🔽</text>
        <text class="sort-text">{{sortText}}</text>
      </view>
      <view class="filter-more" bindtap="onMoreFilterTap">
        <text class="filter-icon">⚙️</text>
        <text class="filter-text">筛选</text>
      </view>
    </view>
    <view class="toolbar-right">
      <text class="product-count">共{{totalCount}}款产品</text>
    </view>
  </view>

  <!-- 排序和筛选工具栏 -->
  <view class="filter-toolbar">
    <view class="toolbar-left">
      <view class="filter-btn" bindtap="onFilterTap">
        <text class="filter-icon">🔍</text>
        <text class="filter-text">筛选</text>
      </view>
      <view class="sort-btn" bindtap="onSortTap">
        <text class="sort-icon">📊</text>
        <text class="sort-text">{{sortOptions[currentSortIndex].name}}</text>
        <text class="sort-arrow">▼</text>
      </view>
    </view>
    <view class="toolbar-right">
      <text class="product-count">共{{totalCount}}款产品</text>
    </view>
  </view>

  <!-- 产品列表 -->
  <view class="products-container">
    <view class="product-card" wx:for="{{products}}" wx:key="id" bindtap="onProductTap" data-product="{{item}}">
      <!-- 产品标签 -->
      <view class="product-tags">
        <text class="tag hot" wx:if="{{item.is_hot}}">热销</text>
        <text class="tag featured" wx:if="{{item.is_featured}}">推荐</text>
        <text class="tag category">{{item.category_name}}</text>
      </view>

      <!-- 产品标签 -->
      <view class="product-tags" wx:if="{{item.tags && item.tags.length > 0}}">
        <view class="product-tag tag-{{tag.type}}" wx:for="{{item.tags}}" wx:key="type" wx:for-item="tag">
          <text class="tag-text">{{tag.text}}</text>
        </view>
      </view>

      <!-- 产品信息 -->
      <view class="product-header">
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <text class="product-subtitle">{{item.subtitle}}</text>
        </view>
        <view class="product-return" wx:if="{{item.expected_return}}">
          <text class="return-value">{{item.expected_return}}%</text>
          <text class="return-label">预期年化</text>
        </view>
      </view>

      <!-- 产品特性网格 -->
      <view class="product-specs">
        <view class="spec-item">
          <text class="spec-icon">💰</text>
          <text class="spec-label">最低保费</text>
          <text class="spec-value">{{item.min_premium || '2万美元'}}</text>
        </view>
        <view class="spec-item">
          <text class="spec-icon">📈</text>
          <text class="spec-label">分红方式</text>
          <text class="spec-value">{{item.dividend_type || '现金分红'}}</text>
        </view>
        <view class="spec-item">
          <text class="spec-icon">📅</text>
          <text class="spec-label">缴费期</text>
          <text class="spec-value">{{item.payment_period || '5/10年'}}</text>
        </view>
      </view>

      <!-- 公司信息 -->
      <view class="product-footer">
        <view class="company-info">
          <view class="company-logo-placeholder" wx:if="{{!item.company_logo}}">
            <text class="placeholder-icon">🏢</text>
          </view>
          <image wx:else src="{{item.company_logo}}" class="company-logo" mode="aspectFit"></image>
          <text class="company-name">{{item.company_name}}</text>
        </view>
        <view class="product-stats">
          <text class="stat-item">{{item.view_count}}次浏览</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="product-actions">
        <button class="btn-outline" bindtap="onProductDetailTap" data-product="{{item}}">查看详情</button>
        <button class="btn-primary" bindtap="onConsultTap" data-product="{{item}}">立即咨询</button>
      </view>
    </view>
  </view>

  <!-- 首次加载状态 -->
  <view class="loading-container" wx:if="{{loading && products.length === 0}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在加载产品...</text>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore && products.length > 0}}">
    <view class="loading-more" wx:if="{{loading}}">
      <view class="loading-spinner-small"></view>
      <text class="loading-text-small">加载中...</text>
    </view>
    <text class="load-more-text" wx:else>上拉加载更多</text>
  </view>

  <!-- 没有更多数据 -->
  <view class="no-more" wx:if="{{!hasMore && products.length > 0}}">
    <text class="no-more-text">没有更多产品了</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{products.length === 0 && !loading}}">
    <view class="empty-icon">📦</view>
    <text class="empty-title">暂无产品</text>
    <text class="empty-subtitle" wx:if="{{searchKeyword}}">没有找到"{{searchKeyword}}"相关的产品</text>
    <text class="empty-subtitle" wx:elif="{{currentFilter !== 'all'}}">当前筛选条件下暂无产品</text>
    <text class="empty-subtitle" wx:else>暂时没有相关产品，请稍后再试</text>
    <view class="empty-actions">
      <button class="retry-btn" wx:if="{{searchKeyword}}" bindtap="onClearSearch">清除搜索</button>
      <button class="retry-btn" wx:elif="{{currentFilter !== 'all'}}" bindtap="onResetFilter">重置筛选</button>
      <button class="retry-btn" wx:else bindtap="onRefresh">刷新页面</button>
    </view>
  </view>
</view>

<!-- 排序弹窗 -->
<view class="modal-overlay" wx:if="{{showSortModal}}" bindtap="closeSortModal">
  <view class="sort-modal" catchtap="">
    <view class="modal-header">
      <text class="modal-title">排序方式</text>
      <text class="modal-close" bindtap="closeSortModal">×</text>
    </view>
    <view class="sort-options">
      <view class="sort-option {{sortBy === 'default' ? 'active' : ''}}" bindtap="onSortSelect" data-sort="default">
        <text>默认排序</text>
        <text class="check-icon" wx:if="{{sortBy === 'default'}}">✓</text>
      </view>
      <view class="sort-option {{sortBy === 'return' ? 'active' : ''}}" bindtap="onSortSelect" data-sort="return">
        <text>收益率从高到低</text>
        <text class="check-icon" wx:if="{{sortBy === 'return'}}">✓</text>
      </view>
      <view class="sort-option {{sortBy === 'premium' ? 'active' : ''}}" bindtap="onSortSelect" data-sort="premium">
        <text>保费从低到高</text>
        <text class="check-icon" wx:if="{{sortBy === 'premium'}}">✓</text>
      </view>
      <view class="sort-option {{sortBy === 'popular' ? 'active' : ''}}" bindtap="onSortSelect" data-sort="popular">
        <text>热度从高到低</text>
        <text class="check-icon" wx:if="{{sortBy === 'popular'}}">✓</text>
      </view>
    </view>
  </view>
</view>

<!-- 高级筛选弹窗 -->
<view class="modal-overlay" wx:if="{{showFilterModal}}" bindtap="closeFilterModal">
  <view class="filter-modal" catchtap="">
    <view class="modal-header">
      <text class="modal-title">筛选条件</text>
      <text class="modal-close" bindtap="closeFilterModal">×</text>
    </view>

    <scroll-view class="filter-content" scroll-y="true">
      <!-- 价格区间 -->
      <view class="filter-section">
        <text class="filter-section-title">价格区间</text>
        <view class="price-range">
          <view class="price-input-group">
            <input class="price-input" placeholder="最低价" type="number"
                   value="{{filterOptions.minPrice}}" bindinput="onMinPriceInput" />
            <text class="price-separator">-</text>
            <input class="price-input" placeholder="最高价" type="number"
                   value="{{filterOptions.maxPrice}}" bindinput="onMaxPriceInput" />
          </view>
          <view class="price-presets">
            <view class="price-preset {{filterOptions.priceRange === '0-50000' ? 'active' : ''}}"
                  bindtap="onPriceRangeSelect" data-range="0-50000">
              <text>5万以下</text>
            </view>
            <view class="price-preset {{filterOptions.priceRange === '50000-100000' ? 'active' : ''}}"
                  bindtap="onPriceRangeSelect" data-range="50000-100000">
              <text>5-10万</text>
            </view>
            <view class="price-preset {{filterOptions.priceRange === '100000-200000' ? 'active' : ''}}"
                  bindtap="onPriceRangeSelect" data-range="100000-200000">
              <text>10-20万</text>
            </view>
            <view class="price-preset {{filterOptions.priceRange === '200000-999999' ? 'active' : ''}}"
                  bindtap="onPriceRangeSelect" data-range="200000-999999">
              <text>20万以上</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 保险公司 -->
      <view class="filter-section">
        <text class="filter-section-title">保险公司</text>
        <view class="filter-options">
          <view class="filter-option {{filterOptions.companies.includes(item.value) ? 'active' : ''}}"
                wx:for="{{companyOptions}}" wx:key="value"
                bindtap="onCompanyToggle" data-company="{{item.value}}">
            <text class="option-text">{{item.label}}</text>
            <text class="option-check" wx:if="{{filterOptions.companies.includes(item.value)}}">✓</text>
          </view>
        </view>
      </view>

      <!-- 保障期限 -->
      <view class="filter-section">
        <text class="filter-section-title">保障期限</text>
        <view class="filter-options">
          <view class="filter-option {{filterOptions.coveragePeriods.includes(item.value) ? 'active' : ''}}"
                wx:for="{{coveragePeriodOptions}}" wx:key="value"
                bindtap="onCoveragePeriodToggle" data-period="{{item.value}}">
            <text class="option-text">{{item.label}}</text>
            <text class="option-check" wx:if="{{filterOptions.coveragePeriods.includes(item.value)}}">✓</text>
          </view>
        </view>
      </view>

      <!-- 特色功能 -->
      <view class="filter-section">
        <text class="filter-section-title">特色功能</text>
        <view class="filter-options">
          <view class="filter-option {{filterOptions.features.includes(item.value) ? 'active' : ''}}"
                wx:for="{{featureOptions}}" wx:key="value"
                bindtap="onFeatureToggle" data-feature="{{item.value}}">
            <text class="option-text">{{item.label}}</text>
            <text class="option-check" wx:if="{{filterOptions.features.includes(item.value)}}">✓</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <view class="filter-actions">
      <button class="filter-reset-btn" bindtap="onResetFilter">重置</button>
      <button class="filter-confirm-btn" bindtap="onConfirmFilter">确定</button>
    </view>
  </view>
</view>
