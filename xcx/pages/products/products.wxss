/* pages/products/products.wxss */
@import "../../common/placeholder.wxss";

/* 搜索栏样式 */
.search-header {
  background: #fff;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.search-box {
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 50rpx;
  padding: 0 30rpx;
  height: 80rpx;
}

.search-icon {
  font-size: 32rpx;
  color: #999;
  margin-right: 20rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.clear-icon {
  font-size: 32rpx;
  color: #999;
  margin-left: 20rpx;
}

/* 搜索建议样式 */
.search-suggestions {
  position: absolute;
  top: 120rpx;
  left: 20rpx;
  right: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  max-height: 400rpx;
  overflow-y: auto;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-icon {
  font-size: 28rpx;
  color: #999;
  margin-right: 24rpx;
}

.suggestion-text {
  font-size: 28rpx;
  color: #333;
}

/* 搜索历史样式 */
.search-history {
  position: absolute;
  top: 120rpx;
  left: 20rpx;
  right: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  max-height: 500rpx;
  overflow-y: auto;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.history-title {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.clear-history {
  font-size: 24rpx;
  color: #999;
}

.history-items {
  padding: 16rpx 0;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 20rpx 32rpx;
}

.history-icon {
  font-size: 24rpx;
  color: #999;
  margin-right: 24rpx;
}

.history-text {
  font-size: 28rpx;
  color: #666;
}

/* 筛选栏 */
.filter-bar {
  background: white;
  padding: 24rpx;
  border-bottom: 1rpx solid #E5E7EB;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-item {
  display: inline-block;
  padding: 16rpx 32rpx;
  margin-right: 24rpx;
  border-radius: 48rpx;
  font-size: 24rpx;
  background: #F3F4F6;
  color: #6B7280;
  transition: all 0.3s;
}

.filter-item.active {
  background: #1E40AF;
  color: white;
}

/* 工具栏 */
.toolbar {
  background: white;
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #E5E7EB;
}

.sort-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #374151;
}

.sort-arrow {
  font-size: 20rpx;
  color: #9CA3AF;
}

.filter-more {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #374151;
}

.filter-icon {
  font-size: 20rpx;
  color: #9CA3AF;
}

/* 产品容器 */
.products-container {
  padding: 24rpx;
}

/* 产品卡片 */
.product-card {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1rpx solid #F0F0F0;
}

/* 产品标签 */
.product-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  margin-bottom: 24rpx;
}

.product-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: bold;
}

.tag-hot {
  background: #dbeafe;
  color: #1976D2;
}

.tag-featured {
  background: #fef3c7;
  color: #d97706;
}

.tag-high-return {
  background: #dcfce7;
  color: #16a34a;
}

.tag-savings {
  background: #e0f2fe;
  color: #0277bd;
}

.tag-critical {
  background: #fce7f3;
  color: #be185d;
}

.tag-medical {
  background: #f3e8ff;
  color: #7c3aed;
}

.tag-life {
  background: #fff7ed;
  color: #ea580c;
}

.tag-text {
  font-size: 20rpx;
}

/* 兼容旧的标签样式 */
.tag {
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 20rpx;
  font-weight: bold;
}

.tag.hot {
  background: #FEE2E2;
  color: #DC2626;
}

.tag.featured {
  background: #DBEAFE;
  color: #1E40AF;
}

.tag.category {
  background: #F3E8FF;
  color: #7C3AED;
}

/* 产品头部 */
.product-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.product-info {
  flex: 1;
  margin-right: 24rpx;
}

.product-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 8rpx;
}

.product-subtitle {
  display: block;
  font-size: 24rpx;
  color: #6B7280;
  line-height: 1.4;
}

.product-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: right;
}

.price-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #1E40AF;
}

.price-label {
  font-size: 20rpx;
  color: #9CA3AF;
  margin-top: 4rpx;
}

/* 产品特色 */
.product-features {
  margin-bottom: 24rpx;
}

.feature-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  width: 48%;
  margin-bottom: 16rpx;
}

.feature-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #10B981;
  color: white;
  font-size: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.feature-text {
  font-size: 24rpx;
  color: #374151;
}

/* 产品底部 */
.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #F3F4F6;
}

.company-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.company-logo {
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
}

.company-name {
  font-size: 24rpx;
  color: #6B7280;
}

.product-stats {
  font-size: 20rpx;
  color: #9CA3AF;
}

/* 操作按钮 */
.product-actions {
  display: flex;
  gap: 24rpx;
}

.product-actions .btn-outline,
.product-actions .btn-primary {
  flex: 1;
  padding: 20rpx;
  font-size: 24rpx;
}

/* 加载更多 */
.load-more {
  padding: 48rpx;
  text-align: center;
}

.loading {
  color: #6B7280;
  font-size: 24rpx;
}

.load-more-btn {
  color: #1E40AF;
  font-size: 24rpx;
  padding: 24rpx;
  border: 1rpx solid #1E40AF;
  border-radius: 24rpx;
  display: inline-block;
}

/* 空状态 */
.empty-state {
  padding: 120rpx 48rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #6B7280;
  margin-bottom: 48rpx;
}

/* 排序弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.sort-modal {
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E7EB;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1F2937;
}

.modal-close {
  font-size: 48rpx;
  color: #9CA3AF;
  line-height: 1;
}

.sort-options {
  padding: 24rpx 0;
}

.sort-option {
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  color: #374151;
}

.sort-option.active {
  color: #1E40AF;
  background: #F0F9FF;
}

.check-icon {
  color: #1E40AF;
  font-weight: bold;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1e40af;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

.loading-text {
  font-size: 24rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 加载更多 */
.load-more {
  padding: 32rpx;
  text-align: center;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.loading-spinner-small {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #f3f3f3;
  border-top: 2rpx solid #1e40af;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text-small {
  font-size: 22rpx;
  color: #666;
}

.load-more-text {
  font-size: 24rpx;
  color: #999;
}

.no-more {
  padding: 32rpx;
  text-align: center;
}

.no-more-text {
  font-size: 22rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.3;
}

.empty-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 48rpx;
  line-height: 1.5;
}

.empty-actions {
  display: flex;
  gap: 24rpx;
}

.retry-btn {
  padding: 20rpx 40rpx;
  background: #1e40af;
  color: white;
  border-radius: 50rpx;
  font-size: 24rpx;
  border: none;
}

/* 高级筛选弹窗 */
.filter-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 80%;
  background: white;
  z-index: 1001;
  display: flex;
  flex-direction: column;
}

.filter-content {
  flex: 1;
  padding: 0 32rpx;
}

.filter-section {
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

/* 价格区间 */
.price-range {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.price-input-group {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.price-input {
  flex: 1;
  padding: 20rpx 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 24rpx;
  text-align: center;
}

.price-separator {
  font-size: 24rpx;
  color: #666;
}

.price-presets {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.price-preset {
  padding: 16rpx 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 24rpx;
  font-size: 22rpx;
  color: #666;
  background: white;
}

.price-preset.active {
  border-color: #1e40af;
  background: #f0f9ff;
  color: #1e40af;
}

/* 筛选选项 */
.filter-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.filter-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  background: white;
}

.filter-option.active {
  border-color: #1e40af;
  background: #f0f9ff;
}

.option-text {
  font-size: 24rpx;
  color: #333;
}

.filter-option.active .option-text {
  color: #1e40af;
}

.option-check {
  font-size: 24rpx;
  color: #1e40af;
  font-weight: bold;
}

/* 筛选操作按钮 */
.filter-actions {
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
  background: white;
}

.filter-reset-btn {
  flex: 1;
  padding: 24rpx;
  background: #f3f4f6;
  color: #666;
  border-radius: 16rpx;
  font-size: 24rpx;
  border: none;
}

.filter-confirm-btn {
  flex: 2;
  padding: 24rpx;
  background: #1e40af;
  color: white;
  border-radius: 16rpx;
  font-size: 24rpx;
  border: none;
}

/* 产品特性网格 */
.product-specs {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: #F8F9FA;
  margin: 0 32rpx;
  border-radius: 16rpx;
}

.spec-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 30%;
  margin-bottom: 16rpx;
}

.spec-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.spec-label {
  font-size: 20rpx;
  color: #6B7280;
  margin-bottom: 4rpx;
}

.spec-value {
  font-size: 24rpx;
  color: #1F2937;
  font-weight: 500;
}

/* 排序和筛选工具栏 */
.filter-toolbar {
  background: white;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.filter-btn, .sort-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 0;
}

.filter-icon, .sort-icon {
  font-size: 28rpx;
  color: #666;
}

.filter-text, .sort-text {
  font-size: 28rpx;
  color: #666;
}

.sort-arrow {
  font-size: 20rpx;
  color: #999;
  transition: transform 0.3s ease;
}

.sort-btn.active .sort-arrow {
  transform: rotate(180deg);
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.product-count {
  font-size: 26rpx;
  color: #999;
}
