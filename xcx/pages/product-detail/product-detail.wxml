<!--pages/product-detail/product-detail.wxml-->
<view class="container">
  <!-- 产品头部信息 -->
  <view class="product-header gradient-bg">
    <view class="header-content">
      <view class="product-tags">
        <text class="tag hot" wx:if="{{product.is_hot}}">热销</text>
        <text class="tag featured" wx:if="{{product.is_featured}}">推荐</text>
        <text class="tag category">{{product.category.name}}</text>
      </view>
      
      <text class="product-title">{{product.name}}</text>
      <text class="product-subtitle">{{product.subtitle}}</text>
      
      <view class="product-highlight">
        <view class="highlight-item">
          <text class="highlight-value" wx:if="{{product.expected_return}}">{{product.expected_return}}%</text>
          <text class="highlight-value" wx:else>{{product.min_premium}}</text>
          <text class="highlight-label" wx:if="{{product.expected_return}}">预期年化收益</text>
          <text class="highlight-label" wx:else>起/{{product.currency}}</text>
        </view>
        <view class="highlight-item">
          <text class="highlight-value">{{product.view_count}}</text>
          <text class="highlight-label">已浏览</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button class="action-btn favorite-btn {{isFavorited ? 'favorited' : ''}}"
                bindtap="onToggleFavorite">
          <text class="btn-icon">{{isFavorited ? '❤️' : '🤍'}}</text>
          <text class="btn-text">{{isFavorited ? '已收藏' : '收藏'}}</text>
        </button>
        <button class="action-btn compare-btn {{isInCompare ? 'in-compare' : ''}}"
                bindtap="onToggleCompare">
          <text class="btn-icon">{{isInCompare ? '📊' : '📈'}}</text>
          <text class="btn-text">{{isInCompare ? '已加入' : '比较'}}</text>
        </button>
        <button class="action-btn share-btn" bindtap="onShare">
          <text class="btn-icon">📤</text>
          <text class="btn-text">分享</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 保费计算器 -->
  <view class="calculator-card">
    <view class="card-header">
      <text class="card-title">保费计算器</text>
      <text class="card-icon">🧮</text>
    </view>
    
    <view class="calculator-form">
      <view class="form-row">
        <view class="form-item">
          <text class="form-label">年缴保费</text>
          <picker bindchange="onPremiumChange" value="{{premiumIndex}}" range="{{premiumOptions}}" range-key="label">
            <view class="picker-input">
              <text>{{premiumOptions[premiumIndex].label}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>
        <view class="form-item">
          <text class="form-label">缴费年期</text>
          <picker bindchange="onPeriodChange" value="{{periodIndex}}" range="{{product.payment_periods}}">
            <view class="picker-input">
              <text>{{product.payment_periods[periodIndex]}}年</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>
      </view>
      
      <view class="form-item">
        <text class="form-label">被保险人年龄</text>
        <picker bindchange="onAgeChange" value="{{ageIndex}}" range="{{ageRanges}}">
          <view class="picker-input">
            <text>{{ageRanges[ageIndex]}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
      
      <!-- 预期价值显示 - 按照原型图设计 -->
      <view class="expected-value-card">
        <view class="value-row">
          <text class="value-label">预期20年后价值</text>
          <text class="value-amount">约{{calculatedValue}}万{{product.currency}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 产品特点 -->
  <view class="section-card">
    <text class="section-title">产品特点</text>
    <view class="features-grid">
      <!-- 预期年化收益 -->
      <view class="feature-row">
        <view class="feature-left">
          <view class="feature-icon blue">📈</view>
          <view class="feature-info">
            <text class="feature-name">预期年化收益</text>
            <text class="feature-desc">长期稳健增值</text>
          </view>
        </view>
        <text class="feature-value blue">{{product.expected_return || '6.5'}}%</text>
      </view>

      <!-- 最低保费 -->
      <view class="feature-row">
        <view class="feature-left">
          <view class="feature-icon green">💰</view>
          <view class="feature-info">
            <text class="feature-name">最低保费</text>
            <text class="feature-desc">年缴保费起点</text>
          </view>
        </view>
        <text class="feature-value green">{{product.min_premium || '2万美元'}}</text>
      </view>

      <!-- 缴费期选择 -->
      <view class="feature-row">
        <view class="feature-left">
          <view class="feature-icon purple">📅</view>
          <view class="feature-info">
            <text class="feature-name">缴费期选择</text>
            <text class="feature-desc">灵活缴费安排</text>
          </view>
        </view>
        <text class="feature-value purple">{{product.payment_periods[0] || '5'}}/{{product.payment_periods[1] || '10'}}年</text>
      </view>

      <!-- 保障期限 -->
      <view class="feature-row">
        <view class="feature-left">
          <view class="feature-icon orange">🛡️</view>
          <view class="feature-info">
            <text class="feature-name">保障期限</text>
            <text class="feature-desc">终身保障</text>
          </view>
        </view>
        <text class="feature-value orange">终身</text>
      </view>

      <!-- 分红方式 -->
      <view class="feature-row">
        <view class="feature-left">
          <view class="feature-icon red">💎</view>
          <view class="feature-info">
            <text class="feature-name">分红方式</text>
            <text class="feature-desc">现金分红</text>
          </view>
        </view>
        <text class="feature-value red">现金分红</text>
      </view>
    </view>
  </view>

  <!-- 产品优势 -->
  <view class="section-card">
    <text class="section-title">产品优势</text>
    <view class="advantages-list">
      <view class="advantage-item" wx:for="{{productAdvantages}}" wx:key="id">
        <view class="advantage-icon">
          <text class="check-icon">✓</text>
        </view>
        <view class="advantage-content">
          <text class="advantage-title">{{item.title}}</text>
          <text class="advantage-desc">{{item.description}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 保障详情 -->
  <view class="section-card" wx:if="{{product.coverage_details && Object.keys(product.coverage_details).length > 0}}">
    <text class="section-title">保障详情</text>
    <view class="coverage-list">
      <view class="coverage-item" wx:for="{{coverageList}}" wx:key="key">
        <view class="coverage-info">
          <text class="coverage-name">{{item.name}}</text>
          <text class="coverage-desc">{{item.description}}</text>
        </view>
        <text class="coverage-amount">{{item.amount}}</text>
      </view>
    </view>
  </view>

  <!-- 公司信息 -->
  <view class="section-card">
    <text class="section-title">保险公司</text>
    <view class="company-card">
      <image src="{{product.company.logo || '/images/company-placeholder.png'}}" class="company-logo" mode="aspectFit"></image>
      <view class="company-info">
        <text class="company-name">{{product.company.name}}</text>
        <text class="company-desc">{{product.company.description}}</text>
        <view class="company-meta">
          <text class="company-year">成立于{{product.company.established_year}}年</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 用户评价 -->
  <view class="section-card">
    <view class="section-header">
      <text class="section-title">用户评价</text>
      <view class="rating-summary">
        <text class="rating-score">{{averageRating}}</text>
        <view class="rating-stars">
          <text class="star {{index < Math.floor(averageRating) ? 'filled' : ''}}" wx:for="{{5}}" wx:key="*this">★</text>
        </view>
        <text class="rating-count">({{reviewsCount}}条评价)</text>
      </view>
    </view>
    
    <view class="reviews-list" wx:if="{{reviews.length > 0}}">
      <view class="review-item" wx:for="{{reviews}}" wx:key="id">
        <view class="review-header">
          <text class="reviewer-name">{{item.user_nickname}}</text>
          <view class="review-rating">
            <text class="star {{starIndex < item.rating ? 'filled' : ''}}" wx:for="{{5}}" wx:key="*this" wx:for-index="starIndex">★</text>
          </view>
          <text class="review-date">{{item.created_at}}</text>
        </view>
        <text class="review-content">{{item.content}}</text>
      </view>
    </view>
    
    <view class="no-reviews" wx:else>
      <text>暂无评价</text>
    </view>
  </view>

  <!-- 产品优势 -->
  <view class="section">
    <text class="section-title">产品优势</text>
    <view class="advantages-list">
      <view class="advantage-item" wx:for="{{productAdvantages}}" wx:key="id">
        <view class="advantage-icon">✓</view>
        <view class="advantage-content">
          <text class="advantage-title">{{item.title}}</text>
          <text class="advantage-desc">{{item.description}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 用户评价详情 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">用户评价</text>
      <button class="view-all-btn" bindtap="onViewAllReviews">查看全部</button>
    </view>

    <view class="reviews-summary">
      <view class="rating-overview">
        <text class="rating-score">{{product.rating || '4.8'}}</text>
        <view class="rating-stars">
          <text class="star filled" wx:for="{{5}}" wx:key="*this">★</text>
        </view>
        <text class="rating-count">{{product.review_count || '1,234'}}条评价</text>
      </view>
    </view>

    <view class="user-reviews-list">
      <view class="user-review-item" wx:for="{{userReviews}}" wx:key="id">
        <view class="review-header">
          <view class="user-avatar-placeholder">
            <text class="placeholder-icon">👤</text>
          </view>
          <view class="review-user">
            <text class="user-name">{{item.user_name}}</text>
            <view class="review-rating">
              <text class="star filled" wx:for="{{item.rating}}" wx:key="*this">★</text>
            </view>
          </view>
        </view>
        <text class="review-content">{{item.content}}</text>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button class="action-btn secondary" bindtap="onCollectTap">
      <text class="btn-icon">{{isCollected ? '♥' : '♡'}}</text>
      <text class="btn-text">{{isCollected ? '已收藏' : '收藏'}}</text>
    </button>
    <button class="action-btn secondary" bindtap="onShareTap">
      <text class="btn-icon">📤</text>
      <text class="btn-text">分享</text>
    </button>
    <button class="action-btn primary" bindtap="onConsultTap">
      <text class="btn-text">立即咨询</text>
    </button>
  </view>

  <!-- 底部固定操作栏 - 按照原型图设计 -->
  <view class="bottom-action-bar">
    <button class="bottom-btn secondary" bindtap="onAddToCompare">
      <text class="bottom-btn-icon">📊</text>
      <text class="bottom-btn-text">加入对比</text>
    </button>
    <button class="bottom-btn primary" bindtap="onPurchaseNow">
      <text class="bottom-btn-text">立即投保</text>
    </button>
  </view>

  <!-- 底部安全间距 -->
  <view class="bottom-safe-area"></view>
</view>
