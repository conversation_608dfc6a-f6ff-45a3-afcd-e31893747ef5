/* pages/product-detail/product-detail.wxss */

/* 产品头部 */
.product-header {
  color: white;
  padding: 48rpx 32rpx;
  position: relative;
  overflow: hidden;
}

.header-content {
  position: relative;
  z-index: 2;
}

.product-tags {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.tag {
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 20rpx;
  font-weight: bold;
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.product-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  line-height: 1.3;
}

.product-subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 32rpx;
  line-height: 1.4;
}

.product-highlight {
  display: flex;
  gap: 48rpx;
}

.highlight-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.highlight-value {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.highlight-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 24rpx;
  margin-top: 32rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 16rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  border: none;
  color: white;
  font-size: 24rpx;
  backdrop-filter: blur(10rpx);
}

.action-btn.favorited {
  background: rgba(255, 107, 107, 0.3);
}

.action-btn.in-compare {
  background: rgba(59, 130, 246, 0.3);
}

.btn-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.btn-text {
  font-size: 22rpx;
  opacity: 0.9;
}

/* 计算器卡片 */
.calculator-card {
  background: white;
  border-radius: 32rpx;
  margin: -48rpx 32rpx 32rpx;
  padding: 32rpx;
  box-shadow: 0 16rpx 24rpx -4rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 3;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1F2937;
}

.card-icon {
  font-size: 32rpx;
}

.calculator-form {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.form-row {
  display: flex;
  gap: 24rpx;
}

.form-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.form-label {
  font-size: 24rpx;
  color: #6B7280;
  font-weight: 500;
}

.picker-input {
  border: 2rpx solid #E5E7EB;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #F9FAFB;
  color: #374151;
}

.picker-arrow {
  color: #9CA3AF;
  font-size: 20rpx;
}

.calculator-result {
  background: #F0F9FF;
  border: 2rpx solid #BFDBFE;
  border-radius: 24rpx;
  padding: 32rpx;
  text-align: center;
  margin-top: 16rpx;
}

.result-label {
  display: block;
  font-size: 24rpx;
  color: #1E40AF;
  margin-bottom: 8rpx;
}

.result-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #1E40AF;
}

/* 分区卡片 */
.section-card {
  background: white;
  border-radius: 32rpx;
  margin: 0 32rpx 32rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 12rpx -2rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 24rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

/* 产品特色 */
.features-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.feature-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #10B981;
  color: white;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.feature-text {
  font-size: 28rpx;
  color: #374151;
  line-height: 1.4;
}

/* 产品特点网格样式 */
.features-grid {
  display: flex;
  flex-direction: column;
}

.feature-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.feature-row:last-child {
  border-bottom: none;
}

.feature-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.features-grid .feature-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-right: 24rpx;
}

.features-grid .feature-icon.blue {
  background: #E3F2FD;
  color: #1976D2;
}

.features-grid .feature-icon.green {
  background: #E8F5E8;
  color: #4CAF50;
}

.features-grid .feature-icon.purple {
  background: #F3E5F5;
  color: #9C27B0;
}

.features-grid .feature-icon.orange {
  background: #FFF3E0;
  color: #FF9800;
}

.features-grid .feature-icon.red {
  background: #FFEBEE;
  color: #F44336;
}

.feature-info {
  display: flex;
  flex-direction: column;
}

.feature-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 26rpx;
  color: #666;
}

.feature-value {
  font-size: 36rpx;
  font-weight: bold;
}

.feature-value.blue {
  color: #1976D2;
}

.feature-value.green {
  color: #4CAF50;
}

.feature-value.purple {
  color: #9C27B0;
}

.feature-value.orange {
  color: #FF9800;
}

.feature-value.red {
  color: #F44336;
}

/* 产品优势列表 */
.advantages-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.advantage-item {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
}

.advantage-icon {
  width: 48rpx;
  height: 48rpx;
  background: #E8F5E8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.check-icon {
  font-size: 24rpx;
  color: #4CAF50;
  font-weight: bold;
}

.advantage-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.advantage-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1F2937;
  margin-bottom: 8rpx;
}

.advantage-desc {
  font-size: 28rpx;
  color: #6B7280;
  line-height: 1.5;
}

/* 预期价值卡片 - 按照原型图设计 */
.expected-value-card {
  background: #EBF8FF;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-top: 24rpx;
}

.value-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.value-label {
  font-size: 28rpx;
  color: #6B7280;
}

.value-amount {
  font-size: 36rpx;
  font-weight: bold;
  color: #1976D2;
}

/* 底部固定操作栏 */
.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #F0F0F0;
  display: flex;
  gap: 24rpx;
  z-index: 100;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
}

.bottom-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
}

.bottom-btn.secondary {
  background: #F3F4F6;
  color: #374151;
}

.bottom-btn.primary {
  background: linear-gradient(135deg, #1976D2 0%, #42A5F5 100%);
  color: white;
}

.bottom-btn-icon {
  font-size: 28rpx;
}

.bottom-btn-text {
  font-size: 32rpx;
  font-weight: 600;
}

/* 保障详情 */
.coverage-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.coverage-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24rpx;
  background: #F9FAFB;
  border-radius: 16rpx;
  border: 1rpx solid #E5E7EB;
}

.coverage-info {
  flex: 1;
  margin-right: 24rpx;
}

.coverage-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1F2937;
  margin-bottom: 8rpx;
}

.coverage-desc {
  font-size: 24rpx;
  color: #6B7280;
  line-height: 1.4;
}

.coverage-amount {
  font-size: 28rpx;
  font-weight: bold;
  color: #1E40AF;
  text-align: right;
}

/* 公司信息 */
.company-card {
  display: flex;
  gap: 24rpx;
  align-items: flex-start;
}

.company-logo {
  width: 96rpx;
  height: 96rpx;
  border-radius: 16rpx;
  flex-shrink: 0;
}

.company-info {
  flex: 1;
}

.company-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 12rpx;
}

.company-desc {
  display: block;
  font-size: 24rpx;
  color: #6B7280;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.company-meta {
  display: flex;
  gap: 24rpx;
}

.company-year {
  font-size: 20rpx;
  color: #9CA3AF;
  background: #F3F4F6;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

/* 评价相关 */
.rating-summary {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.rating-score {
  font-size: 36rpx;
  font-weight: bold;
  color: #F59E0B;
}

.rating-stars {
  display: flex;
  gap: 4rpx;
}

.star {
  font-size: 24rpx;
  color: #E5E7EB;
}

.star.filled {
  color: #F59E0B;
}

.rating-count {
  font-size: 20rpx;
  color: #9CA3AF;
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.review-item {
  padding: 24rpx;
  background: #F9FAFB;
  border-radius: 16rpx;
  border: 1rpx solid #E5E7EB;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.reviewer-name {
  font-size: 24rpx;
  font-weight: 600;
  color: #374151;
}

.review-rating {
  display: flex;
  gap: 2rpx;
}

.review-rating .star {
  font-size: 20rpx;
}

.review-date {
  font-size: 20rpx;
  color: #9CA3AF;
}

.review-content {
  font-size: 26rpx;
  color: #4B5563;
  line-height: 1.5;
}

.no-reviews {
  text-align: center;
  padding: 48rpx;
  color: #9CA3AF;
  font-size: 24rpx;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #E5E7EB;
  display: flex;
  gap: 24rpx;
  z-index: 100;
}

.action-btn {
  border: none;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 600;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx;
  min-height: 96rpx;
}

.action-btn.secondary {
  background: #F3F4F6;
  color: #6B7280;
  flex: 0 0 120rpx;
}

.action-btn.primary {
  background: #1E40AF;
  color: white;
  flex: 1;
}

.btn-icon {
  font-size: 28rpx;
}

.btn-text {
  font-size: 20rpx;
}

.action-btn.primary .btn-text {
  font-size: 28rpx;
}

/* 为底部操作栏留出空间 */
.container {
  padding-bottom: 200rpx;
}

/* 产品优势 */
.advantages-list {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.advantage-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32rpx;
}

.advantage-item:last-child {
  margin-bottom: 0;
}

.advantage-icon {
  width: 40rpx;
  height: 40rpx;
  background: #10B981;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.advantage-content {
  flex: 1;
}

.advantage-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 8rpx;
}

.advantage-desc {
  display: block;
  font-size: 24rpx;
  color: #6B7280;
  line-height: 1.5;
}

/* 用户评价详情 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.view-all-btn {
  background: none;
  border: none;
  color: #1976D2;
  font-size: 24rpx;
  padding: 0;
}

.reviews-summary {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.rating-overview {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.rating-score {
  font-size: 48rpx;
  font-weight: bold;
  color: #F59E0B;
}

.rating-stars {
  display: flex;
  gap: 4rpx;
}

.star {
  font-size: 24rpx;
  color: #F59E0B;
}

.rating-count {
  font-size: 24rpx;
  color: #6B7280;
}

.user-reviews-list {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.user-review-item {
  padding-bottom: 32rpx;
  margin-bottom: 32rpx;
  border-bottom: 1rpx solid #F3F4F6;
}

.user-review-item:last-child {
  padding-bottom: 0;
  margin-bottom: 0;
  border-bottom: none;
}

.review-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.user-avatar-placeholder {
  width: 64rpx;
  height: 64rpx;
  background: #F3F4F6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.user-avatar-placeholder .placeholder-icon {
  font-size: 32rpx;
  color: #9CA3AF;
}

.review-user {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 8rpx;
}

.review-rating {
  display: flex;
  gap: 4rpx;
}

.review-rating .star {
  font-size: 20rpx;
}

.review-content {
  font-size: 26rpx;
  color: #4B5563;
  line-height: 1.6;
}
