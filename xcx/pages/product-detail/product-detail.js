// pages/product-detail/product-detail.js
const app = getApp()

Page({
  data: {
    product: {},
    reviews: [],
    reviewsCount: 0,
    averageRating: 0,
    coverageList: [],
    isCollected: false,
    isFavorited: false,
    isInCompare: false,
    loading: false,
    
    // 计算器相关
    premiumOptions: [
      { label: '2万美元', value: 20000 },
      { label: '5万美元', value: 50000 },
      { label: '10万美元', value: 100000 },
      { label: '20万美元', value: 200000 }
    ],
    premiumIndex: 0,
    periodIndex: 0,
    ageRanges: ['0-17岁', '18-40岁', '41-60岁', '61-75岁'],
    ageIndex: 1,
    calculatedValue: '65万',

    // 产品优势
    productAdvantages: [
      {
        id: 1,
        title: '友邦品牌保障',
        description: '百年品牌，信誉卓著，财务稳健'
      },
      {
        id: 2,
        title: '美元资产配置',
        description: '美元计价，对冲汇率风险'
      },
      {
        id: 3,
        title: '灵活提取',
        description: '第5年起可部分退保，资金灵活'
      },
      {
        id: 4,
        title: '传承功能',
        description: '可指定受益人，财富传承无忧'
      }
    ],

    // 用户评价
    userReviews: [
      {
        id: 1,
        user_name: '张***',
        rating: 5,
        content: '为孩子配置的储蓄计划，收益稳定，很满意友邦的服务！'
      },
      {
        id: 2,
        user_name: '李***',
        rating: 5,
        content: '香港保险收益确实比内地高，专业顾问服务很到位。'
      }
    ]
  },

  onLoad(options) {
    if (options.id) {
      this.productId = options.id
      this.loadProductDetail()
      this.loadProductReviews()
    } else {
      wx.showToast({
        title: '产品ID不能为空',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  onShow() {
    // 检查收藏状态
    this.checkCollectionStatus()
  },

  // 加载产品详情
  async loadProductDetail() {
    if (this.data.loading) return

    this.setData({ loading: true })
    app.showLoading('加载中...')

    try {
      const product = await app.request({
        url: `/insurance/api/products/${this.productId}/`
      })

      // 处理保障详情数据
      const coverageList = this.processCoverageDetails(product.coverage_details || {})

      this.setData({
        product: product,
        coverageList: coverageList,
        periodIndex: 0 // 重置缴费期选择
      })

      // 更新页面标题
      wx.setNavigationBarTitle({
        title: product.name
      })

      // 计算初始保费
      this.calculateValue()

      // 检查收藏和比较状态
      this.checkFavoriteStatus()
      this.checkCompareStatus()

    } catch (error) {
      console.error('加载产品详情失败:', error)
      app.showToast('加载失败，请重试')
    } finally {
      this.setData({ loading: false })
      app.hideLoading()
    }
  },

  // 加载产品评价
  async loadProductReviews() {
    try {
      const response = await app.request({
        url: `/insurance/api/products/${this.productId}/reviews/`,
        data: { page_size: 5 }
      })

      const reviews = response.results || []
      const reviewsCount = response.count || 0
      
      // 计算平均评分
      let averageRating = 0
      if (reviews.length > 0) {
        const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0)
        averageRating = (totalRating / reviews.length).toFixed(1)
      }

      // 格式化评价时间
      const formattedReviews = reviews.map(review => ({
        ...review,
        created_at: this.formatDate(review.created_at)
      }))

      this.setData({
        reviews: formattedReviews,
        reviewsCount: reviewsCount,
        averageRating: parseFloat(averageRating)
      })

    } catch (error) {
      console.error('加载产品评价失败:', error)
    }
  },

  // 处理保障详情数据
  processCoverageDetails(coverageDetails) {
    const coverageList = []
    
    for (const [key, value] of Object.entries(coverageDetails)) {
      if (typeof value === 'object' && value.amount) {
        coverageList.push({
          key: key,
          name: value.name || key,
          description: value.description || '',
          amount: value.amount
        })
      } else if (typeof value === 'string' || typeof value === 'number') {
        coverageList.push({
          key: key,
          name: this.getCoverageDisplayName(key),
          description: '',
          amount: value
        })
      }
    }
    
    return coverageList
  },

  // 获取保障项目显示名称
  getCoverageDisplayName(key) {
    const nameMap = {
      'medical': '医疗费用',
      'death': '身故保障',
      'disability': '残疾保障',
      'critical_illness': '重疾保障',
      'accident': '意外保障'
    }
    return nameMap[key] || key
  },

  // 检查收藏状态
  checkCollectionStatus() {
    const collections = wx.getStorageSync('collections') || []
    const isCollected = collections.includes(this.productId)
    this.setData({ isCollected })
  },

  // 保费选择变化
  onPremiumChange(e) {
    this.setData({
      premiumIndex: parseInt(e.detail.value)
    })
    this.calculateValue()
  },

  // 缴费期变化
  onPeriodChange(e) {
    this.setData({
      periodIndex: parseInt(e.detail.value)
    })
    this.calculateValue()
  },

  // 年龄变化
  onAgeChange(e) {
    this.setData({
      ageIndex: parseInt(e.detail.value)
    })
    this.calculateValue()
  },

  // 计算预期价值
  calculateValue() {
    const { premiumOptions, premiumIndex, periodIndex, product } = this.data
    
    if (!product.payment_periods || product.payment_periods.length === 0) {
      return
    }

    const premium = premiumOptions[premiumIndex].value
    const period = product.payment_periods[periodIndex]
    const expectedReturn = product.expected_return || 6.5

    // 简单的复利计算 (实际应该更复杂)
    const totalPremium = premium * period
    const years = 20
    const futureValue = totalPremium * Math.pow(1 + expectedReturn / 100, years)
    
    const formattedValue = this.formatCurrency(futureValue)
    this.setData({
      calculatedValue: formattedValue
    })
  },

  // 格式化货币
  formatCurrency(amount) {
    if (amount >= 10000) {
      return Math.round(amount / 10000) + '万'
    }
    return Math.round(amount).toLocaleString()
  },

  // 格式化日期
  formatDate(dateString) {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = now - date
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) {
      return '今天'
    } else if (diffDays === 1) {
      return '昨天'
    } else if (diffDays < 7) {
      return `${diffDays}天前`
    } else {
      return date.toLocaleDateString()
    }
  },

  // 收藏/取消收藏
  onCollectTap() {
    const collections = wx.getStorageSync('collections') || []
    const isCollected = collections.includes(this.productId)
    
    if (isCollected) {
      // 取消收藏
      const index = collections.indexOf(this.productId)
      collections.splice(index, 1)
      app.showToast('已取消收藏')
    } else {
      // 添加收藏
      collections.push(this.productId)
      app.showToast('已添加收藏')
    }
    
    wx.setStorageSync('collections', collections)
    this.setData({
      isCollected: !isCollected
    })
  },

  // 分享
  onShareTap() {
    // 触发分享
    wx.showShareMenu({
      withShareTicket: true
    })
  },

  // 立即咨询
  onConsultTap() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再咨询',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            })
          }
        }
      })
      return
    }

    const { product } = this.data
    wx.navigateTo({
      url: `/pages/services/services?type=consult&productId=${product.id}&productName=${encodeURIComponent(product.name)}`
    })
  },

  // 收藏功能
  async onToggleFavorite() {
    const { product, isFavorited } = this.data

    try {
      if (isFavorited) {
        // 取消收藏
        await app.request({
          url: `/insurance/api/products/${product.id}/unfavorite/`,
          method: 'POST'
        })

        this.setData({ isFavorited: false })
        wx.showToast({ title: '已取消收藏', icon: 'success' })

        // 从本地存储中移除
        this.removeFavoriteFromStorage(product.id)
      } else {
        // 添加收藏
        await app.request({
          url: `/insurance/api/products/${product.id}/favorite/`,
          method: 'POST'
        })

        this.setData({ isFavorited: true })
        wx.showToast({ title: '已添加收藏', icon: 'success' })

        // 添加到本地存储
        this.addFavoriteToStorage(product)
      }
    } catch (error) {
      wx.showToast({ title: '操作失败，请重试', icon: 'none' })
    }
  },

  // 比较功能
  onToggleCompare() {
    const { product, isInCompare } = this.data

    if (isInCompare) {
      // 从比较列表中移除
      this.removeFromCompare(product.id)
      this.setData({ isInCompare: false })
      wx.showToast({ title: '已从比较列表移除', icon: 'success' })
    } else {
      // 添加到比较列表
      const compareList = this.getCompareList()

      if (compareList.length >= 3) {
        wx.showModal({
          title: '提示',
          content: '最多只能比较3个产品，请先移除其他产品',
          showCancel: false
        })
        return
      }

      this.addToCompare(product)
      this.setData({ isInCompare: true })
      wx.showToast({ title: '已添加到比较列表', icon: 'success' })
    }
  },

  // 分享功能
  onShare() {
    wx.showActionSheet({
      itemList: ['分享给朋友', '分享到朋友圈', '复制链接'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 分享给朋友 - 触发系统分享
            break
          case 1:
            // 分享到朋友圈 - 触发系统分享
            break
          case 2:
            // 复制链接
            wx.setClipboardData({
              data: `https://baoxian.weixinjishu.top/product/${this.data.product.id}`,
              success: () => {
                wx.showToast({ title: '链接已复制', icon: 'success' })
              }
            })
            break
        }
      }
    })
  },

  // 本地存储管理
  addFavoriteToStorage(product) {
    let favorites = wx.getStorageSync('favorite_products') || []
    const exists = favorites.find(item => item.id === product.id)

    if (!exists) {
      favorites.unshift({
        id: product.id,
        name: product.name,
        subtitle: product.subtitle,
        min_premium: product.min_premium,
        currency: product.currency,
        expected_return: product.expected_return,
        main_image: product.main_image,
        created_at: new Date().toISOString()
      })

      // 最多保存50个收藏
      favorites = favorites.slice(0, 50)
      wx.setStorageSync('favorite_products', favorites)
    }
  },

  removeFavoriteFromStorage(productId) {
    let favorites = wx.getStorageSync('favorite_products') || []
    favorites = favorites.filter(item => item.id !== productId)
    wx.setStorageSync('favorite_products', favorites)
  },

  addToCompare(product) {
    let compareList = this.getCompareList()
    const exists = compareList.find(item => item.id === product.id)

    if (!exists) {
      compareList.push({
        id: product.id,
        name: product.name,
        subtitle: product.subtitle,
        min_premium: product.min_premium,
        currency: product.currency,
        expected_return: product.expected_return,
        main_image: product.main_image,
        category: product.category
      })

      wx.setStorageSync('compare_products', compareList)
    }
  },

  removeFromCompare(productId) {
    let compareList = this.getCompareList()
    compareList = compareList.filter(item => item.id !== productId)
    wx.setStorageSync('compare_products', compareList)
  },

  getCompareList() {
    return wx.getStorageSync('compare_products') || []
  },

  // 检查收藏和比较状态
  checkFavoriteStatus() {
    const favorites = wx.getStorageSync('favorite_products') || []
    const isFavorited = favorites.some(item => item.id === this.data.product.id)
    this.setData({ isFavorited })
  },

  checkCompareStatus() {
    const compareList = this.getCompareList()
    const isInCompare = compareList.some(item => item.id === this.data.product.id)
    this.setData({ isInCompare })
  },

  // 分享给朋友
  onShareAppMessage() {
    const { product } = this.data
    return {
      title: `${product.name} - 香港保险产品`,
      path: `/pages/product-detail/product-detail?id=${this.productId}`,
      imageUrl: product.main_image || '/images/share-product.jpg'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    const { product } = this.data
    return {
      title: `${product.name} - 香港保险产品`,
      imageUrl: product.main_image || '/images/share-product.jpg'
    }
  },

  // 查看全部评价
  onViewAllReviews() {
    wx.showToast({
      title: '查看全部评价功能开发中...',
      icon: 'none'
    });
  },

  // 底部操作栏方法
  onAddToCompare() {
    console.log('加入对比:', this.data.product)

    // 获取对比列表
    let compareList = wx.getStorageSync('compareList') || []

    // 检查是否已在对比列表中
    const existIndex = compareList.findIndex(item => item.id === this.data.product.id)

    if (existIndex > -1) {
      wx.showToast({
        title: '已在对比列表中',
        icon: 'none'
      })
      return
    }

    // 限制对比数量
    if (compareList.length >= 3) {
      wx.showToast({
        title: '最多只能对比3个产品',
        icon: 'none'
      })
      return
    }

    // 添加到对比列表
    compareList.push(this.data.product)
    wx.setStorageSync('compareList', compareList)

    wx.showToast({
      title: '已加入对比',
      icon: 'success'
    })
  },

  onPurchaseNow() {
    console.log('立即投保:', this.data.product)

    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再投保',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            })
          }
        }
      })
      return
    }

    // 跳转到购买页面
    wx.navigateTo({
      url: `/pages/purchase/purchase?id=${this.data.product.id}&premium=${this.data.premiumOptions[this.data.premiumIndex].value}&period=${this.data.product.payment_periods[this.data.periodIndex]}&age=${this.data.ageRanges[this.data.ageIndex]}`
    })
  }
})
