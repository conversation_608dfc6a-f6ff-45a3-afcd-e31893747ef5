<!--pages/payment-success/payment-success.wxml-->
<view class="container">
  <!-- 成功状态 -->
  <view class="success-section">
    <view class="success-icon">✅</view>
    <text class="success-title">支付成功</text>
    <text class="success-desc">恭喜您，保险购买成功！</text>
    
    <view class="order-summary">
      <view class="summary-item">
        <text class="summary-label">订单号</text>
        <text class="summary-value">{{orderInfo.orderNumber}}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">支付金额</text>
        <text class="summary-value amount">¥{{orderInfo.paidAmount}}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">支付时间</text>
        <text class="summary-value">{{orderInfo.payTime}}</text>
      </view>
    </view>
  </view>

  <!-- 保单信息 -->
  <view class="section">
    <text class="section-title">保单信息</text>
    <view class="policy-card">
      <view class="policy-header">
        <text class="policy-name">{{orderInfo.productName}}</text>
        <view class="policy-status">已生效</view>
      </view>
      
      <view class="policy-details">
        <view class="detail-row">
          <text class="detail-label">保单号</text>
          <text class="detail-value">{{orderInfo.policyNumber}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">被保险人</text>
          <text class="detail-value">{{orderInfo.insuredName}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">保障期间</text>
          <text class="detail-value">{{orderInfo.coveragePeriod}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">生效时间</text>
          <text class="detail-value">{{orderInfo.effectiveTime}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 下一步操作 -->
  <view class="section">
    <text class="section-title">接下来您可以</text>
    <view class="action-cards">
      <view class="action-card" bindtap="onActionTap" data-action="download">
        <view class="action-icon download-icon">📄</view>
        <view class="action-content">
          <text class="action-title">下载保单</text>
          <text class="action-desc">获取电子保单PDF</text>
        </view>
        <text class="arrow-icon">></text>
      </view>
      
      <view class="action-card" bindtap="onActionTap" data-action="share">
        <view class="action-icon share-icon">📤</view>
        <view class="action-content">
          <text class="action-title">分享保单</text>
          <text class="action-desc">分享给家人朋友</text>
        </view>
        <text class="arrow-icon">></text>
      </view>
      
      <view class="action-card" bindtap="onActionTap" data-action="service">
        <view class="action-icon service-icon">🎧</view>
        <view class="action-content">
          <text class="action-title">联系客服</text>
          <text class="action-desc">专业顾问为您服务</text>
        </view>
        <text class="arrow-icon">></text>
      </view>
    </view>
  </view>

  <!-- 重要提醒 -->
  <view class="section">
    <view class="reminder-card">
      <view class="reminder-header">
        <text class="reminder-icon">💡</text>
        <text class="reminder-title">重要提醒</text>
      </view>
      <view class="reminder-content">
        <text class="reminder-item">• 请妥善保管您的保单号码</text>
        <text class="reminder-item">• 如需理赔，请及时联系我们</text>
        <text class="reminder-item">• 保单详情可在"我的保单"中查看</text>
        <text class="reminder-item">• 如有疑问，请联系专属顾问</text>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <button class="action-btn secondary" bindtap="onViewPolicy">查看保单</button>
    <button class="action-btn primary" bindtap="onBackHome">返回首页</button>
  </view>
</view>
