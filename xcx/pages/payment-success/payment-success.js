// pages/payment-success/payment-success.js
Page({
  data: {
    orderInfo: {
      orderNumber: 'HK2024032100001',
      paidAmount: '135.00',
      payTime: '2024-03-21 14:30:25',
      productName: '欧洲旅游保险计划A',
      policyNumber: 'POL2024032100001',
      insuredName: '张小明',
      coveragePeriod: '2024-03-15 至 2024-03-20 (5天)',
      effectiveTime: '2024-03-15 00:00:00'
    }
  },

  onLoad(options) {
    console.log('支付成功页面加载', options);
    
    // 如果有传入的订单信息，更新数据
    if (options.orderData) {
      try {
        const orderData = JSON.parse(decodeURIComponent(options.orderData));
        this.setData({
          orderInfo: { ...this.data.orderInfo, ...orderData }
        });
      } catch (e) {
        console.error('解析订单数据失败', e);
      }
    }

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '支付成功'
    });
  },

  onShow() {
    // 禁用返回按钮，防止用户返回到支付页面
    wx.hideHomeButton();
  },

  // 操作卡片点击
  onActionTap(e) {
    const action = e.currentTarget.dataset.action;
    
    switch (action) {
      case 'download':
        this.downloadPolicy();
        break;
      case 'share':
        this.sharePolicy();
        break;
      case 'service':
        this.contactService();
        break;
    }
  },

  // 下载保单
  downloadPolicy() {
    wx.showLoading({
      title: '生成保单中...'
    });

    // 模拟下载过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '保单已保存到相册',
        icon: 'success'
      });
    }, 2000);
  },

  // 分享保单
  sharePolicy() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  // 联系客服
  contactService() {
    wx.makePhoneCall({
      phoneNumber: '4008880000',
      fail: () => {
        wx.showToast({
          title: '拨打失败，请手动拨打',
          icon: 'none'
        });
      }
    });
  },

  // 查看保单
  onViewPolicy() {
    wx.switchTab({
      url: '/pages/profile/profile'
    });
  },

  // 返回首页
  onBackHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },

  // 分享
  onShareAppMessage() {
    const { orderInfo } = this.data;
    return {
      title: `我刚购买了${orderInfo.productName}，安心出行！`,
      path: '/pages/home/<USER>',
      imageUrl: '/images/share-success.jpg'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    const { orderInfo } = this.data;
    return {
      title: `我刚购买了${orderInfo.productName}，安心出行！`,
      imageUrl: '/images/share-success.jpg'
    };
  }
});
