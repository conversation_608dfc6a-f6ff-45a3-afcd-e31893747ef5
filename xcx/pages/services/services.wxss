/* pages/services/services.wxss */

/* 页面容器 */
.container {
  background: #F5F5F5;
  min-height: 100vh;
}

/* 服务中心头部 */
.service-header {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  color: white;
  padding: 48rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.header-icon {
  font-size: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-content {
  flex: 1;
}

.header-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: white;
}

.header-subtitle {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
  color: white;
}

/* 紧急服务 */
.emergency-section {
  padding: 32rpx;
  background: white;
  margin-bottom: 16rpx;
}

.section-title-with-icon {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.emergency-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1F2937;
}

.emergency-buttons {
  display: flex;
  gap: 24rpx;
}

.emergency-btn {
  flex: 1;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  border: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;
  color: white;
}

.emergency-call {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
}

.medical-assist {
  background: linear-gradient(135deg, #F97316 0%, #EA580C 100%);
}

.emergency-btn:active {
  transform: scale(0.95);
}

.btn-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.btn-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}

.btn-subtitle {
  font-size: 22rpx;
  opacity: 0.9;
  display: block;
}

/* 实用工具 */
.tools-section {
  padding: 32rpx;
  background: white;
  margin-bottom: 16rpx;
}

.tools-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.tool-item {
  background: #F8F9FA;
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.tool-item:active {
  transform: scale(0.95);
  background: #E9ECEF;
}

.tool-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin: 0 auto 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: white;
}

.tool-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 8rpx;
  display: block;
}

.tool-desc {
  font-size: 22rpx;
  color: #6B7280;
  margin-bottom: 16rpx;
  display: block;
}

.tool-btn {
  background: #3B82F6;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 22rpx;
  font-weight: 500;
}

/* 通用颜色背景 */
.blue-bg {
  background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%);
}

.green-bg {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
}

.purple-bg {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
}

.orange-bg {
  background: linear-gradient(135deg, #F97316 0%, #EA580C 100%);
}

.red-bg {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
}

/* 当前位置服务 */
.location-section {
  padding: 32rpx;
  background: white;
  margin-bottom: 16rpx;
}

.location-services {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.location-item {
  background: #F8F9FA;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  transition: all 0.3s ease;
}

.location-item:active {
  transform: scale(0.98);
  background: #E9ECEF;
}

.location-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: white;
  flex-shrink: 0;
}

.location-info {
  flex: 1;
}

.location-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 4rpx;
  display: block;
}

.location-desc {
  font-size: 22rpx;
  color: #6B7280;
  display: block;
}

.location-btn {
  background: #3B82F6;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

/* 银行客服 */
.bank-service-section {
  padding: 32rpx;
  background: white;
  margin-bottom: 16rpx;
}

.bank-services {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.bank-item {
  background: #F8F9FA;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  transition: all 0.3s ease;
}

.bank-item:active {
  transform: scale(0.98);
  background: #E9ECEF;
}

.bank-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: white;
  flex-shrink: 0;
}

.bank-info {
  flex: 1;
}

.bank-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 4rpx;
  display: block;
}

.bank-desc {
  font-size: 22rpx;
  color: #6B7280;
  display: block;
}

.bank-btn {
  background: #F97316;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

/* 指南助手 */
.guide-section {
  padding: 32rpx;
  background: white;
  margin-bottom: 16rpx;
}

.guide-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.guide-item {
  background: #F8F9FA;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  transition: all 0.3s ease;
}

.guide-item:active {
  transform: scale(0.98);
  background: #E9ECEF;
}

.guide-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: white;
  flex-shrink: 0;
}

.guide-content {
  flex: 1;
}

.guide-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 4rpx;
  display: block;
}

.guide-desc {
  font-size: 22rpx;
  color: #6B7280;
  display: block;
}

.arrow-icon {
  font-size: 24rpx;
  color: #9CA3AF;
}

/* 客服支持 */
.support-section {
  padding: 32rpx;
  background: white;
  margin-bottom: 16rpx;
}

.support-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.support-item {
  background: #F8F9FA;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  transition: all 0.3s ease;
}

.support-item:active {
  transform: scale(0.98);
  background: #E9ECEF;
}

.support-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: white;
  flex-shrink: 0;
}

.support-content {
  flex: 1;
}

.support-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 4rpx;
  display: block;
}

.support-desc {
  font-size: 22rpx;
  color: #6B7280;
  display: block;
}

.support-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.online-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #10B981;
}

.status-text {
  font-size: 22rpx;
  color: #10B981;
  font-weight: 500;
}

.call-btn {
  background: #10B981;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

/* 紧急求助 */
.emergency-help-section {
  padding: 32rpx;
  background: white;
  margin-bottom: 16rpx;
}

.emergency-help-card {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  color: white;
}

.emergency-help-content {
  flex: 1;
}

.emergency-help-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}

.emergency-help-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
  display: block;
}

.emergency-help-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 24rpx;
  padding: 20rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 24rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.emergency-help-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.help-icon {
  font-size: 32rpx;
}

.help-text {
  font-size: 24rpx;
}
