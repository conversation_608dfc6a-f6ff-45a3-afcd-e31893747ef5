// pages/services/services.js
const app = getApp()

Page({
  data: {
    currentTab: 'tools', // 默认显示实用工具标签页
    currentLocation: '香港特别行政区',
    
    // 工具服务
    tools: [
      { id: 'hospital', title: '医院查询', desc: '查找附近医院信息', icon: '📊' },
      { id: 'consulate', title: '领事馆信息', desc: '中国领事馆', icon: '🏛️' },
      { id: 'exchange', title: '汇率查询', desc: '实时汇率', icon: '💱' },
      { id: 'weather', title: '天气预报', desc: '目的地天气', icon: '🌤️' }
    ],
    
    // 位置服务
    locationServices: [
      { id: 'hospital', title: '最近医院', desc: '距离 1.2km', icon: '🏥' },
      { id: 'consulate', title: '中国领事馆', desc: '距离 3.5km', icon: '🏛️' },
      { id: 'pharmacy', title: '最近药店', desc: '距离 0.8km', icon: '💊' },
      { id: 'emergency', title: '报警电话', desc: '110 (中国)', icon: '📞' }
    ],
    
    // 银行服务
    bankServices: [
      { id: 'icbc', title: '工商银行', desc: '95588 - 24小时', phone: '95588' },
      { id: 'boc', title: '中国银行', desc: '95566 - 24小时', phone: '95566' },
      { id: 'ccb', title: '建设银行', desc: '95533 - 24小时', phone: '95533' },
      { id: 'abc', title: '农业银行', desc: '95599 - 24小时', phone: '95599' }
    ],
    
    // 指南助手
    guides: [
      { id: 'newbie', title: '新手指导', desc: '保险新手必看指南', icon: '📘' },
      { id: 'claims', title: '理赔指导', desc: '理赔流程详细说明', icon: '📋' },
      { id: 'medical', title: '就医指导', desc: '海外就医指南', icon: '🏥' },
      { id: 'faq', title: '常见问题', desc: 'FAQ帮助中心', icon: '❓' }
    ],
    
    // 客服支持
    supportServices: [
      { id: 'online', title: '在线客服', desc: '即时聊天支持', icon: '💬', status: 'online' },
      { id: 'phone', title: '客服热线', desc: '400-1234-5678', icon: '📞', phone: '400-1234-5678' },
      { id: 'email', title: '邮件支持', desc: '<EMAIL>', icon: '✉️' },
      { id: 'feedback', title: '意见反馈', desc: '您的建议很重要', icon: '📝' }
    ],

    // 咨询表单
    consultTypes: [
      { id: 'product', name: '产品咨询' },
      { id: 'claim', name: '理赔咨询' },
      { id: 'service', name: '服务咨询' },
      { id: 'complaint', name: '投诉建议' }
    ],
    consultTypeIndex: 0,
    consultForm: {
      name: '',
      phone: '',
      message: ''
    }
  },

  onLoad() {
    console.log('服务中心页面加载');
  },

  // 标签页切换
  onTabTap(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      currentTab: tab
    });
  },

  // 咨询类型选择
  onConsultTypeChange(e) {
    this.setData({
      consultTypeIndex: e.detail.value
    });
  },

  // 咨询表单输入
  onConsultInputChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`consultForm.${field}`]: value
    });
  },

  // 提交咨询
  onSubmitConsult() {
    const { consultForm, consultTypes, consultTypeIndex } = this.data;

    if (!consultForm.name.trim()) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      });
      return;
    }

    if (!consultForm.phone.trim()) {
      wx.showToast({
        title: '请输入联系电话',
        icon: 'none'
      });
      return;
    }

    if (!consultForm.message.trim()) {
      wx.showToast({
        title: '请输入咨询内容',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '提交中...'
    });

    // 模拟提交
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '提交成功',
        icon: 'success'
      });

      // 重置表单
      this.setData({
        consultForm: {
          name: '',
          phone: '',
          message: ''
        },
        consultTypeIndex: 0
      });
    }, 1500);
  },

  onShow() {
    // 页面显示时刷新位置信息
    this.updateLocation()
  },

  // 紧急救援
  onEmergencyCall() {
    wx.showModal({
      title: '紧急救援',
      content: '是否拨打24小时紧急救援热线？',
      confirmText: '拨打',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '+852-1234-5678',
            fail: () => {
              wx.showToast({
                title: '拨号失败',
                icon: 'none'
              })
            }
          })
        }
      }
    })
  },

  // 医疗协助
  onMedicalAssist() {
    wx.showModal({
      title: '医疗协助',
      content: '是否需要医疗协助服务？我们将为您提供就医指导。',
      confirmText: '需要',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '+852-2345-6789',
            fail: () => {
              wx.showToast({
                title: '拨号失败',
                icon: 'none'
              })
            }
          })
        }
      }
    })
  },

  // 工具查询
  onHospitalQuery() {
    wx.showToast({
      title: '正在查找附近医院',
      icon: 'loading'
    })
    // 这里可以调用地图API或跳转到医院查询页面
  },

  onConsulateInfo() {
    wx.showToast({
      title: '正在获取领事馆信息',
      icon: 'loading'
    })
  },

  onExchangeRate() {
    wx.showToast({
      title: '正在获取实时汇率',
      icon: 'loading'
    })
  },

  onWeatherQuery() {
    wx.showToast({
      title: '正在获取天气信息',
      icon: 'loading'
    })
  },

  // 位置服务
  onLocationService(e) {
    const type = e.currentTarget.dataset.type
    switch (type) {
      case 'hospital':
        wx.showToast({
          title: '正在导航到最近医院',
          icon: 'loading'
        })
        break
      case 'consulate':
        wx.showToast({
          title: '正在获取领事馆信息',
          icon: 'loading'
        })
        break
      case 'pharmacy':
        wx.showToast({
          title: '正在导航到最近药店',
          icon: 'loading'
        })
        break
      case 'emergency':
        wx.makePhoneCall({
          phoneNumber: '110',
          fail: () => {
            wx.showToast({
              title: '拨号失败',
              icon: 'none'
            })
          }
        })
        break
    }
  },

  // 银行服务
  onBankService(e) {
    const type = e.currentTarget.dataset.type
    const bankServices = {
      'icbc': '95588',
      'boc': '95566', 
      'ccb': '95533',
      'abc': '95599'
    }
    
    const phone = bankServices[type]
    if (phone) {
      wx.showModal({
        title: '拨打银行客服',
        content: `是否拨打${phone}？`,
        confirmText: '拨打',
        success: (res) => {
          if (res.confirm) {
            wx.makePhoneCall({
              phoneNumber: phone,
              fail: () => {
                wx.showToast({
                  title: '拨号失败',
                  icon: 'none'
                })
              }
            })
          }
        }
      })
    }
  },

  // 指南助手
  onGuideItem(e) {
    const type = e.currentTarget.dataset.type
    let title = ''
    let content = ''

    switch (type) {
      case 'newbie':
        title = '新手指导'
        content = '保险新手必看指南，包含产品介绍、购买流程等'
        break
      case 'claims':
        title = '理赔指导'
        content = '详细的理赔流程说明，帮助您快速完成理赔'
        break
      case 'medical':
        title = '就医指导'
        content = '海外就医指南，包含医院选择、费用报销等'
        break
      case 'faq':
        title = '常见问题'
        content = 'FAQ帮助中心，解答常见疑问'
        break
    }

    wx.showModal({
      title: title,
      content: content,
      showCancel: false,
      confirmText: '我知道了'
    })
  },

  // 客服支持
  onSupportItem(e) {
    const type = e.currentTarget.dataset.type
    
    switch (type) {
      case 'online':
        wx.showToast({
          title: '正在连接在线客服',
          icon: 'loading'
        })
        break
      case 'phone':
        wx.makePhoneCall({
          phoneNumber: '400-1234-5678',
          fail: () => {
            wx.showToast({
              title: '拨号失败',
              icon: 'none'
            })
          }
        })
        break
      case 'email':
        wx.setClipboardData({
          data: '<EMAIL>',
          success: () => {
            wx.showToast({
              title: '邮箱地址已复制',
              icon: 'success'
            })
          }
        })
        break
      case 'feedback':
        wx.showToast({
          title: '意见反馈功能开发中',
          icon: 'none'
        })
        break
    }
  },

  // 紧急求助
  onEmergencyHelp() {
    wx.showModal({
      title: '紧急求助',
      content: '是否拨打24小时全球救援服务热线？',
      confirmText: '立即拨打',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '+852-9999-8888',
            fail: () => {
              wx.showToast({
                title: '拨号失败',
                icon: 'none'
              })
            }
          })
        }
      }
    })
  },

  // 更新位置
  updateLocation() {
    // 先检查位置权限
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.userLocation']) {
          // 已授权，直接获取位置
          this.getLocationInfo()
        } else {
          // 未授权，请求授权
          wx.authorize({
            scope: 'scope.userLocation',
            success: () => {
              this.getLocationInfo()
            },
            fail: () => {
              console.log('用户拒绝位置授权')
              // 可以显示默认位置信息
              this.setData({
                currentLocation: '香港特别行政区'
              })
            }
          })
        }
      }
    })
  },

  // 获取位置信息
  getLocationInfo() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        console.log('当前位置:', res)
        // 这里可以根据经纬度获取具体位置信息
        // 暂时使用默认位置
        this.setData({
          currentLocation: '香港特别行政区'
        })
      },
      fail: (err) => {
        console.log('获取位置失败:', err)
        this.setData({
          currentLocation: '香港特别行政区'
        })
      }
    })
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '香港保险服务中心 - 全球贴心服务',
      path: '/pages/services/services',
      imageUrl: '/images/share-services.jpg'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '香港保险服务中心 - 全球贴心服务',
      imageUrl: '/images/share-services.jpg'
    }
  }
})
