<!--pages/services/services.wxml-->
<view class="container">
  <!-- 服务头部 -->
  <view class="service-header">
    <view class="header-content">
      <view class="service-icon">🛎️</view>
      <text class="header-title">全球贴心服务</text>
      <text class="header-subtitle">24小时为您的海外之旅保驾护航</text>
    </view>
  </view>
  
  <!-- 紧急服务 -->
  <view class="emergency-services">
    <view class="section-title-with-icon">
      <text class="emergency-icon">⚠️</text>
      <text class="section-title">紧急服务</text>
    </view>
    <view class="emergency-buttons">
      <button class="emergency-btn emergency-rescue" bindtap="onEmergencyCall">
        <view class="btn-icon">📞</view>
        <text class="btn-title">紧急救援</text>
        <text class="btn-subtitle">24小时热线</text>
      </button>
      <button class="emergency-btn medical-assist" bindtap="onMedicalAssist">
        <view class="btn-icon">🏥</view>
        <text class="btn-title">医疗协助</text>
        <text class="btn-subtitle">就医指导</text>
      </button>
    </view>
  </view>
  
  <!-- 服务导航 -->
  <view class="service-nav">
    <scroll-view class="nav-scroll" scroll-x="true">
      <view class="nav-item {{currentTab === 'tools' ? 'active' : ''}}" bindtap="onTabTap" data-tab="tools">
        <text class="nav-icon">🔧</text>
        <text class="nav-text">实用工具</text>
      </view>
      <view class="nav-item {{currentTab === 'consult' ? 'active' : ''}}" bindtap="onTabTap" data-tab="consult">
        <text class="nav-icon">💬</text>
        <text class="nav-text">在线咨询</text>
      </view>
      <view class="nav-item {{currentTab === 'travel' ? 'active' : ''}}" bindtap="onTabTap" data-tab="travel">
        <text class="nav-icon">✈️</text>
        <text class="nav-text">旅行助手</text>
      </view>
      <view class="nav-item {{currentTab === 'support' ? 'active' : ''}}" bindtap="onTabTap" data-tab="support">
        <text class="nav-icon">🎧</text>
        <text class="nav-text">客服支持</text>
      </view>
    </scroll-view>
  </view>

  <!-- 实用工具 -->
  <view class="tab-content" wx:if="{{currentTab === 'tools'}}">
    <view class="tools-grid">
      <view class="tool-item" bindtap="onHospitalQuery">
        <view class="tool-icon hospital-icon">🏥</view>
        <text class="tool-title">医院查询</text>
        <text class="tool-desc">查找附近医院</text>
        <button class="tool-btn">立即查询</button>
      </view>
      
      <view class="tool-item" bindtap="onConsulateInfo">
        <view class="tool-icon consulate-icon">🏛️</view>
        <text class="tool-title">领事馆信息</text>
        <text class="tool-desc">中国领事馆</text>
        <button class="tool-btn">查看详情</button>
      </view>
      
      <view class="tool-item" bindtap="onExchangeRate">
        <view class="tool-icon exchange-icon">💱</view>
        <text class="tool-title">汇率查询</text>
        <text class="tool-desc">实时汇率</text>
        <button class="tool-btn">查看汇率</button>
      </view>
      
      <view class="tool-item" bindtap="onWeatherQuery">
        <view class="tool-icon weather-icon">🌤️</view>
        <text class="tool-title">天气预报</text>
        <text class="tool-desc">目的地天气</text>
        <button class="tool-btn">查看天气</button>
      </view>

      <view class="tool-item" bindtap="onCustomerService">
        <view class="tool-icon service-icon">💬</view>
        <text class="tool-title">在线客服</text>
        <text class="tool-desc">24小时在线</text>
        <button class="tool-btn">立即咨询</button>
      </view>

      <view class="tool-item" bindtap="onAppointmentConsult">
        <view class="tool-icon appointment-icon">📅</view>
        <text class="tool-title">预约咨询</text>
        <text class="tool-desc">专家一对一</text>
        <button class="tool-btn">立即预约</button>
      </view>
    </view>
    
    <!-- 当前位置服务 -->
    <view class="location-service-section">
      <text class="section-title">当前位置服务</text>
      <view class="location-service">
        <view class="location-header">
          <view class="location-info">
            <text class="location-icon">📍</text>
            <text class="location-text">{{currentLocation}}</text>
          </view>
          <button class="update-location-btn" bindtap="onUpdateLocation">更新位置</button>
        </view>
        
        <view class="location-services-grid">
          <view class="location-service-item">
            <view class="service-icon-bg hospital-bg">
              <text class="service-icon">🏥</text>
            </view>
            <text class="service-title">最近医院</text>
            <text class="service-distance">距离 1.2km</text>
          </view>
          
          <view class="location-service-item">
            <view class="service-icon-bg consulate-bg">
              <text class="service-icon">🏛️</text>
            </view>
            <text class="service-title">中国领事馆</text>
            <text class="service-distance">距离 3.5km</text>
          </view>
          
          <view class="location-service-item">
            <view class="service-icon-bg pharmacy-bg">
              <text class="service-icon">💊</text>
            </view>
            <text class="service-title">最近药店</text>
            <text class="service-distance">距离 0.8km</text>
          </view>
          
          <view class="location-service-item">
            <view class="service-icon-bg emergency-bg">
              <text class="service-icon">📞</text>
            </view>
            <text class="service-title">报警电话</text>
            <text class="service-distance">110 (中国)</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 在线咨询 -->
  <view class="tab-content" wx:if="{{currentTab === 'consult'}}">
    <view class="consult-header">
      <text class="section-title">在线咨询</text>
      <text class="section-desc">专业顾问为您提供一对一咨询服务</text>
    </view>

    <view class="consult-form">
      <view class="form-group">
        <text class="form-label">咨询类型</text>
        <picker bindchange="onConsultTypeChange" value="{{consultTypeIndex}}" range="{{consultTypes}}" range-key="name">
          <view class="picker-input">
            <text class="{{consultTypeIndex === 0 ? 'placeholder' : ''}}">{{consultTypes[consultTypeIndex].name}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <view class="form-group">
        <text class="form-label">您的姓名</text>
        <input class="form-input" placeholder="请输入您的姓名" value="{{consultForm.name}}" bindinput="onConsultInputChange" data-field="name" />
      </view>

      <view class="form-group">
        <text class="form-label">联系电话</text>
        <input class="form-input" placeholder="请输入手机号" type="number" value="{{consultForm.phone}}" bindinput="onConsultInputChange" data-field="phone" />
      </view>

      <view class="form-group">
        <text class="form-label">咨询内容</text>
        <textarea class="form-textarea" placeholder="请详细描述您的问题或需求" value="{{consultForm.message}}" bindinput="onConsultInputChange" data-field="message"></textarea>
      </view>

      <button class="submit-btn" bindtap="onSubmitConsult">提交咨询</button>
    </view>
  </view>

  <!-- 旅行助手 -->
  <view class="tab-content" wx:if="{{currentTab === 'travel'}}">
    <view class="travel-assistant">
      <view class="assistant-item" bindtap="onTranslator">
        <view class="assistant-icon">🌐</view>
        <view class="assistant-content">
          <text class="assistant-title">翻译助手</text>
          <text class="assistant-desc">多语言实时翻译</text>
        </view>
        <text class="arrow-icon">></text>
      </view>
      
      <view class="assistant-item" bindtap="onMapNavigation">
        <view class="assistant-icon">🗺️</view>
        <view class="assistant-content">
          <text class="assistant-title">地图导航</text>
          <text class="assistant-desc">离线地图下载</text>
        </view>
        <text class="arrow-icon">></text>
      </view>
      
      <view class="assistant-item" bindtap="onFoodRecommend">
        <view class="assistant-icon">🍽️</view>
        <view class="assistant-content">
          <text class="assistant-title">美食推荐</text>
          <text class="assistant-desc">当地特色餐厅</text>
        </view>
        <text class="arrow-icon">></text>
      </view>
      
      <view class="assistant-item" bindtap="onAttractionRecommend">
        <view class="assistant-icon">📸</view>
        <view class="assistant-content">
          <text class="assistant-title">景点推荐</text>
          <text class="assistant-desc">热门旅游景点</text>
        </view>
        <text class="arrow-icon">></text>
      </view>
    </view>
  </view>

  <!-- 客服支持 -->
  <view class="tab-content" wx:if="{{currentTab === 'support'}}">
    <view class="customer-support">
      <view class="support-item" bindtap="onOnlineChat">
        <view class="support-icon">💬</view>
        <view class="support-content">
          <text class="support-title">在线客服</text>
          <text class="support-desc">即时聊天支持</text>
        </view>
        <view class="support-status">
          <view class="online-dot"></view>
          <text class="status-text">在线</text>
          <text class="arrow-icon">></text>
        </view>
      </view>
      
      <view class="support-item" bindtap="onCallService">
        <view class="support-icon">📞</view>
        <view class="support-content">
          <text class="support-title">客服热线</text>
          <text class="support-desc">+852 1234 5678</text>
        </view>
        <button class="call-btn">📞 拨打</button>
      </view>
      
      <view class="support-item" bindtap="onEmailSupport">
        <view class="support-icon">✉️</view>
        <view class="support-content">
          <text class="support-title">邮件支持</text>
          <text class="support-desc"><EMAIL></text>
        </view>
        <text class="arrow-icon">></text>
      </view>
      
      <view class="support-item" bindtap="onFAQ">
        <view class="support-icon">❓</view>
        <view class="support-content">
          <text class="support-title">常见问题</text>
          <text class="support-desc">FAQ帮助中心</text>
        </view>
        <text class="arrow-icon">></text>
      </view>
    </view>
    
    <!-- 紧急联系卡片 -->
    <view class="emergency-contact">
      <view class="emergency-card">
        <view class="emergency-content">
          <text class="emergency-title">紧急情况？</text>
          <text class="emergency-subtitle">24小时全球救援服务</text>
        </view>
        <button class="emergency-help-btn" bindtap="onEmergencyHelp">
          <text class="help-icon">📞</text>
          <text class="help-text">立即求助</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 底部安全间距 -->
  <view class="bottom-safe-area"></view>
</view>
