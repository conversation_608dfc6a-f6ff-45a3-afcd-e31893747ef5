<!--pages/claims/claims.wxml-->
<view class="container">
  <!-- 理赔服务头部 -->
  <view class="claims-header">
    <view class="header-content">
      <view class="header-icon">🏥</view>
      <text class="header-title">24小时快速理赔</text>
      <text class="header-subtitle">专业团队为您提供贴心服务</text>
    </view>

    <view class="service-features">
      <view class="feature-item">
        <view class="feature-icon">⏰</view>
        <text class="feature-title">快速响应</text>
        <text class="feature-desc">1小时内</text>
      </view>
      <view class="feature-item">
        <view class="feature-icon">📱</view>
        <text class="feature-title">在线申请</text>
        <text class="feature-desc">随时随地</text>
      </view>
      <view class="feature-item">
        <view class="feature-icon">💰</view>
        <text class="feature-title">快速赔付</text>
        <text class="feature-desc">3个工作日</text>
      </view>
    </view>
  </view>

  <!-- 理赔指南 -->
  <view class="guide-card">
    <view class="guide-header">
      <text class="guide-title">理赔指南</text>
      <text class="guide-icon">📋</text>
    </view>
    <view class="guide-steps">
      <view class="guide-step">
        <view class="step-number">1</view>
        <text class="step-text">及时报案</text>
      </view>
      <view class="step-arrow">→</view>
      <view class="guide-step">
        <view class="step-number">2</view>
        <text class="step-text">准备材料</text>
      </view>
      <view class="step-arrow">→</view>
      <view class="guide-step">
        <view class="step-number">3</view>
        <text class="step-text">提交申请</text>
      </view>
      <view class="step-arrow">→</view>
      <view class="guide-step">
        <view class="step-number">4</view>
        <text class="step-text">审核赔付</text>
      </view>
    </view>
  </view>

  <!-- 快速理赔 -->
  <view class="quick-claim-card">
    <text class="card-title">快速理赔</text>
    <view class="claim-options">
      <view class="claim-option" bindtap="onClaimTypeTap" data-type="medical">
        <view class="option-icon medical">🏥</view>
        <text class="option-title">医疗理赔</text>
        <text class="option-desc">住院、门诊费用报销</text>
      </view>
      <view class="claim-option" bindtap="onClaimTypeTap" data-type="critical">
        <view class="option-icon critical">🛡️</view>
        <text class="option-title">重疾理赔</text>
        <text class="option-desc">重大疾病保险金申请</text>
      </view>
      <view class="claim-option" bindtap="onClaimTypeTap" data-type="accident">
        <view class="option-icon accident">⚡</view>
        <text class="option-title">意外理赔</text>
        <text class="option-desc">意外伤害保险金申请</text>
      </view>
      <view class="claim-option" bindtap="onClaimTypeTap" data-type="death">
        <view class="option-icon death">💐</view>
        <text class="option-title">身故理赔</text>
        <text class="option-desc">身故保险金申请</text>
      </view>
    </view>
  </view>

  <!-- 我的理赔 -->
  <view class="my-claims-card" wx:if="{{userInfo}}">
    <view class="card-header">
      <text class="card-title">我的理赔</text>
      <text class="view-all" bindtap="onViewAllClaims">查看全部</text>
    </view>

    <!-- 状态筛选 -->
    <view class="status-filters">
      <button class="filter-btn {{currentFilter === 'all' ? 'active' : ''}}" bindtap="onFilterTap" data-filter="all">全部</button>
      <button class="filter-btn {{currentFilter === 'processing' ? 'active' : ''}}" bindtap="onFilterTap" data-filter="processing">进行中</button>
      <button class="filter-btn {{currentFilter === 'completed' ? 'active' : ''}}" bindtap="onFilterTap" data-filter="completed">已完成</button>
    </view>
    
    <view class="claims-list" wx:if="{{claims.length > 0}}">
      <view class="claim-card" wx:for="{{claims}}" wx:key="id" bindtap="onClaimTap" data-claim="{{item}}">
        <!-- 理赔卡片头部 -->
        <view class="claim-header">
          <view class="claim-main-info">
            <view class="claim-tags">
              <text class="status-tag status-{{item.status}}">{{claimStatusMap[item.status]}}</text>
              <text class="claim-number-tag">理赔号：{{item.claim_number}}</text>
            </view>
            <text class="claim-title">{{claimTypeMap[item.claim_type]}}</text>
            <text class="claim-time">{{item.status === 'completed' ? '完成时间' : '申请时间'}}：{{item.created_at}}</text>
          </view>
          <view class="claim-arrow">
            <text class="arrow-icon">›</text>
          </view>
        </view>

        <!-- 金额高亮卡片 -->
        <view class="amount-highlight status-{{item.status}}">
          <view class="amount-row">
            <text class="amount-label">{{item.status === 'completed' ? '赔付金额' : '申请金额'}}</text>
            <text class="amount-value">{{item.claim_amount}}{{item.currency || 'HKD'}}</text>
          </view>
        </view>

        <!-- 底部信息和操作 -->
        <view class="claim-footer">
          <view class="status-info">
            <text class="status-icon">{{item.status === 'processing' ? '⏱️' : item.status === 'completed' ? '✅' : '⚠️'}}</text>
            <text class="status-desc">{{item.status === 'processing' ? '预计3个工作日内完成' : item.status === 'completed' ? '理赔已完成' : '请补充相关材料'}}</text>
          </view>
          <button class="detail-btn status-{{item.status}}">查看详情</button>
        </view>
      </view>
    </view>
    
    <view class="empty-claims" wx:else>
      <view class="empty-image-placeholder">
        <text class="placeholder-icon">📋</text>
      </view>
      <text class="empty-text">暂无理赔记录</text>
      <text class="empty-desc">如需理赔，请选择上方理赔类型</text>
    </view>
  </view>

  <!-- 理赔须知 -->
  <view class="notice-card">
    <text class="card-title">理赔须知</text>
    <view class="notice-list">
      <view class="notice-item">
        <view class="notice-icon">⏰</view>
        <view class="notice-content">
          <text class="notice-title">报案时效</text>
          <text class="notice-desc">请在事故发生后30天内及时报案</text>
        </view>
      </view>
      <view class="notice-item">
        <view class="notice-icon">📄</view>
        <view class="notice-content">
          <text class="notice-title">材料准备</text>
          <text class="notice-desc">请准备完整的理赔材料，确保信息真实有效</text>
        </view>
      </view>
      <view class="notice-item">
        <view class="notice-icon">🔍</view>
        <view class="notice-content">
          <text class="notice-title">审核流程</text>
          <text class="notice-desc">我们将在收到完整材料后15个工作日内完成审核</text>
        </view>
      </view>
      <view class="notice-item">
        <view class="notice-icon">💰</view>
        <view class="notice-content">
          <text class="notice-title">赔付方式</text>
          <text class="notice-desc">审核通过后，理赔款将直接转入您的银行账户</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 联系客服 -->
  <view class="contact-card">
    <text class="card-title">需要帮助？</text>
    <view class="contact-options">
      <button class="contact-btn primary" bindtap="onContactService">
        <text class="btn-icon">💬</text>
        <text class="btn-text">在线客服</text>
      </button>
      <button class="contact-btn secondary" bindtap="onCallService">
        <text class="btn-icon">📞</text>
        <text class="btn-text">电话咨询</text>
      </button>
    </view>
  </view>



  <!-- 未登录提示 -->
  <view class="login-prompt-card" wx:if="{{!userInfo}}">
    <view class="prompt-icon">🔐</view>
    <text class="prompt-title">请先登录</text>
    <text class="prompt-desc">登录后可查看您的理赔记录和申请理赔</text>
    <button class="login-btn" bindtap="onLoginTap">立即登录</button>
  </view>

  <!-- 理赔申请弹窗 -->
  <view class="claim-modal" wx:if="{{showClaimModal}}">
    <view class="modal-overlay" bindtap="onCloseClaimModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">{{currentClaimType === 'medical' ? '医疗理赔申请' : '意外理赔申请'}}</text>
        <text class="modal-close" bindtap="onCloseClaimModal">×</text>
      </view>

      <scroll-view class="form-content" scroll-y="true">
        <!-- 基本信息 -->
        <view class="form-section">
          <text class="section-title">基本信息</text>

          <view class="form-group">
            <text class="form-label">保单号 *</text>
            <input class="form-input" placeholder="请输入保单号"
                   value="{{claimForm.policyNumber}}" bindinput="onFormInput" data-field="policyNumber" />
          </view>

          <view class="form-group">
            <text class="form-label">被保险人姓名 *</text>
            <input class="form-input" placeholder="请输入被保险人姓名"
                   value="{{claimForm.insuredName}}" bindinput="onFormInput" data-field="insuredName" />
          </view>

          <view class="form-group">
            <text class="form-label">联系电话 *</text>
            <input class="form-input" placeholder="请输入联系电话" type="number"
                   value="{{claimForm.phone}}" bindinput="onFormInput" data-field="phone" />
          </view>

          <view class="form-group">
            <text class="form-label">事故发生时间 *</text>
            <picker mode="date" value="{{claimForm.accidentDate}}" bindchange="onDateChange" data-field="accidentDate">
              <view class="picker-input">
                <text wx:if="{{claimForm.accidentDate}}">{{claimForm.accidentDate}}</text>
                <text wx:else class="placeholder">请选择事故发生时间</text>
                <text class="picker-arrow">▼</text>
              </view>
            </picker>
          </view>

          <view class="form-group">
            <text class="form-label">事故地点 *</text>
            <input class="form-input" placeholder="请输入事故发生地点"
                   value="{{claimForm.accidentLocation}}" bindinput="onFormInput" data-field="accidentLocation" />
          </view>
        </view>

        <!-- 事故描述 -->
        <view class="form-section">
          <text class="section-title">事故描述</text>

          <view class="form-group">
            <text class="form-label">事故经过 *</text>
            <textarea class="form-textarea" placeholder="请详细描述事故经过，包括时间、地点、原因等"
                      value="{{claimForm.accidentDescription}}" bindinput="onFormInput" data-field="accidentDescription"></textarea>
          </view>

          <view class="form-group" wx:if="{{currentClaimType === 'medical'}}">
            <text class="form-label">诊断结果</text>
            <textarea class="form-textarea" placeholder="请输入医院诊断结果"
                      value="{{claimForm.diagnosis}}" bindinput="onFormInput" data-field="diagnosis"></textarea>
          </view>

          <view class="form-group">
            <text class="form-label">理赔金额</text>
            <input class="form-input" placeholder="请输入申请理赔金额" type="digit"
                   value="{{claimForm.claimAmount}}" bindinput="onFormInput" data-field="claimAmount" />
          </view>
        </view>

        <!-- 材料上传 -->
        <view class="form-section">
          <text class="section-title">材料上传</text>

          <view class="upload-section">
            <text class="upload-label">理赔材料 *</text>
            <view class="upload-grid">
              <view class="upload-item" wx:for="{{claimForm.documents}}" wx:key="*this">
                <image src="{{item}}" class="upload-image" mode="aspectFill" bindtap="onPreviewImage" data-url="{{item}}"></image>
                <view class="upload-delete" bindtap="onDeleteImage" data-index="{{index}}">×</view>
              </view>
              <view class="upload-add" wx:if="{{claimForm.documents.length < 9}}" bindtap="onUploadImage">
                <text class="upload-icon">📷</text>
                <text class="upload-text">添加图片</text>
              </view>
            </view>
            <text class="upload-tip">请上传相关证明材料，支持jpg、png格式，最多9张</text>
          </view>
        </view>
      </scroll-view>

      <view class="form-actions">
        <button class="cancel-btn" bindtap="onCloseClaimModal">取消</button>
        <button class="submit-btn" bindtap="onSubmitClaim" disabled="{{submitting}}">
          {{submitting ? '提交中...' : '提交申请'}}
        </button>
      </view>
    </view>
  </view>

  <!-- 底部安全间距 -->
  <view class="bottom-safe-area"></view>
</view>
