/* pages/claims/claims.wxss */
@import "../../common/placeholder.wxss";

.container {
  background: #f5f5f5;
  min-height: 100vh;
}

/* 理赔服务头部 */
.claims-header {
  background: linear-gradient(135deg, #10B981 0%, #34D399 100%);
  color: white;
  padding: 60rpx 32rpx 40rpx;
  text-align: center;
}

.header-content {
  margin-bottom: 40rpx;
}

.header-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  display: block;
}

.header-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  display: block;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

.service-features {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 32rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.feature-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-bottom: 16rpx;
}

.feature-title {
  font-size: 24rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  display: block;
}

.feature-desc {
  font-size: 22rpx;
  opacity: 0.8;
  display: block;
}

/* 理赔指南卡片 */
.guide-card {
  background: white;
  margin: 32rpx;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.guide-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.guide-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1F2937;
}

.guide-icon {
  font-size: 48rpx;
}

.guide-steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}

.guide-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-width: 120rpx;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #1976D2 0%, #42A5F5 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.step-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

.step-arrow {
  font-size: 32rpx;
  color: #1976D2;
  margin: 0 20rpx;
}

/* 理赔中心头部 */
.claims-header {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  color: white;
  padding: 40rpx 32rpx 32rpx;
  position: relative;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32rpx;
}

.header-info {
  flex: 1;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  display: block;
}

.service-info {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.service-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.service-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-right: 16rpx;
}

.service-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
  display: block;
  margin-top: 8rpx;
}

.header-help {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  font-size: 24rpx;
}

/* 快速操作 */
.quick-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin: 0 16rpx;
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-bottom: 12rpx;
}

.action-text {
  font-size: 24rpx;
  font-weight: bold;
  margin-bottom: 4rpx;
  display: block;
}

.action-desc {
  font-size: 20rpx;
  opacity: 0.8;
  display: block;
}

/* 快速理赔进度查询 */
.progress-query {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  margin-bottom: 24rpx;
}

.query-icon {
  font-size: 24rpx;
  margin-right: 12rpx;
}

.query-text {
  font-size: 24rpx;
}

/* 立即申请理赔按钮 */
.apply-claim-btn {
  width: 100%;
  height: 88rpx;
  background: #3B82F6;
  color: white;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.3);
}

.apply-claim-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.3);
}

/* 理赔记录 */
.claims-records {
  background: white;
  margin: 24rpx 32rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.records-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.filter-tabs {
  display: flex;
  gap: 24rpx;
}

.filter-tab {
  font-size: 24rpx;
  color: #999;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.filter-tab.active {
  color: #3B82F6;
  background: #EBF4FF;
}

.records-list {
  padding: 0 32rpx 32rpx;
}

.claim-record {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.claim-record:last-child {
  border-bottom: none;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.record-type {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.record-number {
  font-size: 24rpx;
  color: #999;
}

.record-info {
  margin-bottom: 16rpx;
}

.record-date {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 4rpx;
}

.record-hospital {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.record-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-status {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.record-status.processing .status-dot {
  background: #F59E0B;
}

.record-status.completed .status-dot {
  background: #10B981;
}

.record-status.rejected .status-dot {
  background: #EF4444;
}

.status-text {
  font-size: 24rpx;
}

.record-status.processing .status-text {
  color: #F59E0B;
}

.record-status.completed .status-text {
  color: #10B981;
}

.record-status.rejected .status-text {
  color: #EF4444;
}

.record-amount {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.empty-records {
  padding: 80rpx 32rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.card-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 32rpx;
}

.claim-options {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.claim-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 32rpx 24rpx;
  border: 2rpx solid #E5E7EB;
  border-radius: 24rpx;
  transition: all 0.3s;
  width: 48%;
  margin-bottom: 24rpx;
}

.claim-option:active {
  transform: scale(0.98);
  border-color: #1E40AF;
  background: #F0F9FF;
}

.option-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-bottom: 16rpx;
}

.option-icon.medical {
  background: #DBEAFE;
}

.option-icon.critical {
  background: #D1FAE5;
}

.option-icon.accident {
  background: #FEF3C7;
}

.option-icon.death {
  background: #E0E7FF;
}

.option-title {
  display: block;
  font-size: 24rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 8rpx;
}

.option-desc {
  font-size: 20rpx;
  color: #6B7280;
  line-height: 1.4;
}

/* 我的理赔卡片 */
.my-claims-card {
  background: white;
  border-radius: 32rpx;
  margin: 0 32rpx 32rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 12rpx -2rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.view-all {
  font-size: 24rpx;
  color: #1E40AF;
}

.claims-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.claim-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: #F9FAFB;
  border-radius: 16rpx;
  border: 1rpx solid #E5E7EB;
}

.claim-info {
  flex: 1;
  margin-right: 24rpx;
}

.claim-number {
  display: block;
  font-size: 24rpx;
  color: #6B7280;
  margin-bottom: 8rpx;
}

.claim-type {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1F2937;
  margin-bottom: 8rpx;
}

.claim-date {
  font-size: 20rpx;
  color: #9CA3AF;
}

.claim-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: right;
}

.status-text {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.status-text.status-submitted {
  background: #FEF3C7;
  color: #D97706;
}

.status-text.status-reviewing {
  background: #DBEAFE;
  color: #1E40AF;
}

.status-text.status-approved {
  background: #D1FAE5;
  color: #059669;
}

.status-text.status-paid {
  background: #D1FAE5;
  color: #059669;
}

.status-text.status-rejected {
  background: #FEE2E2;
  color: #DC2626;
}

.claim-amount {
  font-size: 24rpx;
  font-weight: bold;
  color: #1E40AF;
}

/* 空状态 */
.empty-claims {
  text-align: center;
  padding: 48rpx 24rpx;
}

.empty-image {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 24rpx;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #6B7280;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #9CA3AF;
}

/* 理赔须知卡片 */
.notice-card {
  background: white;
  border-radius: 32rpx;
  margin: 0 32rpx 32rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 12rpx -2rpx rgba(0, 0, 0, 0.1);
}

.notice-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.notice-item {
  display: flex;
  gap: 20rpx;
  align-items: flex-start;
}

.notice-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #F0F9FF;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  flex-shrink: 0;
}

.notice-content {
  flex: 1;
}

.notice-title {
  display: block;
  font-size: 24rpx;
  font-weight: 600;
  color: #1F2937;
  margin-bottom: 8rpx;
}

.notice-desc {
  font-size: 22rpx;
  color: #6B7280;
  line-height: 1.5;
}

/* 联系客服卡片 */
.contact-card {
  background: white;
  border-radius: 32rpx;
  margin: 0 32rpx 32rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 12rpx -2rpx rgba(0, 0, 0, 0.1);
}

.contact-options {
  display: flex;
  gap: 24rpx;
}

.contact-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  padding: 32rpx 24rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 600;
  border: none;
}

.contact-btn.primary {
  background: #1E40AF;
  color: white;
}

.contact-btn.secondary {
  background: #F3F4F6;
  color: #6B7280;
  border: 2rpx solid #E5E7EB;
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  font-size: 20rpx;
}

/* 登录提示卡片 */
.login-prompt-card {
  background: white;
  border-radius: 32rpx;
  margin: 0 32rpx 32rpx;
  padding: 48rpx 32rpx;
  box-shadow: 0 8rpx 12rpx -2rpx rgba(0, 0, 0, 0.1);
  text-align: center;
}

.prompt-icon {
  font-size: 96rpx;
  margin-bottom: 24rpx;
}

.prompt-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 16rpx;
}

.prompt-desc {
  display: block;
  font-size: 24rpx;
  color: #6B7280;
  margin-bottom: 48rpx;
  line-height: 1.5;
}

.login-btn {
  background: #1E40AF;
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 600;
}

/* 理赔申请弹窗 */
.claim-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 10%;
  left: 5%;
  right: 5%;
  bottom: 10%;
  background: white;
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: white;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
}

.form-content {
  flex: 1;
  padding: 0 32rpx;
}

.form-section {
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #333;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #333;
  box-sizing: border-box;
}

.picker-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #333;
}

.placeholder {
  color: #999;
}

.picker-arrow {
  font-size: 20rpx;
  color: #999;
}

/* 上传区域 */
.upload-section {
  margin-top: 16rpx;
}

.upload-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.upload-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.upload-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}

.upload-delete {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
}

.upload-add {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #ccc;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.upload-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.upload-text {
  font-size: 20rpx;
}

.upload-tip {
  font-size: 20rpx;
  color: #999;
  margin-top: 16rpx;
  line-height: 1.4;
}

/* 表单操作按钮 */
.form-actions {
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
  background: white;
}

.cancel-btn {
  flex: 1;
  padding: 24rpx;
  background: #f3f4f6;
  color: #666;
  border-radius: 16rpx;
  font-size: 24rpx;
  border: none;
}

.submit-btn {
  flex: 2;
  padding: 24rpx;
  background: #1e40af;
  color: white;
  border-radius: 16rpx;
  font-size: 24rpx;
  border: none;
}

.submit-btn[disabled] {
  background: #ccc;
  color: #999;
}

/* 理赔指南 */
.claims-guide {
  background: white;
  margin: 24rpx 32rpx;
  border-radius: 20rpx;
  padding: 32rpx;
}

.guide-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

.guide-list {
  display: flex;
  flex-direction: column;
}

.guide-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.guide-item:last-child {
  border-bottom: none;
}

.guide-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  margin-right: 20rpx;
}

.guide-content {
  flex: 1;
}

.guide-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}

.guide-desc {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.guide-arrow {
  font-size: 24rpx;
  color: #ccc;
}

.guide-status {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.online-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.online-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #10B981;
}

.online-text {
  font-size: 20rpx;
  color: #10B981;
}

/* 紧急联系 */
.emergency-contact {
  background: #FEF2F2;
  margin: 24rpx 32rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  border: 2rpx solid #FECACA;
}

.emergency-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.emergency-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.emergency-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #DC2626;
}

.contact-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.contact-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
}

.contact-label {
  font-size: 24rpx;
  color: #666;
}

.contact-action {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.contact-number {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.call-btn {
  background: #DC2626;
  color: white;
  border-radius: 8rpx;
  padding: 8rpx 16rpx;
  font-size: 20rpx;
  border: none;
}

/* 理赔记录卡片 - 按照原型图精确设计 */
.claim-card {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}

.claim-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.claim-main-info {
  flex: 1;
}

.claim-tags {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  gap: 16rpx;
}

.status-tag {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 50rpx;
  font-weight: 500;
}

.status-tag.status-processing {
  background: #DBEAFE;
  color: #1976D2;
}

.status-tag.status-completed {
  background: #D1FAE5;
  color: #059669;
}

.status-tag.status-pending {
  background: #FEE2E2;
  color: #DC2626;
}

.claim-number-tag {
  font-size: 24rpx;
  color: #6B7280;
}

.claim-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1F2937;
  margin-bottom: 8rpx;
  display: block;
}

.claim-time {
  font-size: 26rpx;
  color: #6B7280;
  display: block;
}

.claim-arrow {
  margin-left: 16rpx;
}

.arrow-icon {
  font-size: 32rpx;
  color: #9CA3AF;
}

/* 金额高亮卡片 */
.amount-highlight {
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.amount-highlight.status-processing {
  background: #F0F9FF;
}

.amount-highlight.status-completed {
  background: #ECFDF5;
}

.amount-highlight.status-pending {
  background: #FEF2F2;
}

.amount-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.amount-label {
  font-size: 28rpx;
  color: #6B7280;
}

.amount-value {
  font-size: 32rpx;
  font-weight: bold;
}

.amount-highlight.status-processing .amount-value {
  color: #1976D2;
}

.amount-highlight.status-completed .amount-value {
  color: #059669;
}

.amount-highlight.status-pending .amount-value {
  color: #DC2626;
}

/* 理赔卡片底部 */
.claim-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex: 1;
}

.status-icon {
  font-size: 28rpx;
}

.status-desc {
  font-size: 26rpx;
  color: #6B7280;
}

.detail-btn {
  padding: 12rpx 24rpx;
  border-radius: 16rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
}

.detail-btn.status-processing {
  background: #DBEAFE;
  color: #1976D2;
}

.detail-btn.status-completed {
  background: #D1FAE5;
  color: #059669;
}

.detail-btn.status-pending {
  background: #FEE2E2;
  color: #DC2626;
}
