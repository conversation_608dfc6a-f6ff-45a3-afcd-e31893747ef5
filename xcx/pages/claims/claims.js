// pages/claims/claims.js
const app = getApp()

Page({
  data: {
    userInfo: { name: '用户' }, // 模拟登录状态
    claims: [
      {
        id: 1,
        claim_number: 'LP20240315-000001',
        claim_type: 'medical',
        status: 'processing',
        claim_amount: '2850',
        currency: 'CNY',
        created_at: '2024-03-15 14:30',
        hospital: '申请医院'
      },
      {
        id: 2,
        claim_number: 'LP20240310-000002',
        claim_type: 'medical',
        status: 'completed',
        claim_amount: '500',
        currency: 'CNY',
        created_at: '2024-03-10 16:45',
        hospital: '申请医院'
      },
      {
        id: 3,
        claim_number: 'LP20240307-000003',
        claim_type: 'accident',
        status: 'rejected',
        claim_amount: '1200',
        currency: 'CNY',
        created_at: '2024-03-07 12:00',
        hospital: '申请保险'
      }
    ],
    loading: false,
    hasActiveClaims: true,

    // 理赔类型映射
    claimTypeMap: {
      'medical': '医疗理赔',
      'critical_illness': '重疾理赔',
      'accident': '意外理赔',
      'death': '身故理赔',
      'disability': '残疾理赔'
    },

    // 理赔状态映射
    claimStatusMap: {
      'submitted': '已提交',
      'reviewing': '审核中',
      'processing': '申请处理中',
      'approved': '已批准',
      'completed': '理赔完成',
      'paid': '已赔付',
      'rejected': '资料不足需补充',
      'closed': '已关闭'
    },

    // 筛选相关
    currentFilter: 'all',
    filteredClaims: [],

    // 理赔申请相关
    showClaimModal: false,
    currentClaimType: '',
    submitting: false,
    claimForm: {
      policyNumber: '',
      insuredName: '',
      phone: '',
      accidentDate: '',
      accidentLocation: '',
      accidentDescription: '',
      diagnosis: '',
      claimAmount: '',
      documents: []
    }
  },

  onLoad() {
    this.checkLoginStatus()
  },

  onShow() {
    this.checkLoginStatus()

    // 如果已登录，加载理赔数据
    if (app.globalData.isLoggedIn) {
      this.loadUserClaims()
    }
  },

  // 快速操作
  onQuickAction(e) {
    const action = e.currentTarget.dataset.action

    switch (action) {
      case 'report':
        // 在线报案
        wx.navigateTo({
          url: '/pages/services/services?type=report'
        })
        break
      case 'apply':
        // 立即申请
        this.onApplyClaim()
        break
      case 'query':
        // 保单查询
        wx.navigateTo({
          url: '/pages/products/products?type=policy'
        })
        break
    }
  },

  // 立即申请理赔
  onApplyClaim() {
    if (!this.data.userInfo) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再申请理赔',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/profile/profile'
            })
          }
        }
      })
      return
    }

    wx.navigateTo({
      url: '/pages/claims/claim-apply/claim-apply'
    })
  },

  // 理赔详情
  onClaimDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/claims/claim-detail/claim-detail?id=${id}`
    })
  },

  // 理赔指南详情
  onGuideDetail(e) {
    const type = e.currentTarget.dataset.type
    let url = ''

    switch (type) {
      case 'process':
        url = '/pages/claims/guide/process'
        break
      case 'materials':
        url = '/pages/claims/guide/materials'
        break
      case 'faq':
        url = '/pages/claims/guide/faq'
        break
      case 'service':
        // 联系客服
        wx.showModal({
          title: '联系客服',
          content: '是否拨打客服电话：************？',
          success: (res) => {
            if (res.confirm) {
              wx.makePhoneCall({
                phoneNumber: '4009990000'
              })
            }
          }
        })
        return
    }

    if (url) {
      wx.navigateTo({ url })
    }
  },

  // 紧急联系
  onEmergencyCall(e) {
    const phone = e.currentTarget.dataset.phone
    wx.makePhoneCall({
      phoneNumber: phone,
      fail: (err) => {
        console.error('拨打电话失败:', err)
        wx.showToast({
          title: '拨打失败',
          icon: 'none'
        })
      }
    })
  },

  // 快速操作
  onQuickAction(e) {
    const action = e.currentTarget.dataset.action

    switch (action) {
      case 'report':
        // 在线报案
        wx.navigateTo({
          url: '/pages/services/services?type=report'
        })
        break
      case 'apply':
        // 立即申请
        this.onApplyClaim()
        break
      case 'query':
        // 保单查询
        wx.navigateTo({
          url: '/pages/products/products?type=policy'
        })
        break
    }
  },

  // 立即申请理赔
  onApplyClaim() {
    if (!this.data.userInfo) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再申请理赔',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/profile/profile'
            })
          }
        }
      })
      return
    }

    wx.navigateTo({
      url: '/pages/claims/claim-apply/claim-apply'
    })
  },

  // 理赔详情
  onClaimDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/claims/claim-detail/claim-detail?id=${id}`
    })
  },

  // 理赔指南详情
  onGuideDetail(e) {
    const type = e.currentTarget.dataset.type
    let url = ''

    switch (type) {
      case 'process':
        url = '/pages/claims/guide/process'
        break
      case 'materials':
        url = '/pages/claims/guide/materials'
        break
      case 'faq':
        url = '/pages/claims/guide/faq'
        break
      case 'service':
        // 联系客服
        wx.showModal({
          title: '联系客服',
          content: '是否拨打客服电话：************？',
          success: (res) => {
            if (res.confirm) {
              wx.makePhoneCall({
                phoneNumber: '4009990000'
              })
            }
          }
        })
        return
    }

    if (url) {
      wx.navigateTo({ url })
    }
  },

  // 紧急联系
  onEmergencyCall(e) {
    const phone = e.currentTarget.dataset.phone
    wx.makePhoneCall({
      phoneNumber: phone,
      fail: (err) => {
        console.error('拨打电话失败:', err)
        wx.showToast({
          title: '拨打失败',
          icon: 'none'
        })
      }
    })
  },

  onPullDownRefresh() {
    this.loadUserClaims().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 检查登录状态
  checkLoginStatus() {
    this.setData({
      userInfo: app.globalData.userInfo
    })
  },

  // 加载用户理赔记录
  async loadUserClaims() {
    if (!app.globalData.isLoggedIn) return

    this.setData({ loading: true })

    try {
      const response = await app.request({
        url: '/users/api/claims/',
        data: { page_size: 5 }
      })

      const claims = response.results || []
      
      // 格式化理赔数据
      const formattedClaims = claims.map(claim => ({
        ...claim,
        created_at: this.formatDate(claim.created_at)
      }))

      this.setData({
        claims: formattedClaims
      })

    } catch (error) {
      console.error('加载理赔记录失败:', error)
      app.showToast('加载失败，请重试')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 格式化日期
  formatDate(dateString) {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  },

  // 理赔类型点击
  onClaimTypeTap(e) {
    const type = e.currentTarget.dataset.type
    
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      this.showLoginPrompt()
      return
    }

    // 跳转到理赔申请页面
    wx.navigateTo({
      url: `/pages/services/services?tab=claim&claimType=${type}`
    })
  },

  // 理赔记录点击
  onClaimTap(e) {
    const claim = e.currentTarget.dataset.claim
    
    wx.navigateTo({
      url: `/pages/services/services?tab=claim&claimId=${claim.id}`
    })
  },

  // 查看全部理赔
  onViewAllClaims() {
    wx.navigateTo({
      url: '/pages/services/services?tab=claims'
    })
  },

  // 登录点击
  onLoginTap() {
    this.performWeChatLogin()
  },

  // 执行微信登录
  async performWeChatLogin() {
    try {
      app.showLoading('登录中...')
      
      const userInfo = await app.wechatLogin()
      
      this.setData({ userInfo })
      
      // 登录成功后加载理赔数据
      this.loadUserClaims()
      
      app.showToast('登录成功', 'success')
    } catch (error) {
      console.error('登录失败:', error)
      app.showToast('登录失败，请重试')
    } finally {
      app.hideLoading()
    }
  },

  // 显示登录提示
  showLoginPrompt() {
    wx.showModal({
      title: '提示',
      content: '请先登录后使用理赔功能',
      confirmText: '立即登录',
      success: (res) => {
        if (res.confirm) {
          this.performWeChatLogin()
        }
      }
    })
  },

  // 联系在线客服
  onContactService() {
    wx.navigateTo({
      url: '/pages/services/services?tab=contact'
    })
  },

  // 电话咨询
  onCallService() {
    wx.showModal({
      title: '理赔热线',
      content: '客服热线：+852 1234 5678\n服务时间：周一至周五 9:00-18:00',
      confirmText: '拨打电话',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '+85212345678',
            fail: () => {
              app.showToast('拨打失败，请手动拨打')
            }
          })
        }
      }
    })
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '香港保险理赔服务 - 快速便捷的理赔体验',
      path: '/pages/claims/claims',
      imageUrl: '/images/share-claims.jpg'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '香港保险理赔服务 - 快速便捷的理赔体验',
      imageUrl: '/images/share-claims.jpg'
    }
  },

  // 状态筛选
  onFilterTap(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({ currentFilter: filter });
    this.filterClaims(filter);
  },

  // 筛选理赔记录
  filterClaims(filter) {
    const { claims } = this.data;
    let filteredClaims = [];

    switch (filter) {
      case 'processing':
        filteredClaims = claims.filter(claim =>
          ['submitted', 'reviewing'].includes(claim.status)
        );
        break;
      case 'completed':
        filteredClaims = claims.filter(claim =>
          ['paid', 'approved'].includes(claim.status)
        );
        break;
      default:
        filteredClaims = claims;
    }

    this.setData({ filteredClaims });
  },

  // 理赔指南项目点击
  onGuideItemTap(e) {
    const type = e.currentTarget.dataset.type;

    switch (type) {
      case 'process':
        wx.showToast({ title: '理赔流程功能开发中...', icon: 'none' });
        break;
      case 'materials':
        wx.showToast({ title: '材料清单功能开发中...', icon: 'none' });
        break;
      case 'faq':
        wx.showToast({ title: 'FAQ功能开发中...', icon: 'none' });
        break;
      case 'service':
        wx.showToast({ title: '正在连接理赔客服...', icon: 'loading' });
        break;
    }
  },

  // 理赔申请相关方法
  onClaimTypeTap(e) {
    const type = e.currentTarget.dataset.type;

    if (!this.data.userInfo) {
      wx.showModal({
        title: '请先登录',
        content: '登录后才能申请理赔',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            this.onLoginTap();
          }
        }
      });
      return;
    }

    this.setData({
      currentClaimType: type,
      showClaimModal: true
    });

    // 重置表单
    this.resetClaimForm();
  },

  onCloseClaimModal() {
    this.setData({ showClaimModal: false });
  },

  resetClaimForm() {
    this.setData({
      claimForm: {
        policyNumber: '',
        insuredName: '',
        phone: '',
        accidentDate: '',
        accidentLocation: '',
        accidentDescription: '',
        diagnosis: '',
        claimAmount: '',
        documents: []
      }
    });
  },

  // 表单输入处理
  onFormInput(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;

    this.setData({
      [`claimForm.${field}`]: value
    });
  },

  onDateChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;

    this.setData({
      [`claimForm.${field}`]: value
    });
  },

  // 图片上传
  onUploadImage() {
    wx.chooseImage({
      count: 9 - this.data.claimForm.documents.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePaths = res.tempFilePaths;
        const documents = [...this.data.claimForm.documents, ...tempFilePaths];

        this.setData({
          'claimForm.documents': documents
        });
      }
    });
  },

  onDeleteImage(e) {
    const index = e.currentTarget.dataset.index;
    const documents = [...this.data.claimForm.documents];
    documents.splice(index, 1);

    this.setData({
      'claimForm.documents': documents
    });
  },

  onPreviewImage(e) {
    const url = e.currentTarget.dataset.url;
    wx.previewImage({
      current: url,
      urls: this.data.claimForm.documents
    });
  },

  // 提交理赔申请
  async onSubmitClaim() {
    const { claimForm, currentClaimType } = this.data;

    // 表单验证
    if (!this.validateClaimForm()) {
      return;
    }

    this.setData({ submitting: true });

    try {
      // 上传图片
      const uploadedImages = await this.uploadImages(claimForm.documents);

      // 提交理赔申请
      const claimData = {
        ...claimForm,
        claimType: currentClaimType,
        documents: uploadedImages
      };

      await app.request({
        url: '/insurance/api/claims/',
        method: 'POST',
        data: claimData
      });

      wx.showToast({
        title: '申请提交成功',
        icon: 'success'
      });

      this.setData({ showClaimModal: false });
      this.loadUserClaims(); // 重新加载理赔记录

    } catch (error) {
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 表单验证
  validateClaimForm() {
    const { claimForm } = this.data;

    if (!claimForm.policyNumber) {
      wx.showToast({ title: '请输入保单号', icon: 'none' });
      return false;
    }

    if (!claimForm.insuredName) {
      wx.showToast({ title: '请输入被保险人姓名', icon: 'none' });
      return false;
    }

    if (!claimForm.phone) {
      wx.showToast({ title: '请输入联系电话', icon: 'none' });
      return false;
    }

    if (!claimForm.accidentDate) {
      wx.showToast({ title: '请选择事故发生时间', icon: 'none' });
      return false;
    }

    if (!claimForm.accidentLocation) {
      wx.showToast({ title: '请输入事故地点', icon: 'none' });
      return false;
    }

    if (!claimForm.accidentDescription) {
      wx.showToast({ title: '请描述事故经过', icon: 'none' });
      return false;
    }

    if (claimForm.documents.length === 0) {
      wx.showToast({ title: '请上传理赔材料', icon: 'none' });
      return false;
    }

    return true;
  },

  // 上传图片
  async uploadImages(images) {
    const uploadPromises = images.map(image => {
      return new Promise((resolve, reject) => {
        wx.uploadFile({
          url: `${app.globalData.baseUrl}/insurance/api/upload/`,
          filePath: image,
          name: 'file',
          header: {
            'Authorization': `Bearer ${wx.getStorageSync('access_token')}`
          },
          success: (res) => {
            const data = JSON.parse(res.data);
            resolve(data.url);
          },
          fail: reject
        });
      });
    });

    return Promise.all(uploadPromises);
  },

  // 紧急电话拨打
  onEmergencyCall(e) {
    const phone = e.currentTarget.dataset.phone;
    wx.makePhoneCall({
      phoneNumber: phone,
      fail: () => {
        wx.showToast({
          title: '拨打失败，请手动拨打',
          icon: 'none'
        });
      }
    });
  }
})
