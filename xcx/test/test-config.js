// test/test-config.js
// 测试配置文件

const testConfig = {
  // 测试环境配置
  environment: {
    // 模拟微信小程序环境
    mockWxAPIs: true,
    // 测试数据库
    useTestDatabase: true,
    // 测试API地址
    testApiBase: 'https://test-api.example.com'
  },

  // 测试覆盖率配置
  coverage: {
    // 需要测试的文件模式
    include: [
      'pages/**/*.js',
      'utils/**/*.js',
      'components/**/*.js'
    ],
    // 排除的文件
    exclude: [
      'test/**/*.js',
      '**/*.test.js',
      'node_modules/**'
    ],
    // 覆盖率阈值
    threshold: {
      statements: 80,
      branches: 75,
      functions: 80,
      lines: 80
    }
  },

  // 测试套件配置
  testSuites: [
    {
      name: '工具类测试',
      pattern: 'utils/**/*.test.js',
      timeout: 5000
    },
    {
      name: '页面逻辑测试',
      pattern: 'pages/**/*.test.js',
      timeout: 10000
    },
    {
      name: '组件测试',
      pattern: 'components/**/*.test.js',
      timeout: 5000
    },
    {
      name: '集成测试',
      pattern: 'test/integration/**/*.test.js',
      timeout: 15000
    }
  ],

  // 模拟数据配置
  mockData: {
    // 用户数据
    user: {
      id: 'test_user_001',
      name: '测试用户',
      phone: '13812345678',
      email: '<EMAIL>',
      avatar: 'https://example.com/avatar.jpg'
    },
    
    // 产品数据
    products: [
      {
        id: 'product_001',
        name: '测试保险产品A',
        category: '储蓄分红',
        company_name: '测试保险公司',
        expected_return: '6.5%',
        main_image: 'https://example.com/product1.jpg'
      },
      {
        id: 'product_002',
        name: '测试保险产品B',
        category: '重疾保险',
        company_name: '测试保险公司',
        coverage_amount: '100万',
        main_image: 'https://example.com/product2.jpg'
      }
    ],

    // API响应数据
    apiResponses: {
      '/products/api/products/': {
        statusCode: 200,
        data: {
          results: [
            // 产品列表数据
          ],
          count: 10,
          next: null,
          previous: null
        }
      },
      '/users/api/profiles/me/': {
        statusCode: 200,
        data: {
          id: 'test_user_001',
          name: '测试用户',
          phone: '13812345678'
        }
      }
    }
  },

  // 测试报告配置
  reporting: {
    // 报告格式
    formats: ['console', 'json', 'html'],
    // 输出目录
    outputDir: 'test/reports',
    // 是否生成详细报告
    verbose: true,
    // 是否包含覆盖率报告
    includeCoverage: true
  }
}

module.exports = testConfig
