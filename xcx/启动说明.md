# 香港保险小程序启动说明

## 问题解决

✅ **已解决：app.json 文件缺失问题**

已创建完整的 `app.json` 配置文件，包含：
- 页面路由配置
- 窗口样式配置
- 底部导航栏配置
- 网络超时配置
- 权限配置等

## 当前项目状态

### ✅ 已完成
1. **后端服务** - Django API 正常运行在 https://baoxian.weixinjishu.top
2. **app.json** - 小程序核心配置文件已创建
3. **页面结构** - 所有主要页面文件已存在
4. **配置文件** - API地址和其他配置已正确设置

### 📋 待完善
1. **底部导航图标** - 需要添加 tabBar 图标文件（见 images/README.md）
2. **微信小程序 AppID** - 需要在 config/config.js 中配置正确的 AppID

## 启动步骤

1. **打开微信开发者工具**
2. **导入项目**
   - 选择 `xcx` 目录作为项目根目录
   - AppID：使用 `project.config.json` 中的 `wxfc5e703c6c0e1a2f`
3. **编译运行**
   - 现在应该可以正常编译和预览

## 页面结构

- `pages/index/index` - 欢迎页面
- `pages/home/<USER>
- `pages/products/products` - 产品列表
- `pages/product-detail/product-detail` - 产品详情
- `pages/purchase/purchase` - 购买页面
- `pages/claims/claims` - 理赔页面
- `pages/profile/profile` - 个人中心
- `pages/logs/logs` - 日志页面

## API 配置

当前配置的后端 API 地址：
- 生产环境：`https://baoxian.weixinjishu.top`
- 开发环境：`http://127.0.0.1:8000`

可在 `config/config.js` 中修改 `isDev` 参数来切换环境。

## 下一步

1. 添加底部导航图标文件
2. 测试各个页面功能
3. 配置微信小程序的真实 AppID
4. 测试与后端 API 的连接
