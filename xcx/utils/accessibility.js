// utils/accessibility.js
// 无障碍支持和用户体验优化工具

class AccessibilityHelper {
  constructor() {
    this.isVoiceOverEnabled = false
    this.fontSize = 'normal' // small, normal, large
    this.highContrast = false
    this.init()
  }

  // 初始化无障碍设置
  init() {
    // 检查系统无障碍设置
    if (wx.getSystemInfo) {
      wx.getSystemInfo({
        success: (res) => {
          // 检查是否启用了辅助功能
          if (res.fontSizeSetting) {
            this.fontSize = res.fontSizeSetting
          }
        }
      })
    }

    // 从本地存储加载用户偏好
    this.loadUserPreferences()
  }

  // 加载用户偏好设置
  loadUserPreferences() {
    try {
      const preferences = wx.getStorageSync('accessibility_preferences')
      if (preferences) {
        this.fontSize = preferences.fontSize || 'normal'
        this.highContrast = preferences.highContrast || false
      }
    } catch (e) {
      console.warn('加载无障碍偏好设置失败:', e)
    }
  }

  // 保存用户偏好设置
  saveUserPreferences() {
    try {
      wx.setStorageSync('accessibility_preferences', {
        fontSize: this.fontSize,
        highContrast: this.highContrast
      })
    } catch (e) {
      console.warn('保存无障碍偏好设置失败:', e)
    }
  }

  // 设置字体大小
  setFontSize(size) {
    this.fontSize = size
    this.saveUserPreferences()
    this.applyFontSize()
  }

  // 应用字体大小
  applyFontSize() {
    const body = document.body || document.documentElement
    if (body) {
      body.classList.remove('font-small', 'font-normal', 'font-large')
      body.classList.add(`font-${this.fontSize}`)
    }
  }

  // 设置高对比度
  setHighContrast(enabled) {
    this.highContrast = enabled
    this.saveUserPreferences()
    this.applyHighContrast()
  }

  // 应用高对比度
  applyHighContrast() {
    const body = document.body || document.documentElement
    if (body) {
      if (this.highContrast) {
        body.classList.add('high-contrast')
      } else {
        body.classList.remove('high-contrast')
      }
    }
  }

  // 为元素添加无障碍属性
  addAriaLabel(selector, label) {
    const elements = document.querySelectorAll(selector)
    elements.forEach(element => {
      element.setAttribute('aria-label', label)
    })
  }

  // 为按钮添加角色和状态
  enhanceButton(selector, options = {}) {
    const elements = document.querySelectorAll(selector)
    elements.forEach(element => {
      element.setAttribute('role', 'button')
      element.setAttribute('tabindex', '0')
      
      if (options.label) {
        element.setAttribute('aria-label', options.label)
      }
      
      if (options.pressed !== undefined) {
        element.setAttribute('aria-pressed', options.pressed)
      }
      
      if (options.disabled) {
        element.setAttribute('aria-disabled', 'true')
      }

      // 添加键盘支持
      element.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault()
          element.click()
        }
      })
    })
  }

  // 为表单添加无障碍支持
  enhanceForm(formSelector) {
    const form = document.querySelector(formSelector)
    if (!form) return

    // 为所有输入框添加标签关联
    const inputs = form.querySelectorAll('input, textarea, select')
    inputs.forEach(input => {
      const label = form.querySelector(`label[for="${input.id}"]`)
      if (!label && input.id) {
        // 查找相邻的文本作为标签
        const prevText = input.previousElementSibling
        if (prevText && prevText.textContent) {
          input.setAttribute('aria-label', prevText.textContent.trim())
        }
      }

      // 添加必填字段标识
      if (input.required) {
        input.setAttribute('aria-required', 'true')
      }

      // 添加错误状态支持
      input.addEventListener('invalid', () => {
        input.setAttribute('aria-invalid', 'true')
      })

      input.addEventListener('input', () => {
        if (input.validity.valid) {
          input.removeAttribute('aria-invalid')
        }
      })
    })
  }

  // 创建屏幕阅读器友好的公告
  announce(message, priority = 'polite') {
    const announcement = document.createElement('div')
    announcement.setAttribute('aria-live', priority)
    announcement.setAttribute('aria-atomic', 'true')
    announcement.style.position = 'absolute'
    announcement.style.left = '-10000px'
    announcement.style.width = '1px'
    announcement.style.height = '1px'
    announcement.style.overflow = 'hidden'
    
    document.body.appendChild(announcement)
    announcement.textContent = message
    
    // 清理
    setTimeout(() => {
      document.body.removeChild(announcement)
    }, 1000)
  }

  // 焦点管理
  manageFocus() {
    let lastFocusedElement = null

    return {
      // 保存当前焦点
      saveFocus() {
        lastFocusedElement = document.activeElement
      },

      // 恢复焦点
      restoreFocus() {
        if (lastFocusedElement && lastFocusedElement.focus) {
          lastFocusedElement.focus()
        }
      },

      // 设置焦点到指定元素
      setFocus(selector) {
        const element = document.querySelector(selector)
        if (element && element.focus) {
          element.focus()
        }
      },

      // 焦点陷阱（用于模态框）
      trapFocus(containerSelector) {
        const container = document.querySelector(containerSelector)
        if (!container) return

        const focusableElements = container.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        )
        
        const firstElement = focusableElements[0]
        const lastElement = focusableElements[focusableElements.length - 1]

        const handleTabKey = (e) => {
          if (e.key === 'Tab') {
            if (e.shiftKey) {
              if (document.activeElement === firstElement) {
                e.preventDefault()
                lastElement.focus()
              }
            } else {
              if (document.activeElement === lastElement) {
                e.preventDefault()
                firstElement.focus()
              }
            }
          }
        }

        container.addEventListener('keydown', handleTabKey)
        
        // 设置初始焦点
        if (firstElement) {
          firstElement.focus()
        }

        // 返回清理函数
        return () => {
          container.removeEventListener('keydown', handleTabKey)
        }
      }
    }
  }

  // 颜色对比度检查
  checkColorContrast(foreground, background) {
    // 简化的对比度计算
    const getLuminance = (color) => {
      const rgb = parseInt(color.replace('#', ''), 16)
      const r = (rgb >> 16) & 0xff
      const g = (rgb >> 8) & 0xff
      const b = (rgb >> 0) & 0xff
      
      const [rs, gs, bs] = [r, g, b].map(c => {
        c = c / 255
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
      })
      
      return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs
    }

    const l1 = getLuminance(foreground)
    const l2 = getLuminance(background)
    const ratio = (Math.max(l1, l2) + 0.05) / (Math.min(l1, l2) + 0.05)
    
    return {
      ratio: ratio,
      aa: ratio >= 4.5,
      aaa: ratio >= 7
    }
  }

  // 获取无障碍建议
  getAccessibilityTips() {
    return [
      '使用语义化的HTML标签',
      '为图片添加有意义的alt属性',
      '确保足够的颜色对比度',
      '支持键盘导航',
      '为表单元素添加标签',
      '使用aria-label描述元素用途',
      '避免仅依赖颜色传达信息',
      '提供跳过导航的链接',
      '确保焦点可见',
      '使用适当的标题层级'
    ]
  }
}

// 用户体验优化工具
class UXOptimizer {
  constructor() {
    this.touchStartTime = 0
    this.touchStartPosition = { x: 0, y: 0 }
  }

  // 优化触摸体验
  optimizeTouchExperience() {
    // 防止双击缩放
    document.addEventListener('touchstart', (e) => {
      if (e.touches.length > 1) {
        e.preventDefault()
      }
    })

    // 优化点击延迟
    document.addEventListener('touchend', (e) => {
      const now = Date.now()
      if (now - this.touchStartTime < 300) {
        e.preventDefault()
        e.target.click()
      }
    })
  }

  // 添加加载反馈
  addLoadingFeedback(element, text = '加载中...') {
    const originalContent = element.innerHTML
    const originalDisabled = element.disabled

    element.disabled = true
    element.innerHTML = `<span class="loading-spinner"></span> ${text}`
    element.classList.add('loading')

    return () => {
      element.disabled = originalDisabled
      element.innerHTML = originalContent
      element.classList.remove('loading')
    }
  }

  // 添加成功反馈
  showSuccessFeedback(message, duration = 2000) {
    const feedback = document.createElement('div')
    feedback.className = 'success-feedback'
    feedback.textContent = message
    feedback.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #10B981;
      color: white;
      padding: 16px 24px;
      border-radius: 8px;
      z-index: 9999;
      animation: fadeInOut ${duration}ms ease-in-out;
    `

    document.body.appendChild(feedback)

    setTimeout(() => {
      document.body.removeChild(feedback)
    }, duration)
  }

  // 添加错误反馈
  showErrorFeedback(message, duration = 3000) {
    const feedback = document.createElement('div')
    feedback.className = 'error-feedback'
    feedback.textContent = message
    feedback.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #EF4444;
      color: white;
      padding: 16px 24px;
      border-radius: 8px;
      z-index: 9999;
      animation: fadeInOut ${duration}ms ease-in-out;
    `

    document.body.appendChild(feedback)

    setTimeout(() => {
      document.body.removeChild(feedback)
    }, duration)
  }

  // 优化滚动体验
  optimizeScrolling() {
    // 平滑滚动
    document.documentElement.style.scrollBehavior = 'smooth'

    // 滚动到顶部按钮
    const scrollToTop = document.createElement('button')
    scrollToTop.innerHTML = '↑'
    scrollToTop.className = 'scroll-to-top'
    scrollToTop.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: #1E40AF;
      color: white;
      border: none;
      font-size: 20px;
      cursor: pointer;
      opacity: 0;
      transition: opacity 0.3s;
      z-index: 1000;
    `

    scrollToTop.addEventListener('click', () => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    })

    document.body.appendChild(scrollToTop)

    // 显示/隐藏滚动到顶部按钮
    window.addEventListener('scroll', () => {
      if (window.pageYOffset > 300) {
        scrollToTop.style.opacity = '1'
      } else {
        scrollToTop.style.opacity = '0'
      }
    })
  }
}

// 创建全局实例
const accessibilityHelper = new AccessibilityHelper()
const uxOptimizer = new UXOptimizer()

module.exports = {
  accessibilityHelper,
  uxOptimizer
}
