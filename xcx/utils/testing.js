// utils/testing.js
// 小程序测试工具和测试用例

class TestRunner {
  constructor() {
    this.tests = []
    this.results = []
    this.isRunning = false
  }

  // 添加测试用例
  addTest(name, testFunction, options = {}) {
    this.tests.push({
      name,
      testFunction,
      timeout: options.timeout || 5000,
      skip: options.skip || false,
      only: options.only || false
    })
  }

  // 运行所有测试
  async runTests() {
    if (this.isRunning) {
      console.warn('测试正在运行中...')
      return
    }

    this.isRunning = true
    this.results = []

    console.log(`开始运行 ${this.tests.length} 个测试用例...`)

    // 检查是否有only标记的测试
    const onlyTests = this.tests.filter(test => test.only)
    const testsToRun = onlyTests.length > 0 ? onlyTests : this.tests.filter(test => !test.skip)

    for (const test of testsToRun) {
      await this.runSingleTest(test)
    }

    this.isRunning = false
    this.printResults()
    return this.results
  }

  // 运行单个测试
  async runSingleTest(test) {
    const startTime = Date.now()
    let result = {
      name: test.name,
      passed: false,
      error: null,
      duration: 0,
      skipped: test.skip
    }

    if (test.skip) {
      result.skipped = true
      this.results.push(result)
      return
    }

    try {
      console.log(`运行测试: ${test.name}`)
      
      // 设置超时
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('测试超时')), test.timeout)
      })

      // 运行测试
      const testPromise = Promise.resolve(test.testFunction())
      
      await Promise.race([testPromise, timeoutPromise])
      
      result.passed = true
      console.log(`✅ ${test.name}`)
    } catch (error) {
      result.error = error.message
      console.error(`❌ ${test.name}: ${error.message}`)
    }

    result.duration = Date.now() - startTime
    this.results.push(result)
  }

  // 打印测试结果
  printResults() {
    const passed = this.results.filter(r => r.passed).length
    const failed = this.results.filter(r => !r.passed && !r.skipped).length
    const skipped = this.results.filter(r => r.skipped).length
    const total = this.results.length

    console.log('\n=== 测试结果 ===')
    console.log(`总计: ${total}`)
    console.log(`通过: ${passed}`)
    console.log(`失败: ${failed}`)
    console.log(`跳过: ${skipped}`)

    if (failed > 0) {
      console.log('\n失败的测试:')
      this.results.filter(r => !r.passed && !r.skipped).forEach(r => {
        console.log(`- ${r.name}: ${r.error}`)
      })
    }

    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0)
    console.log(`\n总耗时: ${totalDuration}ms`)
  }

  // 断言工具
  assert = {
    // 相等断言
    equal: (actual, expected, message) => {
      if (actual !== expected) {
        throw new Error(message || `期望 ${expected}，实际 ${actual}`)
      }
    },

    // 深度相等断言
    deepEqual: (actual, expected, message) => {
      if (JSON.stringify(actual) !== JSON.stringify(expected)) {
        throw new Error(message || `期望 ${JSON.stringify(expected)}，实际 ${JSON.stringify(actual)}`)
      }
    },

    // 真值断言
    ok: (value, message) => {
      if (!value) {
        throw new Error(message || `期望真值，实际 ${value}`)
      }
    },

    // 抛出异常断言
    throws: async (fn, message) => {
      try {
        await fn()
        throw new Error(message || '期望抛出异常，但没有抛出')
      } catch (error) {
        if (error.message === (message || '期望抛出异常，但没有抛出')) {
          throw error
        }
        // 正确抛出了异常
      }
    },

    // 包含断言
    includes: (array, item, message) => {
      if (!array.includes(item)) {
        throw new Error(message || `期望数组包含 ${item}`)
      }
    },

    // 类型断言
    type: (value, expectedType, message) => {
      const actualType = typeof value
      if (actualType !== expectedType) {
        throw new Error(message || `期望类型 ${expectedType}，实际类型 ${actualType}`)
      }
    }
  }

  // 模拟工具
  mock = {
    // 模拟函数
    fn: (implementation) => {
      const mockFn = implementation || (() => {})
      mockFn.calls = []
      mockFn.results = []
      
      const wrappedFn = (...args) => {
        mockFn.calls.push(args)
        try {
          const result = mockFn(...args)
          mockFn.results.push({ type: 'return', value: result })
          return result
        } catch (error) {
          mockFn.results.push({ type: 'throw', value: error })
          throw error
        }
      }
      
      wrappedFn.calls = mockFn.calls
      wrappedFn.results = mockFn.results
      wrappedFn.mockReturnValue = (value) => {
        mockFn.implementation = () => value
      }
      wrappedFn.mockResolvedValue = (value) => {
        mockFn.implementation = () => Promise.resolve(value)
      }
      wrappedFn.mockRejectedValue = (error) => {
        mockFn.implementation = () => Promise.reject(error)
      }
      
      return wrappedFn
    },

    // 模拟微信API
    wxApi: (apiName, mockImplementation) => {
      const originalApi = wx[apiName]
      wx[apiName] = mockImplementation
      
      return () => {
        wx[apiName] = originalApi
      }
    }
  }
}

// 创建测试实例
const testRunner = new TestRunner()

// 添加基础测试用例
testRunner.addTest('数据管理器测试', async () => {
  const { dataManager } = require('./dataManager.js')
  
  // 测试缓存设置和获取
  dataManager.setCache('test_key', { data: 'test_value' })
  const cached = dataManager.getCache('test_key')
  testRunner.assert.deepEqual(cached, { data: 'test_value' })
  
  // 测试缓存过期
  dataManager.setCache('expire_key', 'expire_value', 100) // 100ms过期
  await new Promise(resolve => setTimeout(resolve, 150))
  const expired = dataManager.getCache('expire_key')
  testRunner.assert.equal(expired, null)
})

testRunner.addTest('API管理器测试', async () => {
  const { api } = require('./api.js')
  
  // 模拟wx.request
  const mockRequest = testRunner.mock.wxApi('request', (options) => {
    options.success({ statusCode: 200, data: { success: true } })
  })
  
  try {
    const result = await api.get('/test')
    testRunner.assert.deepEqual(result, { success: true })
  } finally {
    mockRequest() // 恢复原始API
  }
})

testRunner.addTest('安全管理器测试', () => {
  const { securityManager } = require('./security.js')
  
  // 测试数据脱敏
  const maskedPhone = securityManager.maskSensitiveData('13812345678', 'phone')
  testRunner.assert.equal(maskedPhone, '138****5678')
  
  const maskedIdCard = securityManager.maskSensitiveData('110101199001011234', 'idCard')
  testRunner.assert.equal(maskedIdCard, '110101********1234')
  
  // 测试输入验证
  const phoneValidation = securityManager.validateInput('13812345678', 'phone')
  testRunner.assert.ok(phoneValidation.valid)
  
  const invalidPhoneValidation = securityManager.validateInput('123', 'phone')
  testRunner.assert.ok(!invalidPhoneValidation.valid)
})

testRunner.addTest('性能监控器测试', () => {
  const { performanceMonitor } = require('./performance.js')
  
  // 测试页面加载时间记录
  const startTime = Date.now()
  const endTime = startTime + 1000
  performanceMonitor.recordPageLoad('test_page', startTime, endTime)
  
  const report = performanceMonitor.getPerformanceReport()
  testRunner.assert.ok(report.pageLoadTimes.test_page)
  testRunner.assert.equal(report.pageLoadTimes.test_page.average, 1000)
})

testRunner.addTest('兼容性检查器测试', () => {
  const { compatibilityChecker } = require('./compatibility.js')
  
  // 测试功能支持检查
  const hasGetSystemInfo = compatibilityChecker.checkAPISupport('getSystemInfoSync')
  testRunner.assert.ok(hasGetSystemInfo)
  
  // 测试兼容性报告
  const report = compatibilityChecker.getCompatibilityReport()
  testRunner.assert.ok(report.systemInfo)
  testRunner.assert.type(report.supportedFeatures, 'object')
})

// 错误场景测试
testRunner.addTest('网络错误处理测试', async () => {
  const { api } = require('./api.js')
  
  // 模拟网络错误
  const mockRequest = testRunner.mock.wxApi('request', (options) => {
    options.fail(new Error('网络连接失败'))
  })
  
  try {
    await testRunner.assert.throws(async () => {
      await api.get('/test')
    })
  } finally {
    mockRequest() // 恢复原始API
  }
})

testRunner.addTest('存储错误处理测试', () => {
  const { dataManager } = require('./dataManager.js')
  
  // 模拟存储错误
  const originalSetStorageSync = wx.setStorageSync
  wx.setStorageSync = () => {
    throw new Error('存储空间不足')
  }
  
  try {
    // 应该不会抛出异常，而是优雅处理
    dataManager.setCache('error_key', 'error_value')
    testRunner.assert.ok(true) // 如果没有抛出异常，测试通过
  } finally {
    wx.setStorageSync = originalSetStorageSync
  }
})

// 边界条件测试
testRunner.addTest('边界条件测试', () => {
  const { securityManager } = require('./security.js')
  
  // 测试空值处理
  const emptyMask = securityManager.maskSensitiveData('', 'phone')
  testRunner.assert.equal(emptyMask, '')
  
  const nullMask = securityManager.maskSensitiveData(null, 'phone')
  testRunner.assert.equal(nullMask, null)
  
  // 测试无效输入
  const invalidValidation = securityManager.validateInput('', 'phone')
  testRunner.assert.ok(!invalidValidation.valid)
})

// 导出测试运行器
module.exports = {
  testRunner,
  // 便捷的测试运行函数
  runTests: () => testRunner.runTests(),
  // 添加自定义测试
  addTest: (name, testFunction, options) => testRunner.addTest(name, testFunction, options)
}
