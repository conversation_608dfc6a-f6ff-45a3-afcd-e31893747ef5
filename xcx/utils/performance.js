// utils/performance.js
// 性能监控和优化工具

class PerformanceMonitor {
  constructor() {
    this.pageLoadTimes = new Map()
    this.apiCallTimes = new Map()
    this.memoryUsage = []
    this.isMonitoring = false
  }

  // 开始监控
  startMonitoring() {
    this.isMonitoring = true
    this.startMemoryMonitoring()
  }

  // 停止监控
  stopMonitoring() {
    this.isMonitoring = false
  }

  // 记录页面加载时间
  recordPageLoad(pageName, startTime, endTime) {
    const loadTime = endTime - startTime
    if (!this.pageLoadTimes.has(pageName)) {
      this.pageLoadTimes.set(pageName, [])
    }
    this.pageLoadTimes.get(pageName).push(loadTime)
    
    console.log(`页面 ${pageName} 加载时间: ${loadTime}ms`)
    
    // 如果加载时间过长，给出警告
    if (loadTime > 3000) {
      console.warn(`页面 ${pageName} 加载时间过长: ${loadTime}ms`)
    }
  }

  // 记录API调用时间
  recordApiCall(apiName, startTime, endTime) {
    const callTime = endTime - startTime
    if (!this.apiCallTimes.has(apiName)) {
      this.apiCallTimes.set(apiName, [])
    }
    this.apiCallTimes.get(apiName).push(callTime)
    
    console.log(`API ${apiName} 调用时间: ${callTime}ms`)
    
    // 如果调用时间过长，给出警告
    if (callTime > 5000) {
      console.warn(`API ${apiName} 调用时间过长: ${callTime}ms`)
    }
  }

  // 监控内存使用
  startMemoryMonitoring() {
    if (!this.isMonitoring) return

    const checkMemory = () => {
      if (wx.getPerformance) {
        const performance = wx.getPerformance()
        if (performance.memory) {
          this.memoryUsage.push({
            timestamp: Date.now(),
            usedJSHeapSize: performance.memory.usedJSHeapSize,
            totalJSHeapSize: performance.memory.totalJSHeapSize,
            jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
          })

          // 只保留最近100条记录
          if (this.memoryUsage.length > 100) {
            this.memoryUsage.shift()
          }

          // 检查内存使用是否过高
          const usagePercent = performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit
          if (usagePercent > 0.8) {
            console.warn(`内存使用率过高: ${(usagePercent * 100).toFixed(2)}%`)
          }
        }
      }

      if (this.isMonitoring) {
        setTimeout(checkMemory, 10000) // 每10秒检查一次
      }
    }

    checkMemory()
  }

  // 获取性能报告
  getPerformanceReport() {
    const report = {
      pageLoadTimes: {},
      apiCallTimes: {},
      memoryUsage: this.memoryUsage.slice(-10) // 最近10条内存记录
    }

    // 计算页面加载时间统计
    for (const [pageName, times] of this.pageLoadTimes) {
      const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length
      const maxTime = Math.max(...times)
      const minTime = Math.min(...times)
      
      report.pageLoadTimes[pageName] = {
        average: Math.round(avgTime),
        max: maxTime,
        min: minTime,
        count: times.length
      }
    }

    // 计算API调用时间统计
    for (const [apiName, times] of this.apiCallTimes) {
      const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length
      const maxTime = Math.max(...times)
      const minTime = Math.min(...times)
      
      report.apiCallTimes[apiName] = {
        average: Math.round(avgTime),
        max: maxTime,
        min: minTime,
        count: times.length
      }
    }

    return report
  }

  // 清除监控数据
  clearData() {
    this.pageLoadTimes.clear()
    this.apiCallTimes.clear()
    this.memoryUsage = []
  }
}

// 性能优化工具
class PerformanceOptimizer {
  constructor() {
    this.imageCache = new Map()
    this.lazyLoadObserver = null
  }

  // 图片懒加载
  setupLazyLoad() {
    if (!wx.createIntersectionObserver) return

    this.lazyLoadObserver = wx.createIntersectionObserver()
    this.lazyLoadObserver.relativeToViewport({ bottom: 100 })
    
    return this.lazyLoadObserver
  }

  // 图片预加载
  preloadImages(imageUrls) {
    const promises = imageUrls.map(url => {
      return new Promise((resolve) => {
        if (this.imageCache.has(url)) {
          resolve(url)
          return
        }

        wx.getImageInfo({
          src: url,
          success: () => {
            this.imageCache.set(url, true)
            resolve(url)
          },
          fail: () => {
            resolve(null)
          }
        })
      })
    })

    return Promise.all(promises)
  }

  // 防抖函数
  debounce(func, wait) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  }

  // 节流函数
  throttle(func, limit) {
    let inThrottle
    return function executedFunction(...args) {
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }

  // 分页加载优化
  createPagination(loadFunction, pageSize = 20) {
    let currentPage = 1
    let loading = false
    let hasMore = true

    return {
      async loadMore() {
        if (loading || !hasMore) return []

        loading = true
        try {
          const data = await loadFunction(currentPage, pageSize)
          if (data.length < pageSize) {
            hasMore = false
          }
          currentPage++
          return data
        } catch (error) {
          console.error('分页加载失败:', error)
          return []
        } finally {
          loading = false
        }
      },
      
      reset() {
        currentPage = 1
        loading = false
        hasMore = true
      },

      get isLoading() {
        return loading
      },

      get hasMoreData() {
        return hasMore
      }
    }
  }

  // 长列表优化 - 虚拟滚动
  createVirtualScroll(options) {
    const {
      itemHeight,
      containerHeight,
      buffer = 5
    } = options

    const visibleCount = Math.ceil(containerHeight / itemHeight)
    const totalCount = visibleCount + buffer * 2

    return {
      getVisibleRange(scrollTop, totalItems) {
        const startIndex = Math.floor(scrollTop / itemHeight)
        const endIndex = Math.min(startIndex + totalCount, totalItems)
        const actualStartIndex = Math.max(0, startIndex - buffer)
        
        return {
          startIndex: actualStartIndex,
          endIndex: endIndex,
          offsetY: actualStartIndex * itemHeight
        }
      }
    }
  }

  // 内存清理
  cleanupMemory() {
    // 清理图片缓存
    this.imageCache.clear()
    
    // 清理定时器
    if (this.lazyLoadObserver) {
      this.lazyLoadObserver.disconnect()
      this.lazyLoadObserver = null
    }

    // 建议进行垃圾回收（如果支持）
    if (wx.triggerGC) {
      wx.triggerGC()
    }
  }
}

// 页面性能装饰器
function withPerformanceMonitoring(pageConfig) {
  const originalOnLoad = pageConfig.onLoad || function() {}
  const originalOnShow = pageConfig.onShow || function() {}
  const originalOnHide = pageConfig.onHide || function() {}

  let loadStartTime = 0
  let showStartTime = 0

  pageConfig.onLoad = function(options) {
    loadStartTime = Date.now()
    const result = originalOnLoad.call(this, options)
    
    // 在下一个事件循环中记录加载完成时间
    setTimeout(() => {
      const loadEndTime = Date.now()
      performanceMonitor.recordPageLoad(
        this.route || 'unknown',
        loadStartTime,
        loadEndTime
      )
    }, 0)

    return result
  }

  pageConfig.onShow = function() {
    showStartTime = Date.now()
    return originalOnShow.call(this)
  }

  pageConfig.onHide = function() {
    if (showStartTime > 0) {
      const hideTime = Date.now()
      const stayTime = hideTime - showStartTime
      console.log(`页面停留时间: ${stayTime}ms`)
    }
    return originalOnHide.call(this)
  }

  return pageConfig
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor()
const performanceOptimizer = new PerformanceOptimizer()

// 自动开始监控
performanceMonitor.startMonitoring()

module.exports = {
  performanceMonitor,
  performanceOptimizer,
  withPerformanceMonitoring
}
