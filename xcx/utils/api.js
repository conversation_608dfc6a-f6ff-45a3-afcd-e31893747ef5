// utils/api.js
// 统一的API管理工具

const { dataManager } = require('./dataManager.js')
const config = require('../config/config.js')

class ApiManager {
  constructor() {
    this.baseURL = 'https://baoxian.weixinjishu.top'
    this.timeout = 10000
    this.retryCount = 3
    this.retryDelay = 1000
  }

  // 统一请求方法
  async request(options) {
    const {
      url,
      method = 'GET',
      data = {},
      header = {},
      useCache = false,
      cacheKey = null,
      cacheExpiry = 5 * 60 * 1000,
      retry = true
    } = options

    // 检查缓存
    if (useCache && method === 'GET') {
      const key = cacheKey || this.generateCacheKey(url, data)
      const cached = dataManager.getCache(key)
      if (cached) {
        return cached
      }
    }

    // 获取token
    const token = wx.getStorageSync('token') || wx.getStorageSync('access_token')

    // 构建请求头
    const requestHeader = {
      'Content-Type': 'application/json',
      ...header
    }

    if (token) {
      requestHeader['Authorization'] = `Bearer ${token}`
    }

    // 执行请求
    try {
      const result = await this.executeRequest({
        url: `${this.baseURL}${url}`,
        method,
        data,
        header: requestHeader,
        timeout: this.timeout
      }, retry ? this.retryCount : 0)

      // 缓存结果
      if (useCache && method === 'GET') {
        const key = cacheKey || this.generateCacheKey(url, data)
        dataManager.setCache(key, result, cacheExpiry)
      }

      return result
    } catch (error) {
      this.handleError(error)
      throw error
    }
  }

  // 执行请求（带重试）
  async executeRequest(options, retryCount = 0) {
    return new Promise((resolve, reject) => {
      wx.request({
        ...options,
        success: (res) => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data)
          } else if (res.statusCode === 401) {
            this.handleUnauthorized()
            reject(new Error('登录已过期'))
          } else {
            const error = new Error(res.data?.message || `请求失败 (${res.statusCode})`)
            error.statusCode = res.statusCode
            reject(error)
          }
        },
        fail: async (err) => {
          if (retryCount > 0) {
            console.log(`请求失败，${this.retryDelay}ms后重试，剩余重试次数：${retryCount}`)
            await this.delay(this.retryDelay)
            try {
              const result = await this.executeRequest(options, retryCount - 1)
              resolve(result)
            } catch (retryError) {
              reject(retryError)
            }
          } else {
            reject(err)
          }
        }
      })
    })
  }

  // 处理401未授权
  handleUnauthorized() {
    // 清除token
    wx.removeStorageSync('token')
    wx.removeStorageSync('access_token')
    wx.removeStorageSync('user_info')

    // 更新全局状态
    const app = getApp()
    if (app) {
      app.globalData.isLoggedIn = false
      app.globalData.userInfo = null
    }

    // 显示提示
    wx.showToast({
      title: '登录已过期，请重新登录',
      icon: 'none'
    })

    // 跳转到登录页面
    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/login/login'
      }).catch(() => {
        wx.switchTab({
          url: '/pages/home/<USER>'
        })
      })
    }, 1500)
  }

  // 处理错误
  handleError(error) {
    console.error('API请求错误:', error)

    if (error.message.includes('request:fail')) {
      wx.showToast({
        title: '网络连接失败',
        icon: 'none'
      })
    } else if (error.statusCode === 500) {
      wx.showToast({
        title: '服务器错误',
        icon: 'none'
      })
    } else if (error.statusCode === 404) {
      wx.showToast({
        title: '请求的资源不存在',
        icon: 'none'
      })
    } else if (!error.message.includes('登录已过期')) {
      wx.showToast({
        title: error.message || '请求失败',
        icon: 'none'
      })
    }
  }

  // 生成缓存键
  generateCacheKey(url, data) {
    const params = Object.keys(data).sort().map(key => `${key}=${data[key]}`).join('&')
    return `${url}${params ? '?' + params : ''}`
  }

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // GET请求
  get(url, params = {}, options = {}) {
    const queryString = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')
    const fullUrl = queryString ? `${url}?${queryString}` : url
    
    return this.request({
      url: fullUrl,
      method: 'GET',
      useCache: true,
      ...options
    })
  }

  // POST请求
  post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    })
  }

  // PUT请求
  put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    })
  }

  // DELETE请求
  delete(url, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      ...options
    })
  }

  // 上传文件
  uploadFile(url, filePath, name = 'file', formData = {}) {
    return new Promise((resolve, reject) => {
      const token = wx.getStorageSync('token') || wx.getStorageSync('access_token')
      const header = {}
      
      if (token) {
        header['Authorization'] = `Bearer ${token}`
      }

      wx.uploadFile({
        url: `${this.baseURL}${url}`,
        filePath,
        name,
        formData,
        header,
        success: (res) => {
          try {
            const data = JSON.parse(res.data)
            if (res.statusCode >= 200 && res.statusCode < 300) {
              resolve(data)
            } else {
              reject(new Error(data.message || '上传失败'))
            }
          } catch (e) {
            reject(new Error('响应格式错误'))
          }
        },
        fail: reject
      })
    })
  }

  // 下载文件
  downloadFile(url, options = {}) {
    return new Promise((resolve, reject) => {
      wx.downloadFile({
        url: `${this.baseURL}${url}`,
        success: resolve,
        fail: reject,
        ...options
      })
    })
  }
}

// 创建API实例
const api = new ApiManager()

// 具体的API方法
const apiMethods = {
  // 用户相关
  user: {
    // 获取用户信息
    getProfile: () => api.get('/users/api/profiles/me/'),
    // 更新用户信息
    updateProfile: (data) => api.put('/users/api/profiles/me/', data),
    // 微信登录
    wechatLogin: (code) => api.post('/users/api/wechat-login/', { code }),
    // 退出登录
    logout: () => api.post('/users/api/logout/')
  },

  // 产品相关
  products: {
    // 获取产品列表
    getList: (params) => api.get('/products/api/products/', params, { useCache: true, cacheExpiry: 10 * 60 * 1000 }),
    // 获取产品详情
    getDetail: (id) => api.get(`/products/api/products/${id}/`, {}, { useCache: true }),
    // 搜索产品
    search: (keyword, params) => api.get('/products/api/products/search/', { keyword, ...params }),
    // 获取产品分类
    getCategories: () => api.get('/products/api/categories/', {}, { useCache: true, cacheExpiry: 30 * 60 * 1000 }),
    // 获取热门产品
    getHot: () => api.get('/products/api/products/hot/', {}, { useCache: true, cacheExpiry: 15 * 60 * 1000 })
  },

  // 理赔相关
  claims: {
    // 获取理赔列表
    getList: () => api.get('/claims/api/claims/'),
    // 提交理赔申请
    submit: (data) => api.post('/claims/api/claims/', data),
    // 上传理赔材料
    uploadDocument: (filePath, claimId) => api.uploadFile('/claims/api/documents/', filePath, 'file', { claim_id: claimId })
  },

  // 服务相关
  services: {
    // 预约咨询
    appointment: (data) => api.post('/services/api/appointments/', data),
    // 在线咨询
    consult: (data) => api.post('/services/api/consultations/', data)
  },

  // 订单相关
  orders: {
    // 创建订单
    create: (data) => api.post('/orders/api/orders/', data),
    // 获取订单详情
    getDetail: (id) => api.get(`/orders/api/orders/${id}/`),
    // 支付订单
    pay: (id, paymentData) => api.post(`/orders/api/orders/${id}/pay/`, paymentData)
  }
}

module.exports = {
  api,
  ...apiMethods
}
