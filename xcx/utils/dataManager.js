// utils/dataManager.js
// 数据管理工具类

class DataManager {
  constructor() {
    this.cache = new Map()
    this.cacheExpiry = new Map()
    this.defaultExpiry = 5 * 60 * 1000 // 5分钟默认过期时间
  }

  // 设置缓存
  setCache(key, data, expiry = this.defaultExpiry) {
    this.cache.set(key, data)
    this.cacheExpiry.set(key, Date.now() + expiry)
    
    // 同时存储到本地存储
    try {
      wx.setStorageSync(`cache_${key}`, {
        data: data,
        expiry: Date.now() + expiry
      })
    } catch (e) {
      console.warn('设置本地缓存失败:', e)
    }
  }

  // 获取缓存
  getCache(key) {
    // 先检查内存缓存
    if (this.cache.has(key)) {
      const expiry = this.cacheExpiry.get(key)
      if (Date.now() < expiry) {
        return this.cache.get(key)
      } else {
        // 过期了，清除缓存
        this.clearCache(key)
      }
    }

    // 检查本地存储缓存
    try {
      const cached = wx.getStorageSync(`cache_${key}`)
      if (cached && cached.expiry > Date.now()) {
        // 恢复到内存缓存
        this.cache.set(key, cached.data)
        this.cacheExpiry.set(key, cached.expiry)
        return cached.data
      } else if (cached) {
        // 过期了，清除本地缓存
        wx.removeStorageSync(`cache_${key}`)
      }
    } catch (e) {
      console.warn('获取本地缓存失败:', e)
    }

    return null
  }

  // 清除指定缓存
  clearCache(key) {
    this.cache.delete(key)
    this.cacheExpiry.delete(key)
    try {
      wx.removeStorageSync(`cache_${key}`)
    } catch (e) {
      console.warn('清除本地缓存失败:', e)
    }
  }

  // 清除所有缓存
  clearAllCache() {
    this.cache.clear()
    this.cacheExpiry.clear()
    
    try {
      const info = wx.getStorageInfoSync()
      info.keys.forEach(key => {
        if (key.startsWith('cache_')) {
          wx.removeStorageSync(key)
        }
      })
    } catch (e) {
      console.warn('清除所有本地缓存失败:', e)
    }
  }

  // 检查缓存是否存在且未过期
  hasValidCache(key) {
    return this.getCache(key) !== null
  }

  // 获取缓存大小
  getCacheSize() {
    return this.cache.size
  }

  // 获取所有缓存键
  getCacheKeys() {
    return Array.from(this.cache.keys())
  }
}

// 全局数据状态管理
class GlobalState {
  constructor() {
    this.state = {
      userInfo: null,
      isLoggedIn: false,
      compareProducts: [], // 产品比较列表
      favoriteProducts: [], // 收藏的产品
      recentViewed: [], // 最近浏览
      searchHistory: [] // 搜索历史
    }
    this.listeners = new Map()
    this.loadFromStorage()
  }

  // 从本地存储加载状态
  loadFromStorage() {
    try {
      const userInfo = wx.getStorageSync('user_info')
      const token = wx.getStorageSync('token') || wx.getStorageSync('access_token')
      const compareProducts = wx.getStorageSync('compare_products') || []
      const favoriteProducts = wx.getStorageSync('favorite_products') || []
      const recentViewed = wx.getStorageSync('recent_viewed') || []
      const searchHistory = wx.getStorageSync('search_history') || []

      this.state = {
        userInfo: userInfo,
        isLoggedIn: !!token,
        compareProducts: compareProducts,
        favoriteProducts: favoriteProducts,
        recentViewed: recentViewed,
        searchHistory: searchHistory
      }
    } catch (e) {
      console.warn('加载本地状态失败:', e)
    }
  }

  // 保存状态到本地存储
  saveToStorage(key, value) {
    try {
      wx.setStorageSync(key, value)
    } catch (e) {
      console.warn('保存本地状态失败:', e)
    }
  }

  // 设置状态
  setState(key, value) {
    const oldValue = this.state[key]
    this.state[key] = value

    // 保存到本地存储
    const storageMap = {
      userInfo: 'user_info',
      compareProducts: 'compare_products',
      favoriteProducts: 'favorite_products',
      recentViewed: 'recent_viewed',
      searchHistory: 'search_history'
    }

    if (storageMap[key]) {
      this.saveToStorage(storageMap[key], value)
    }

    // 通知监听器
    this.notifyListeners(key, value, oldValue)
  }

  // 获取状态
  getState(key) {
    return this.state[key]
  }

  // 添加状态监听器
  addListener(key, callback) {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, [])
    }
    this.listeners.get(key).push(callback)
  }

  // 移除状态监听器
  removeListener(key, callback) {
    if (this.listeners.has(key)) {
      const callbacks = this.listeners.get(key)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  // 通知监听器
  notifyListeners(key, newValue, oldValue) {
    if (this.listeners.has(key)) {
      this.listeners.get(key).forEach(callback => {
        try {
          callback(newValue, oldValue)
        } catch (e) {
          console.error('状态监听器执行失败:', e)
        }
      })
    }
  }

  // 添加到比较列表
  addToCompare(product) {
    const compareProducts = [...this.state.compareProducts]
    if (compareProducts.length >= 3) {
      wx.showToast({
        title: '最多只能比较3个产品',
        icon: 'none'
      })
      return false
    }

    const exists = compareProducts.find(p => p.id === product.id)
    if (exists) {
      wx.showToast({
        title: '产品已在比较列表中',
        icon: 'none'
      })
      return false
    }

    compareProducts.push(product)
    this.setState('compareProducts', compareProducts)
    return true
  }

  // 从比较列表移除
  removeFromCompare(productId) {
    const compareProducts = this.state.compareProducts.filter(p => p.id !== productId)
    this.setState('compareProducts', compareProducts)
  }

  // 添加到收藏
  addToFavorite(product) {
    const favoriteProducts = [...this.state.favoriteProducts]
    const exists = favoriteProducts.find(p => p.id === product.id)
    if (!exists) {
      favoriteProducts.unshift(product)
      this.setState('favoriteProducts', favoriteProducts)
      return true
    }
    return false
  }

  // 从收藏移除
  removeFromFavorite(productId) {
    const favoriteProducts = this.state.favoriteProducts.filter(p => p.id !== productId)
    this.setState('favoriteProducts', favoriteProducts)
  }

  // 添加到最近浏览
  addToRecentViewed(product) {
    const recentViewed = [...this.state.recentViewed]
    // 移除已存在的
    const index = recentViewed.findIndex(p => p.id === product.id)
    if (index > -1) {
      recentViewed.splice(index, 1)
    }
    // 添加到开头
    recentViewed.unshift(product)
    // 限制数量
    if (recentViewed.length > 20) {
      recentViewed.splice(20)
    }
    this.setState('recentViewed', recentViewed)
  }

  // 添加搜索历史
  addSearchHistory(keyword) {
    if (!keyword || !keyword.trim()) return

    const searchHistory = [...this.state.searchHistory]
    // 移除已存在的
    const index = searchHistory.indexOf(keyword)
    if (index > -1) {
      searchHistory.splice(index, 1)
    }
    // 添加到开头
    searchHistory.unshift(keyword)
    // 限制数量
    if (searchHistory.length > 10) {
      searchHistory.splice(10)
    }
    this.setState('searchHistory', searchHistory)
  }

  // 清除搜索历史
  clearSearchHistory() {
    this.setState('searchHistory', [])
  }
}

// 创建全局实例
const dataManager = new DataManager()
const globalState = new GlobalState()

module.exports = {
  dataManager,
  globalState
}
