// utils/compatibility.js
// 小程序兼容性检查和处理工具

class CompatibilityChecker {
  constructor() {
    this.systemInfo = null
    this.supportedFeatures = new Map()
    this.init()
  }

  // 初始化系统信息
  init() {
    try {
      this.systemInfo = wx.getSystemInfoSync()
      this.checkFeatureSupport()
    } catch (e) {
      console.error('获取系统信息失败:', e)
    }
  }

  // 检查功能支持情况
  checkFeatureSupport() {
    const features = [
      'getUpdateManager',
      'createIntersectionObserver',
      'createSelectorQuery',
      'getPerformance',
      'triggerGC',
      'getBackgroundAudioManager',
      'createVideoContext',
      'createCameraContext',
      'createLivePlayerContext',
      'createMapContext',
      'createCanvasContext',
      'getFileSystemManager',
      'createWorker',
      'getAccountInfoSync',
      'getEnterOptionsSync',
      'getLaunchOptionsSync'
    ]

    features.forEach(feature => {
      this.supportedFeatures.set(feature, typeof wx[feature] === 'function')
    })
  }

  // 检查微信版本
  checkWechatVersion() {
    if (!this.systemInfo) return null

    const version = this.systemInfo.version
    const versionParts = version.split('.').map(Number)
    
    return {
      version: version,
      major: versionParts[0] || 0,
      minor: versionParts[1] || 0,
      patch: versionParts[2] || 0,
      isSupported: this.isVersionSupported(versionParts)
    }
  }

  // 检查版本是否支持
  isVersionSupported(versionParts) {
    const [major, minor, patch] = versionParts
    
    // 最低支持微信版本 7.0.0
    if (major < 7) return false
    if (major === 7 && minor < 0) return false
    if (major === 7 && minor === 0 && patch < 0) return false
    
    return true
  }

  // 检查基础库版本
  checkSDKVersion() {
    if (!this.systemInfo) return null

    const sdkVersion = this.systemInfo.SDKVersion
    const versionParts = sdkVersion.split('.').map(Number)
    
    return {
      version: sdkVersion,
      major: versionParts[0] || 0,
      minor: versionParts[1] || 0,
      patch: versionParts[2] || 0,
      isSupported: this.isSDKVersionSupported(versionParts)
    }
  }

  // 检查基础库版本是否支持
  isSDKVersionSupported(versionParts) {
    const [major, minor, patch] = versionParts
    
    // 最低支持基础库版本 2.10.0
    if (major < 2) return false
    if (major === 2 && minor < 10) return false
    if (major === 2 && minor === 10 && patch < 0) return false
    
    return true
  }

  // 检查设备信息
  checkDeviceInfo() {
    if (!this.systemInfo) return null

    return {
      platform: this.systemInfo.platform,
      system: this.systemInfo.system,
      model: this.systemInfo.model,
      brand: this.systemInfo.brand,
      screenWidth: this.systemInfo.screenWidth,
      screenHeight: this.systemInfo.screenHeight,
      pixelRatio: this.systemInfo.pixelRatio,
      windowWidth: this.systemInfo.windowWidth,
      windowHeight: this.systemInfo.windowHeight,
      statusBarHeight: this.systemInfo.statusBarHeight,
      safeArea: this.systemInfo.safeArea,
      isIOS: this.systemInfo.platform === 'ios',
      isAndroid: this.systemInfo.platform === 'android',
      isDevTools: this.systemInfo.platform === 'devtools'
    }
  }

  // 检查API支持
  checkAPISupport(apiName) {
    return this.supportedFeatures.get(apiName) || false
  }

  // 安全调用API
  safeCall(apiName, options = {}, fallback = null) {
    if (this.checkAPISupport(apiName)) {
      try {
        return wx[apiName](options)
      } catch (e) {
        console.warn(`API ${apiName} 调用失败:`, e)
        return fallback
      }
    } else {
      console.warn(`API ${apiName} 不支持`)
      return fallback
    }
  }

  // 兼容性适配
  adaptForCompatibility() {
    const deviceInfo = this.checkDeviceInfo()
    const wechatVersion = this.checkWechatVersion()
    const sdkVersion = this.checkSDKVersion()

    // 检查版本兼容性
    if (!wechatVersion?.isSupported || !sdkVersion?.isSupported) {
      this.showVersionWarning()
    }

    // iOS适配
    if (deviceInfo?.isIOS) {
      this.adaptForIOS()
    }

    // Android适配
    if (deviceInfo?.isAndroid) {
      this.adaptForAndroid()
    }

    // 小屏幕适配
    if (deviceInfo && deviceInfo.screenWidth < 375) {
      this.adaptForSmallScreen()
    }

    // 刘海屏适配
    if (deviceInfo?.safeArea) {
      this.adaptForNotch()
    }
  }

  // 显示版本警告
  showVersionWarning() {
    wx.showModal({
      title: '版本提示',
      content: '您的微信版本过低，可能影响部分功能的正常使用，建议升级到最新版本',
      showCancel: false,
      confirmText: '知道了'
    })
  }

  // iOS适配
  adaptForIOS() {
    // iOS特殊处理
    const style = document.createElement('style')
    style.textContent = `
      /* iOS滚动优化 */
      .scroll-view {
        -webkit-overflow-scrolling: touch;
      }
      
      /* iOS输入框优化 */
      input, textarea {
        -webkit-appearance: none;
        border-radius: 0;
      }
      
      /* iOS按钮优化 */
      button {
        -webkit-appearance: none;
        border-radius: 0;
      }
    `
    document.head.appendChild(style)
  }

  // Android适配
  adaptForAndroid() {
    // Android特殊处理
    const style = document.createElement('style')
    style.textContent = `
      /* Android字体优化 */
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      }
      
      /* Android滚动优化 */
      .scroll-view {
        overflow-scrolling: auto;
      }
    `
    document.head.appendChild(style)
  }

  // 小屏幕适配
  adaptForSmallScreen() {
    const style = document.createElement('style')
    style.textContent = `
      /* 小屏幕字体调整 */
      .container {
        font-size: 14px;
      }
      
      /* 小屏幕按钮调整 */
      .btn {
        padding: 8px 16px;
        font-size: 14px;
      }
      
      /* 小屏幕间距调整 */
      .section {
        padding: 16px;
      }
    `
    document.head.appendChild(style)
  }

  // 刘海屏适配
  adaptForNotch() {
    const deviceInfo = this.checkDeviceInfo()
    if (!deviceInfo?.safeArea) return

    const safeAreaTop = deviceInfo.safeArea.top
    const statusBarHeight = deviceInfo.statusBarHeight

    const style = document.createElement('style')
    style.textContent = `
      /* 刘海屏顶部适配 */
      .header-section {
        padding-top: ${safeAreaTop}px;
      }
      
      /* 状态栏高度适配 */
      .status-bar-placeholder {
        height: ${statusBarHeight}px;
      }
    `
    document.head.appendChild(style)
  }

  // 获取兼容性报告
  getCompatibilityReport() {
    return {
      systemInfo: this.systemInfo,
      wechatVersion: this.checkWechatVersion(),
      sdkVersion: this.checkSDKVersion(),
      deviceInfo: this.checkDeviceInfo(),
      supportedFeatures: Object.fromEntries(this.supportedFeatures),
      recommendations: this.getRecommendations()
    }
  }

  // 获取兼容性建议
  getRecommendations() {
    const recommendations = []
    const wechatVersion = this.checkWechatVersion()
    const sdkVersion = this.checkSDKVersion()
    const deviceInfo = this.checkDeviceInfo()

    if (!wechatVersion?.isSupported) {
      recommendations.push('建议升级微信到7.0.0以上版本')
    }

    if (!sdkVersion?.isSupported) {
      recommendations.push('建议升级基础库到2.10.0以上版本')
    }

    if (deviceInfo?.screenWidth < 375) {
      recommendations.push('检测到小屏幕设备，已自动调整界面布局')
    }

    if (!this.checkAPISupport('createIntersectionObserver')) {
      recommendations.push('不支持交叉观察器API，懒加载功能可能受影响')
    }

    if (!this.checkAPISupport('getPerformance')) {
      recommendations.push('不支持性能监控API，性能统计功能不可用')
    }

    return recommendations
  }

  // Polyfill不支持的API
  setupPolyfills() {
    // Promise polyfill
    if (!global.Promise) {
      global.Promise = require('./polyfills/promise.js')
    }

    // Object.assign polyfill
    if (!Object.assign) {
      Object.assign = require('./polyfills/object-assign.js')
    }

    // Array.includes polyfill
    if (!Array.prototype.includes) {
      Array.prototype.includes = function(searchElement, fromIndex) {
        return this.indexOf(searchElement, fromIndex) !== -1
      }
    }

    // String.includes polyfill
    if (!String.prototype.includes) {
      String.prototype.includes = function(search, start) {
        if (typeof start !== 'number') {
          start = 0
        }
        return this.indexOf(search, start) !== -1
      }
    }
  }

  // 功能降级处理
  setupFallbacks() {
    // 如果不支持交叉观察器，使用滚动监听
    if (!this.checkAPISupport('createIntersectionObserver')) {
      this.setupScrollBasedLazyLoad()
    }

    // 如果不支持性能监控，使用简单的时间记录
    if (!this.checkAPISupport('getPerformance')) {
      this.setupSimplePerformanceMonitor()
    }

    // 如果不支持文件系统管理器，使用本地存储
    if (!this.checkAPISupport('getFileSystemManager')) {
      this.setupStorageBasedFileSystem()
    }
  }

  // 基于滚动的懒加载
  setupScrollBasedLazyLoad() {
    // 实现基于滚动事件的懒加载
    console.log('使用滚动事件实现懒加载')
  }

  // 简单性能监控
  setupSimplePerformanceMonitor() {
    // 实现简单的性能监控
    console.log('使用简单时间记录实现性能监控')
  }

  // 基于存储的文件系统
  setupStorageBasedFileSystem() {
    // 实现基于本地存储的文件系统
    console.log('使用本地存储模拟文件系统')
  }
}

// 创建全局实例
const compatibilityChecker = new CompatibilityChecker()

// 自动执行兼容性适配
compatibilityChecker.adaptForCompatibility()
compatibilityChecker.setupPolyfills()
compatibilityChecker.setupFallbacks()

module.exports = {
  compatibilityChecker
}
