// utils/security.js
// 安全性和数据保护工具

class SecurityManager {
  constructor() {
    this.sensitiveFields = ['password', 'phone', 'idCard', 'bankCard']
    this.encryptionKey = 'hk_insurance_2024'
  }

  // 数据脱敏
  maskSensitiveData(data, field) {
    if (!data) return data

    switch (field) {
      case 'phone':
        return data.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
      case 'idCard':
        return data.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
      case 'bankCard':
        return data.replace(/(\d{4})\d{8,12}(\d{4})/, '$1****$2')
      case 'email':
        return data.replace(/(.{2}).*(@.*)/, '$1***$2')
      case 'name':
        return data.replace(/(.{1}).*(.{1})/, '$1*$2')
      default:
        return data
    }
  }

  // 简单加密（仅用于本地存储）
  encrypt(text) {
    if (!text) return text
    
    try {
      let result = ''
      for (let i = 0; i < text.length; i++) {
        const charCode = text.charCodeAt(i)
        const keyChar = this.encryptionKey.charCodeAt(i % this.encryptionKey.length)
        result += String.fromCharCode(charCode ^ keyChar)
      }
      return btoa(result) // Base64编码
    } catch (e) {
      console.warn('加密失败:', e)
      return text
    }
  }

  // 简单解密
  decrypt(encryptedText) {
    if (!encryptedText) return encryptedText
    
    try {
      const decoded = atob(encryptedText) // Base64解码
      let result = ''
      for (let i = 0; i < decoded.length; i++) {
        const charCode = decoded.charCodeAt(i)
        const keyChar = this.encryptionKey.charCodeAt(i % this.encryptionKey.length)
        result += String.fromCharCode(charCode ^ keyChar)
      }
      return result
    } catch (e) {
      console.warn('解密失败:', e)
      return encryptedText
    }
  }

  // 安全存储敏感数据
  setSecureStorage(key, value) {
    try {
      const encryptedValue = this.encrypt(JSON.stringify(value))
      wx.setStorageSync(key, encryptedValue)
      return true
    } catch (e) {
      console.error('安全存储失败:', e)
      return false
    }
  }

  // 安全获取敏感数据
  getSecureStorage(key) {
    try {
      const encryptedValue = wx.getStorageSync(key)
      if (!encryptedValue) return null
      
      const decryptedValue = this.decrypt(encryptedValue)
      return JSON.parse(decryptedValue)
    } catch (e) {
      console.error('安全获取失败:', e)
      return null
    }
  }

  // 清除敏感数据
  clearSensitiveData() {
    const sensitiveKeys = [
      'user_password',
      'payment_info',
      'id_card_info',
      'bank_card_info'
    ]

    sensitiveKeys.forEach(key => {
      try {
        wx.removeStorageSync(key)
      } catch (e) {
        console.warn(`清除敏感数据失败: ${key}`, e)
      }
    })
  }

  // 输入验证
  validateInput(value, type) {
    if (!value) return { valid: false, message: '输入不能为空' }

    switch (type) {
      case 'phone':
        const phoneRegex = /^1[3-9]\d{9}$/
        return {
          valid: phoneRegex.test(value),
          message: phoneRegex.test(value) ? '' : '请输入正确的手机号码'
        }
      
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        return {
          valid: emailRegex.test(value),
          message: emailRegex.test(value) ? '' : '请输入正确的邮箱地址'
        }
      
      case 'idCard':
        const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
        return {
          valid: idCardRegex.test(value),
          message: idCardRegex.test(value) ? '' : '请输入正确的身份证号码'
        }
      
      case 'password':
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/
        return {
          valid: passwordRegex.test(value),
          message: passwordRegex.test(value) ? '' : '密码至少8位，包含大小写字母和数字'
        }
      
      default:
        return { valid: true, message: '' }
    }
  }

  // XSS防护
  sanitizeInput(input) {
    if (typeof input !== 'string') return input
    
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;')
  }

  // 生成随机字符串
  generateRandomString(length = 16) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  // 检查权限
  checkPermission(permission) {
    return new Promise((resolve) => {
      wx.getSetting({
        success: (res) => {
          const granted = res.authSetting[`scope.${permission}`]
          resolve(granted === true)
        },
        fail: () => {
          resolve(false)
        }
      })
    })
  }

  // 请求权限
  requestPermission(permission, reason) {
    return new Promise((resolve, reject) => {
      wx.authorize({
        scope: `scope.${permission}`,
        success: () => {
          resolve(true)
        },
        fail: () => {
          // 权限被拒绝，显示说明并引导用户到设置页面
          wx.showModal({
            title: '权限申请',
            content: reason || `需要${permission}权限才能正常使用此功能`,
            confirmText: '去设置',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    const granted = settingRes.authSetting[`scope.${permission}`]
                    resolve(granted === true)
                  },
                  fail: () => {
                    resolve(false)
                  }
                })
              } else {
                resolve(false)
              }
            }
          })
        }
      })
    })
  }

  // 安全的网络请求
  secureRequest(options) {
    // 添加请求签名
    const timestamp = Date.now()
    const nonce = this.generateRandomString(8)
    
    // 简单的签名算法（实际项目中应使用更安全的算法）
    const signature = this.generateSignature(options.data, timestamp, nonce)
    
    const secureOptions = {
      ...options,
      header: {
        ...options.header,
        'X-Timestamp': timestamp,
        'X-Nonce': nonce,
        'X-Signature': signature
      }
    }

    return wx.request(secureOptions)
  }

  // 生成请求签名
  generateSignature(data, timestamp, nonce) {
    const dataString = JSON.stringify(data || {})
    const signString = `${dataString}${timestamp}${nonce}${this.encryptionKey}`
    
    // 简单的哈希算法（实际项目中应使用SHA-256等）
    let hash = 0
    for (let i = 0; i < signString.length; i++) {
      const char = signString.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return Math.abs(hash).toString(16)
  }

  // 检查应用完整性
  checkAppIntegrity() {
    try {
      // 检查关键配置
      const app = getApp()
      if (!app || !app.globalData) {
        console.warn('应用配置异常')
        return false
      }

      // 检查API地址
      if (!app.globalData.apiBase || !app.globalData.apiBase.startsWith('https://')) {
        console.warn('API地址配置异常')
        return false
      }

      return true
    } catch (e) {
      console.error('应用完整性检查失败:', e)
      return false
    }
  }

  // 数据备份
  backupUserData() {
    try {
      const userData = {
        userInfo: wx.getStorageSync('user_info'),
        favoriteProducts: wx.getStorageSync('favorite_products'),
        searchHistory: wx.getStorageSync('search_history'),
        timestamp: Date.now()
      }

      // 加密备份数据
      const encryptedBackup = this.encrypt(JSON.stringify(userData))
      wx.setStorageSync('user_data_backup', encryptedBackup)
      
      return true
    } catch (e) {
      console.error('数据备份失败:', e)
      return false
    }
  }

  // 数据恢复
  restoreUserData() {
    try {
      const encryptedBackup = wx.getStorageSync('user_data_backup')
      if (!encryptedBackup) return false

      const backupData = JSON.parse(this.decrypt(encryptedBackup))
      
      // 检查备份数据的时效性（7天内）
      const now = Date.now()
      const backupTime = backupData.timestamp
      if (now - backupTime > 7 * 24 * 60 * 60 * 1000) {
        console.warn('备份数据已过期')
        return false
      }

      // 恢复数据
      if (backupData.userInfo) {
        wx.setStorageSync('user_info', backupData.userInfo)
      }
      if (backupData.favoriteProducts) {
        wx.setStorageSync('favorite_products', backupData.favoriteProducts)
      }
      if (backupData.searchHistory) {
        wx.setStorageSync('search_history', backupData.searchHistory)
      }

      return true
    } catch (e) {
      console.error('数据恢复失败:', e)
      return false
    }
  }
}

// 隐私保护工具
class PrivacyManager {
  constructor() {
    this.consentGiven = false
    this.loadConsent()
  }

  // 加载用户同意状态
  loadConsent() {
    try {
      this.consentGiven = wx.getStorageSync('privacy_consent') === 'true'
    } catch (e) {
      console.warn('加载隐私同意状态失败:', e)
    }
  }

  // 请求隐私同意
  requestConsent() {
    return new Promise((resolve) => {
      if (this.consentGiven) {
        resolve(true)
        return
      }

      wx.showModal({
        title: '隐私政策',
        content: '我们需要收集必要的信息为您提供保险服务，请阅读并同意我们的隐私政策',
        confirmText: '同意',
        cancelText: '拒绝',
        success: (res) => {
          if (res.confirm) {
            this.consentGiven = true
            wx.setStorageSync('privacy_consent', 'true')
            resolve(true)
          } else {
            resolve(false)
          }
        }
      })
    })
  }

  // 撤销同意
  revokeConsent() {
    this.consentGiven = false
    wx.removeStorageSync('privacy_consent')
    
    // 清除相关数据
    const securityManager = new SecurityManager()
    securityManager.clearSensitiveData()
  }

  // 检查是否可以收集数据
  canCollectData() {
    return this.consentGiven
  }

  // 数据最小化处理
  minimizeData(data, purpose) {
    if (!this.consentGiven) return {}

    // 根据用途返回最少必要的数据
    switch (purpose) {
      case 'profile':
        return {
          name: data.name,
          phone: data.phone
        }
      case 'insurance':
        return {
          name: data.name,
          phone: data.phone,
          age: data.age,
          gender: data.gender
        }
      case 'analytics':
        return {
          // 匿名化数据
          age_range: this.getAgeRange(data.age),
          gender: data.gender,
          region: data.region
        }
      default:
        return {}
    }
  }

  // 获取年龄范围
  getAgeRange(age) {
    if (age < 18) return '未成年'
    if (age < 30) return '18-29'
    if (age < 40) return '30-39'
    if (age < 50) return '40-49'
    if (age < 60) return '50-59'
    return '60+'
  }
}

// 创建全局实例
const securityManager = new SecurityManager()
const privacyManager = new PrivacyManager()

module.exports = {
  securityManager,
  privacyManager
}
