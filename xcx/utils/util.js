const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`
}

const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

// 收藏功能
const favoriteManager = {
  // 获取收藏列表
  getFavorites() {
    try {
      return wx.getStorageSync('favorites') || []
    } catch (e) {
      console.error('获取收藏列表失败:', e)
      return []
    }
  },

  // 添加收藏
  addFavorite(productId) {
    try {
      const favorites = this.getFavorites()
      if (!favorites.includes(productId)) {
        favorites.push(productId)
        wx.setStorageSync('favorites', favorites)
        return true
      }
      return false
    } catch (e) {
      console.error('添加收藏失败:', e)
      return false
    }
  },

  // 移除收藏
  removeFavorite(productId) {
    try {
      const favorites = this.getFavorites()
      const index = favorites.indexOf(productId)
      if (index > -1) {
        favorites.splice(index, 1)
        wx.setStorageSync('favorites', favorites)
        return true
      }
      return false
    } catch (e) {
      console.error('移除收藏失败:', e)
      return false
    }
  },

  // 检查是否已收藏
  isFavorited(productId) {
    const favorites = this.getFavorites()
    return favorites.includes(productId)
  },

  // 切换收藏状态
  toggleFavorite(productId) {
    if (this.isFavorited(productId)) {
      return this.removeFavorite(productId) ? 'removed' : 'error'
    } else {
      return this.addFavorite(productId) ? 'added' : 'error'
    }
  }
}

// 比较功能
const compareManager = {
  // 获取比较列表
  getCompareList() {
    try {
      return wx.getStorageSync('compare_list') || []
    } catch (e) {
      console.error('获取比较列表失败:', e)
      return []
    }
  },

  // 添加到比较
  addToCompare(productId) {
    try {
      const compareList = this.getCompareList()
      if (compareList.length >= 3) {
        wx.showToast({
          title: '最多只能比较3个产品',
          icon: 'none'
        })
        return false
      }
      if (!compareList.includes(productId)) {
        compareList.push(productId)
        wx.setStorageSync('compare_list', compareList)
        return true
      }
      return false
    } catch (e) {
      console.error('添加比较失败:', e)
      return false
    }
  },

  // 从比较中移除
  removeFromCompare(productId) {
    try {
      const compareList = this.getCompareList()
      const index = compareList.indexOf(productId)
      if (index > -1) {
        compareList.splice(index, 1)
        wx.setStorageSync('compare_list', compareList)
        return true
      }
      return false
    } catch (e) {
      console.error('移除比较失败:', e)
      return false
    }
  },

  // 检查是否在比较列表中
  isInCompare(productId) {
    const compareList = this.getCompareList()
    return compareList.includes(productId)
  },

  // 切换比较状态
  toggleCompare(productId) {
    if (this.isInCompare(productId)) {
      return this.removeFromCompare(productId) ? 'removed' : 'error'
    } else {
      return this.addToCompare(productId) ? 'added' : 'error'
    }
  }
}

module.exports = {
  formatTime,
  formatNumber,
  favoriteManager,
  compareManager
}
