<!--components/loading-state/loading-state.wxml-->
<view class="loading-container" wx:if="{{show}}">
  <!-- 加载中 -->
  <view class="loading-content" wx:if="{{type === 'loading'}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{text || '正在加载...'}}</text>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-content" wx:elif="{{type === 'empty'}}">
    <view class="empty-icon">{{icon || '📋'}}</view>
    <text class="empty-title">{{title || '暂无数据'}}</text>
    <text class="empty-desc" wx:if="{{desc}}">{{desc}}</text>
    <button class="empty-btn" wx:if="{{buttonText}}" bindtap="onButtonTap">{{buttonText}}</button>
  </view>
  
  <!-- 错误状态 -->
  <view class="error-content" wx:elif="{{type === 'error'}}">
    <view class="error-icon">{{icon || '⚠️'}}</view>
    <text class="error-title">{{title || '加载失败'}}</text>
    <text class="error-desc" wx:if="{{desc}}">{{desc}}</text>
    <button class="retry-btn" wx:if="{{showRetry}}" bindtap="onRetryTap">{{retryText || '重试'}}</button>
  </view>
  
  <!-- 网络错误 -->
  <view class="network-content" wx:elif="{{type === 'network'}}">
    <view class="network-icon">📶</view>
    <text class="network-title">网络连接异常</text>
    <text class="network-desc">请检查网络设置后重试</text>
    <button class="retry-btn" bindtap="onRetryTap">重新加载</button>
  </view>
</view>
