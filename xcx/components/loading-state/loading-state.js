// components/loading-state/loading-state.js
Component({
  properties: {
    // 是否显示
    show: {
      type: Boolean,
      value: false
    },
    // 状态类型
    type: {
      type: String,
      value: 'loading' // loading, empty, error, network
    },
    // 图标
    icon: {
      type: String,
      value: ''
    },
    // 标题
    title: {
      type: String,
      value: ''
    },
    // 描述文本
    desc: {
      type: String,
      value: ''
    },
    // 加载文本
    text: {
      type: String,
      value: ''
    },
    // 按钮文本
    buttonText: {
      type: String,
      value: ''
    },
    // 重试按钮文本
    retryText: {
      type: String,
      value: ''
    },
    // 是否显示重试按钮
    showRetry: {
      type: Boolean,
      value: true
    }
  },

  methods: {
    // 按钮点击
    onButtonTap() {
      this.triggerEvent('buttontap')
    },

    // 重试点击
    onRetryTap() {
      this.triggerEvent('retry')
    }
  }
})
