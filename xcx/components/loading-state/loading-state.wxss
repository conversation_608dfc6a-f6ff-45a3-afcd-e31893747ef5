/* components/loading-state/loading-state.wxss */

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
  min-height: 400rpx;
}

/* 加载状态 */
.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #E5E7EB;
  border-top: 4rpx solid #1E40AF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 24rpx;
  color: #6B7280;
}

/* 空状态 */
.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  color: #374151;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 24rpx;
  color: #6B7280;
  margin-bottom: 32rpx;
  line-height: 1.5;
}

.empty-btn {
  background: #1E40AF;
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 错误状态 */
.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.error-title {
  font-size: 32rpx;
  color: #EF4444;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.error-desc {
  font-size: 24rpx;
  color: #6B7280;
  margin-bottom: 32rpx;
  line-height: 1.5;
}

.retry-btn {
  background: #1E40AF;
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 网络错误 */
.network-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.network-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.network-title {
  font-size: 32rpx;
  color: #374151;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.network-desc {
  font-size: 24rpx;
  color: #6B7280;
  margin-bottom: 32rpx;
  line-height: 1.5;
}
