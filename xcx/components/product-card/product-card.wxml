<!--components/product-card/product-card.wxml-->
<view class="product-card" bindtap="onCardTap">
  <!-- 产品标签 -->
  <view class="product-tags" wx:if="{{product.tags && product.tags.length > 0}}">
    <text class="tag hot" wx:if="{{product.is_hot}}">热销</text>
    <text class="tag featured" wx:if="{{product.is_featured}}">推荐</text>
    <text class="tag category">{{product.category_name}}</text>
  </view>

  <!-- 产品图片 -->
  <view class="product-image-container">
    <view class="product-image-placeholder" wx:if="{{!product.main_image}}">
      <text class="placeholder-icon">📋</text>
    </view>
    <image wx:else src="{{product.main_image}}" class="product-image" mode="aspectFill"></image>
  </view>

  <!-- 产品信息 -->
  <view class="product-info">
    <text class="product-name">{{product.name}}</text>
    <text class="product-subtitle" wx:if="{{product.subtitle}}">{{product.subtitle}}</text>
    
    <!-- 产品特性 -->
    <view class="product-specs" wx:if="{{product.specs && product.specs.length > 0}}">
      <view class="spec-item" wx:for="{{product.specs}}" wx:key="label" wx:for-item="spec">
        <text class="spec-label">{{spec.label}}</text>
        <text class="spec-value">{{spec.value}}</text>
      </view>
    </view>
  </view>

  <!-- 产品收益/价格 -->
  <view class="product-meta">
    <text class="product-return">{{product.expected_return || product.price}}</text>
    <text class="product-return-label">{{product.return_label || '起'}}</text>
  </view>

  <!-- 公司信息 -->
  <view class="product-footer" wx:if="{{showCompany}}">
    <view class="company-info">
      <view class="company-logo-placeholder" wx:if="{{!product.company_logo}}">
        <text class="placeholder-icon">🏢</text>
      </view>
      <image wx:else src="{{product.company_logo}}" class="company-logo" mode="aspectFit"></image>
      <text class="company-name">{{product.company_name}}</text>
    </view>
    <view class="product-stats" wx:if="{{product.view_count}}">
      <text class="stat-item">{{product.view_count}}次浏览</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="product-actions" wx:if="{{showActions}}">
    <button class="btn-outline" bindtap="onDetailTap" data-product="{{product}}">查看详情</button>
    <button class="btn-primary" bindtap="onConsultTap" data-product="{{product}}">立即咨询</button>
  </view>
</view>
