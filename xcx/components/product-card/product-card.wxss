/* components/product-card/product-card.wxss */

.product-card {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 12rpx -2rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

/* 产品标签 */
.product-tags {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
  flex-wrap: wrap;
}

.tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  color: white;
}

.tag.hot {
  background: #EF4444;
}

.tag.featured {
  background: #F59E0B;
}

.tag.category {
  background: #6B7280;
}

/* 产品图片 */
.product-image-container {
  width: 100%;
  height: 200rpx;
  margin-bottom: 24rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
}

.product-image-placeholder {
  width: 100%;
  height: 100%;
  background: #F3F4F6;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
}

.placeholder-icon {
  font-size: 40rpx;
  opacity: 0.6;
}

/* 产品信息 */
.product-info {
  margin-bottom: 24rpx;
}

.product-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.product-subtitle {
  display: block;
  font-size: 24rpx;
  color: #6B7280;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

/* 产品特性 */
.product-specs {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 16rpx;
}

.spec-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80rpx;
}

.spec-label {
  font-size: 20rpx;
  color: #6B7280;
  margin-bottom: 4rpx;
}

.spec-value {
  font-size: 24rpx;
  color: #1F2937;
  font-weight: 500;
}

/* 产品收益/价格 */
.product-meta {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
  margin-bottom: 24rpx;
}

.product-return {
  font-size: 36rpx;
  font-weight: bold;
  color: #DC2626;
}

.product-return-label {
  font-size: 24rpx;
  color: #6B7280;
}

/* 公司信息 */
.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #E5E7EB;
}

.company-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.company-logo {
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
}

.company-logo-placeholder {
  width: 48rpx;
  height: 48rpx;
  background: #F3F4F6;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.company-logo-placeholder .placeholder-icon {
  font-size: 24rpx;
}

.company-name {
  font-size: 24rpx;
  color: #6B7280;
}

.product-stats {
  font-size: 20rpx;
  color: #9CA3AF;
}

/* 操作按钮 */
.product-actions {
  display: flex;
  gap: 24rpx;
}

.btn-outline,
.btn-primary {
  flex: 1;
  padding: 20rpx;
  font-size: 24rpx;
  border-radius: 16rpx;
  border: none;
}

.btn-outline {
  background: transparent;
  color: #1E40AF;
  border: 1rpx solid #1E40AF;
}

.btn-primary {
  background: #1E40AF;
  color: white;
}
