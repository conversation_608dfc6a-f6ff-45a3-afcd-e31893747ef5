// components/product-card/product-card.js
Component({
  properties: {
    // 产品数据
    product: {
      type: Object,
      value: {}
    },
    // 是否显示公司信息
    showCompany: {
      type: Boolean,
      value: true
    },
    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      value: true
    },
    // 卡片样式类型
    cardType: {
      type: String,
      value: 'default' // default, compact, simple
    }
  },

  data: {
    
  },

  methods: {
    // 卡片点击
    onCardTap() {
      this.triggerEvent('cardtap', {
        product: this.data.product
      })
    },

    // 查看详情
    onDetailTap(e) {
      e.stopPropagation()
      this.triggerEvent('detail', {
        product: this.data.product
      })
    },

    // 立即咨询
    onConsultTap(e) {
      e.stopPropagation()
      this.triggerEvent('consult', {
        product: this.data.product
      })
    }
  }
})
