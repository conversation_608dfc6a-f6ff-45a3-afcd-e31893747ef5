<!--components/form-group/form-group.wxml-->
<view class="form-group {{required ? 'required' : ''}} {{error ? 'error' : ''}}">
  <text class="form-label" wx:if="{{label}}">{{label}} <text class="required-mark" wx:if="{{required}}">*</text></text>
  
  <!-- 输入框 -->
  <input wx:if="{{type === 'input'}}" 
         class="form-input" 
         placeholder="{{placeholder}}" 
         value="{{value}}" 
         type="{{inputType || 'text'}}"
         maxlength="{{maxlength || -1}}"
         bindinput="onInput"
         bindblur="onBlur"
         bindfocus="onFocus" />
  
  <!-- 文本域 -->
  <textarea wx:elif="{{type === 'textarea'}}" 
            class="form-textarea" 
            placeholder="{{placeholder}}" 
            value="{{value}}" 
            maxlength="{{maxlength || -1}}"
            bindinput="onInput"
            bindblur="onBlur"
            bindfocus="onFocus"></textarea>
  
  <!-- 选择器 -->
  <picker wx:elif="{{type === 'picker'}}" 
          bindchange="onPickerChange" 
          value="{{pickerValue}}" 
          range="{{range}}" 
          range-key="{{rangeKey}}">
    <view class="picker-input">
      <text class="{{!value || value === placeholder ? 'placeholder' : ''}}">{{value || placeholder}}</text>
      <text class="picker-arrow">▼</text>
    </view>
  </picker>
  
  <!-- 多列选择器 -->
  <picker wx:elif="{{type === 'multiPicker'}}" 
          mode="multiSelector"
          bindchange="onMultiPickerChange" 
          value="{{multiPickerValue}}" 
          range="{{multiRange}}">
    <view class="picker-input">
      <text class="{{!value || value === placeholder ? 'placeholder' : ''}}">{{value || placeholder}}</text>
      <text class="picker-arrow">▼</text>
    </view>
  </picker>
  
  <!-- 日期选择器 -->
  <picker wx:elif="{{type === 'date'}}" 
          mode="date"
          bindchange="onDateChange" 
          value="{{value}}"
          start="{{dateStart}}"
          end="{{dateEnd}}">
    <view class="picker-input">
      <text class="{{!value || value === placeholder ? 'placeholder' : ''}}">{{value || placeholder}}</text>
      <text class="picker-arrow">📅</text>
    </view>
  </picker>
  
  <!-- 时间选择器 -->
  <picker wx:elif="{{type === 'time'}}" 
          mode="time"
          bindchange="onTimeChange" 
          value="{{value}}">
    <view class="picker-input">
      <text class="{{!value || value === placeholder ? 'placeholder' : ''}}">{{value || placeholder}}</text>
      <text class="picker-arrow">🕐</text>
    </view>
  </picker>
  
  <!-- 错误提示 -->
  <text class="error-message" wx:if="{{error}}">{{error}}</text>
  
  <!-- 帮助文本 -->
  <text class="help-text" wx:if="{{helpText && !error}}">{{helpText}}</text>
</view>
