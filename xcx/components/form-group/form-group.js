// components/form-group/form-group.js
Component({
  properties: {
    // 表单类型
    type: {
      type: String,
      value: 'input' // input, textarea, picker, multiPicker, date, time
    },
    // 标签文本
    label: {
      type: String,
      value: ''
    },
    // 占位符
    placeholder: {
      type: String,
      value: ''
    },
    // 值
    value: {
      type: String,
      value: ''
    },
    // 是否必填
    required: {
      type: Boolean,
      value: false
    },
    // 错误信息
    error: {
      type: String,
      value: ''
    },
    // 帮助文本
    helpText: {
      type: String,
      value: ''
    },
    // 输入框类型
    inputType: {
      type: String,
      value: 'text' // text, number, idcard, digit
    },
    // 最大长度
    maxlength: {
      type: Number,
      value: -1
    },
    // 选择器选项
    range: {
      type: Array,
      value: []
    },
    // 选择器显示字段
    rangeKey: {
      type: String,
      value: ''
    },
    // 选择器当前值
    pickerValue: {
      type: Number,
      value: 0
    },
    // 多列选择器选项
    multiRange: {
      type: Array,
      value: []
    },
    // 多列选择器当前值
    multiPickerValue: {
      type: Array,
      value: []
    },
    // 日期选择器开始日期
    dateStart: {
      type: String,
      value: ''
    },
    // 日期选择器结束日期
    dateEnd: {
      type: String,
      value: ''
    }
  },

  data: {
    focused: false
  },

  methods: {
    // 输入事件
    onInput(e) {
      const value = e.detail.value
      this.triggerEvent('input', {
        value: value,
        field: this.data.field
      })
    },

    // 失去焦点
    onBlur(e) {
      this.setData({ focused: false })
      this.triggerEvent('blur', {
        value: e.detail.value,
        field: this.data.field
      })
    },

    // 获得焦点
    onFocus(e) {
      this.setData({ focused: true })
      this.triggerEvent('focus', {
        value: e.detail.value,
        field: this.data.field
      })
    },

    // 选择器改变
    onPickerChange(e) {
      const index = e.detail.value
      const selectedItem = this.data.range[index]
      const value = this.data.rangeKey ? selectedItem[this.data.rangeKey] : selectedItem
      
      this.triggerEvent('change', {
        value: value,
        index: index,
        selectedItem: selectedItem,
        field: this.data.field
      })
    },

    // 多列选择器改变
    onMultiPickerChange(e) {
      const value = e.detail.value
      this.triggerEvent('multichange', {
        value: value,
        field: this.data.field
      })
    },

    // 日期改变
    onDateChange(e) {
      const value = e.detail.value
      this.triggerEvent('datechange', {
        value: value,
        field: this.data.field
      })
    },

    // 时间改变
    onTimeChange(e) {
      const value = e.detail.value
      this.triggerEvent('timechange', {
        value: value,
        field: this.data.field
      })
    }
  }
})
