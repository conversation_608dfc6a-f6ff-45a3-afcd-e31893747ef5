/* components/form-group/form-group.wxss */

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #374151;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.required-mark {
  color: #EF4444;
  margin-left: 4rpx;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 24rpx 32rpx;
  border: 1rpx solid #D1D5DB;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #1F2937;
  background: #FFFFFF;
  box-sizing: border-box;
}

.form-input:focus,
.form-textarea:focus {
  border-color: #1E40AF;
  outline: none;
}

.form-textarea {
  min-height: 120rpx;
  resize: none;
}

.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border: 1rpx solid #D1D5DB;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #1F2937;
  background: #FFFFFF;
  min-height: 80rpx;
  box-sizing: border-box;
}

.picker-input .placeholder {
  color: #9CA3AF;
}

.picker-arrow {
  font-size: 24rpx;
  color: #6B7280;
  margin-left: 16rpx;
}

.error-message {
  display: block;
  font-size: 24rpx;
  color: #EF4444;
  margin-top: 8rpx;
}

.help-text {
  display: block;
  font-size: 24rpx;
  color: #6B7280;
  margin-top: 8rpx;
}

/* 错误状态 */
.form-group.error .form-input,
.form-group.error .form-textarea,
.form-group.error .picker-input {
  border-color: #EF4444;
}

.form-group.error .form-label {
  color: #EF4444;
}

/* 必填字段样式 */
.form-group.required .form-label {
  position: relative;
}

/* 聚焦状态 */
.form-group.focused .form-input,
.form-group.focused .form-textarea,
.form-group.focused .picker-input {
  border-color: #1E40AF;
  box-shadow: 0 0 0 3rpx rgba(30, 64, 175, 0.1);
}
