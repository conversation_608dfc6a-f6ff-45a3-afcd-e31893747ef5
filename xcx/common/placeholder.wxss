/* 通用占位符样式 */

/* 公司 Logo 占位符 */
.company-logo-placeholder {
  width: 60rpx;
  height: 60rpx;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #E0E0E0;
}

.company-logo-placeholder .placeholder-icon {
  font-size: 24rpx;
  opacity: 0.6;
}

/* 用户头像占位符 */
.user-avatar-placeholder {
  width: 120rpx;
  height: 120rpx;
  background-color: #F5F5F5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #E0E0E0;
}

.user-avatar-placeholder .placeholder-icon {
  font-size: 48rpx;
  opacity: 0.6;
}

/* 空状态图片占位符 */
.empty-image-placeholder {
  width: 200rpx;
  height: 200rpx;
  background-color: #F8F9FA;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30rpx;
}

.empty-image-placeholder .placeholder-icon {
  font-size: 80rpx;
  opacity: 0.4;
}

/* 产品图片占位符 */
.product-image-placeholder {
  width: 100%;
  height: 100%;
  background-color: #F5F5F5;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  min-height: 200rpx;
}

.product-image-placeholder .placeholder-icon {
  font-size: 40rpx;
  opacity: 0.6;
}

/* 轮播图占位符 */
.banner-image-placeholder {
  width: 100%;
  height: 100%;
  background-color: #E0E0E0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 320rpx;
}

.banner-image-placeholder .placeholder-icon {
  font-size: 60rpx;
  margin-bottom: 10rpx;
  opacity: 0.6;
}

.banner-image-placeholder .placeholder-text {
  font-size: 24rpx;
  color: #757575;
}

/* 通用占位符文字样式 */
.placeholder-icon {
  color: #9E9E9E;
}

.placeholder-text {
  color: #757575;
  font-size: 24rpx;
}
