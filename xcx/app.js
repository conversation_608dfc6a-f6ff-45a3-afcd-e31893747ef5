// app.js
const config = require('./config/config.js')

App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 检查登录状态
    this.checkLoginStatus()
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token')
    if (token) {
      // 验证token是否有效
      this.validateToken(token)
    }
  },

  // 验证token
  validateToken(token) {
    wx.request({
      url: `${this.globalData.apiBase}/users/api/profiles/me/`,
      header: {
        'Authorization': `Bearer ${token}`
      },
      success: (res) => {
        if (res.statusCode === 200) {
          this.globalData.userInfo = res.data
          this.globalData.isLoggedIn = true
        } else {
          this.logout()
        }
      },
      fail: () => {
        this.logout()
      }
    })
  },

  // 微信登录
  wechatLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            // 发送code到后台
            wx.request({
              url: `${this.globalData.apiBase}/users/api/wechat-login/`,
              method: 'POST',
              data: {
                code: res.code
              },
              success: (response) => {
                if (response.statusCode === 200) {
                  const { user, token } = response.data
                  wx.setStorageSync('token', token)
                  this.globalData.userInfo = user
                  this.globalData.isLoggedIn = true
                  resolve(user)
                } else {
                  reject(new Error('登录失败'))
                }
              },
              fail: reject
            })
          } else {
            reject(new Error('获取微信登录code失败'))
          }
        },
        fail: reject
      })
    })
  },

  // 退出登录
  logout() {
    wx.removeStorageSync('token')
    this.globalData.userInfo = null
    this.globalData.isLoggedIn = false
  },

  // 显示加载中
  showLoading(title = '加载中...') {
    wx.showLoading({
      title: title,
      mask: true
    })
  },

  // 隐藏加载中
  hideLoading() {
    wx.hideLoading()
  },

  // 显示提示信息
  showToast(title, icon = 'none') {
    wx.showToast({
      title: title,
      icon: icon,
      duration: 2000
    })
  },

  // 统一的网络请求方法
  request(options) {
    return new Promise((resolve, reject) => {
      // 开发环境下使用模拟数据
      if (this.globalData.isDev) {
        this.getMockData(options.url, options.method, options.data)
          .then(resolve)
          .catch(reject)
        return
      }

      const token = wx.getStorageSync('token') || wx.getStorageSync('access_token')

      wx.request({
        url: `${this.globalData.apiBase}${options.url}`,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          ...options.header
        },
        timeout: config.api.timeout || 10000,
        success: (res) => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data)
          } else if (res.statusCode === 401) {
            // token过期，清除本地存储并重新登录
            this.logout()
            wx.showToast({
              title: '登录已过期，请重新登录',
              icon: 'none'
            })
            // 跳转到登录页面（如果存在）
            setTimeout(() => {
              wx.navigateTo({
                url: '/pages/login/login'
              }).catch(() => {
                // 如果没有登录页面，则跳转到首页
                wx.switchTab({
                  url: '/pages/home/<USER>'
                })
              })
            }, 1500)
            reject(new Error('登录已过期'))
          } else {
            reject(new Error(res.data.message || '请求失败'))
          }
        },
        fail: (err) => {
          wx.showToast({
            title: '网络请求失败',
            icon: 'none'
          })
          reject(err)
        }
      })
    })
  },

  // 模拟数据方法
  getMockData(url, method = 'GET', data = {}) {
    return new Promise((resolve) => {
      setTimeout(() => {
        if (url.includes('/home/')) {
          resolve({
            banners: [
              {
                id: 1,
                title: '储蓄分红保险',
                subtitle: '预期年化收益6.7%',
                image: '/images/banner1.jpg'
              }
            ],
            products: [
              {
                id: 1,
                name: '储蓄分红保险',
                rate: '6.5%',
                description: '长期储蓄，稳健收益'
              },
              {
                id: 2,
                name: '重疾保险',
                coverage: '100+',
                description: '全面保障'
              }
            ]
          })
        } else if (url.includes('/products/')) {
          resolve({
            results: [
              {
                id: 1,
                name: '储蓄分红保险',
                rate: '6.5%',
                description: '长期储蓄，稳健收益',
                image: '/images/product1.jpg'
              },
              {
                id: 2,
                name: '重疾保险',
                coverage: '100+',
                description: '全面保障',
                image: '/images/product2.jpg'
              }
            ],
            count: 2
          })
        } else {
          resolve({ message: '模拟数据' })
        }
      }, 500)
    })
  },

  globalData: {
    userInfo: null,
    isLoggedIn: false,
    isDev: true, // 开发模式标志
    apiBase: 'https://baoxian.weixinjishu.top', // 后端API地址
    config: config // 配置对象
  }
})
