/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
@layer properties{@supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-translate-x:0;--tw-translate-y:0;--tw-translate-z:0;--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-space-y-reverse:0;--tw-border-style:solid;--tw-leading:initial;--tw-font-weight:initial;--tw-tracking:initial;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-outline-style:solid;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial;--tw-backdrop-blur:initial;--tw-backdrop-brightness:initial;--tw-backdrop-contrast:initial;--tw-backdrop-grayscale:initial;--tw-backdrop-hue-rotate:initial;--tw-backdrop-invert:initial;--tw-backdrop-opacity:initial;--tw-backdrop-saturate:initial;--tw-backdrop-sepia:initial;--tw-duration:initial;--tw-ease:initial;--tw-content:"";--tw-gradient-position:initial;--tw-gradient-from:#0000;--tw-gradient-via:#0000;--tw-gradient-to:#0000;--tw-gradient-stops:initial;--tw-gradient-via-stops:initial;--tw-gradient-from-position:0%;--tw-gradient-via-position:50%;--tw-gradient-to-position:100%;--tw-space-x-reverse:0}}}@layer theme{:root,:host{--font-sans:"Inter",sans-serif;--font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--color-red-50:oklch(97.1% .013 17.38);--color-red-100:oklch(93.6% .032 17.717);--color-red-200:oklch(88.5% .062 18.334);--color-red-300:oklch(80.8% .114 19.571);--color-red-400:oklch(70.4% .191 22.216);--color-red-500:oklch(63.7% .237 25.331);--color-red-600:oklch(57.7% .245 27.325);--color-red-700:oklch(50.5% .213 27.518);--color-orange-100:oklch(95.4% .038 75.164);--color-orange-400:oklch(75% .183 55.934);--color-orange-500:oklch(70.5% .213 47.604);--color-orange-600:oklch(64.6% .222 41.116);--color-orange-700:oklch(55.3% .195 38.402);--color-amber-100:oklch(96.2% .059 95.617);--color-amber-600:oklch(66.6% .179 58.318);--color-yellow-200:oklch(94.5% .129 101.54);--color-green-100:oklch(96.2% .044 156.743);--color-green-200:oklch(92.5% .084 155.995);--color-green-400:oklch(79.2% .209 151.711);--color-green-500:oklch(72.3% .219 149.579);--color-green-600:oklch(62.7% .194 149.214);--color-green-700:oklch(52.7% .154 150.069);--color-blue-50:oklch(97% .014 254.604);--color-blue-100:oklch(93.2% .032 255.585);--color-blue-200:oklch(88.2% .059 254.128);--color-blue-400:oklch(70.7% .165 254.624);--color-blue-500:oklch(62.3% .214 259.815);--color-blue-600:oklch(54.6% .245 262.881);--color-blue-700:oklch(48.8% .243 264.376);--color-gray-50:oklch(98.5% .002 247.839);--color-gray-100:oklch(96.7% .003 264.542);--color-gray-200:oklch(92.8% .006 264.531);--color-gray-500:oklch(55.1% .027 264.364);--color-gray-700:oklch(37.3% .034 259.733);--color-gray-900:oklch(21% .034 264.665);--color-white:#fff;--spacing:.25rem;--container-2xl:42rem;--container-3xl:48rem;--container-4xl:56rem;--container-5xl:64rem;--text-xs:.75rem;--text-xs--line-height:calc(1/.75);--text-sm:.875rem;--text-sm--line-height:calc(1.25/.875);--text-base:1rem;--text-base--line-height:calc(1.5/1);--text-lg:1.125rem;--text-lg--line-height:calc(1.75/1.125);--text-xl:1.25rem;--text-xl--line-height:calc(1.75/1.25);--text-2xl:1.5rem;--text-2xl--line-height:calc(2/1.5);--text-5xl:3rem;--text-5xl--line-height:1;--text-6xl:3.75rem;--text-6xl--line-height:1;--font-weight-normal:400;--font-weight-medium:500;--font-weight-semibold:600;--tracking-tight:-.025em;--leading-normal:1.5;--leading-relaxed:1.625;--radius-xs:.125rem;--ease-in-out:cubic-bezier(.4,0,.2,1);--animate-spin:spin 1s linear infinite;--blur-xs:4px;--default-transition-duration:.15s;--default-transition-timing-function:cubic-bezier(.4,0,.2,1);--default-font-family:var(--font-sans);--default-mono-font-family:var(--font-mono);--color-primary-50:rgb(var(--color-primary-50));--color-primary-100:rgb(var(--color-primary-100));--color-primary-200:rgb(var(--color-primary-200));--color-primary-300:rgb(var(--color-primary-300));--color-primary-400:rgb(var(--color-primary-400));--color-primary-500:rgb(var(--color-primary-500));--color-primary-600:rgb(var(--color-primary-600));--color-primary-700:rgb(var(--color-primary-700));--color-primary-800:rgb(var(--color-primary-800));--color-primary-900:rgb(var(--color-primary-900));--color-primary-950:rgb(var(--color-primary-950));--color-base-50:rgb(var(--color-base-50));--color-base-100:rgb(var(--color-base-100));--color-base-200:rgb(var(--color-base-200));--color-base-300:rgb(var(--color-base-300));--color-base-400:rgb(var(--color-base-400));--color-base-500:rgb(var(--color-base-500));--color-base-600:rgb(var(--color-base-600));--color-base-700:rgb(var(--color-base-700));--color-base-800:rgb(var(--color-base-800));--color-base-900:rgb(var(--color-base-900));--color-base-950:rgb(var(--color-base-950));--color-font-subtle-light:rgb(var(--color-font-subtle-light));--color-font-subtle-dark:rgb(var(--color-font-subtle-dark));--color-font-default-light:rgb(var(--color-font-default-light));--color-font-default-dark:rgb(var(--color-font-default-dark));--color-font-important-light:rgb(var(--color-font-important-light));--color-font-important-dark:rgb(var(--color-font-important-dark))}}@layer base{*,:after,:before,::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}html,:host{-webkit-text-size-adjust:100%;tab-size:4;line-height:1.5;font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");font-feature-settings:var(--default-font-feature-settings,normal);font-variation-settings:var(--default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);font-feature-settings:var(--default-mono-font-feature-settings,normal);font-variation-settings:var(--default-mono-font-variation-settings,normal);font-size:1em}small{font-size:80%}sub,sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}:-moz-focusring{outline:auto}progress{vertical-align:baseline}summary{display:list-item}ol,ul,menu{list-style:none}img,svg,video,canvas,audio,iframe,embed,object{vertical-align:middle;display:block}img,video{max-width:100%;height:auto}button,input,select,optgroup,textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}:where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}::file-selector-button{margin-inline-end:4px}::placeholder{opacity:1}@supports (not ((-webkit-appearance:-apple-pay-button))) or (contain-intrinsic-size:1px){::placeholder{color:currentColor}@supports (color:color-mix(in lab, red, red)){::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}textarea{resize:vertical}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-datetime-edit{padding-block:0}::-webkit-datetime-edit-year-field{padding-block:0}::-webkit-datetime-edit-month-field{padding-block:0}::-webkit-datetime-edit-day-field{padding-block:0}::-webkit-datetime-edit-hour-field{padding-block:0}::-webkit-datetime-edit-minute-field{padding-block:0}::-webkit-datetime-edit-second-field{padding-block:0}::-webkit-datetime-edit-millisecond-field{padding-block:0}::-webkit-datetime-edit-meridiem-field{padding-block:0}:-moz-ui-invalid{box-shadow:none}button,input:where([type=button],[type=reset],[type=submit]){appearance:button}::file-selector-button{appearance:button}::-webkit-inner-spin-button{height:auto}::-webkit-outer-spin-button{height:auto}[hidden]:where(:not([hidden=until-found])){display:none!important}*,:after,:before,::backdrop{border-color:var(--color-gray-200,currentColor)}::file-selector-button{border-color:var(--color-gray-200,currentColor)}}@layer components;@layer utilities{.\@container{container-type:inline-size}.pointer-events-none{pointer-events:none}.collapse{visibility:collapse}.visible{visibility:visible}.absolute{position:absolute}.fixed{position:fixed}.relative{position:relative}.static{position:static}.sticky{position:sticky}.inset-0{inset:calc(var(--spacing)*0)}.-top-0{top:calc(var(--spacing)*0)}.-top-1{top:calc(var(--spacing)*-1)}.-top-2{top:calc(var(--spacing)*-2)}.-top-3{top:calc(var(--spacing)*-3)}.-top-4{top:calc(var(--spacing)*-4)}.-top-5{top:calc(var(--spacing)*-5)}.-top-6{top:calc(var(--spacing)*-6)}.top-0{top:calc(var(--spacing)*0)}.top-1{top:calc(var(--spacing)*1)}.top-1\/2{top:50%}.top-2{top:calc(var(--spacing)*2)}.top-3{top:calc(var(--spacing)*3)}.top-3\!{top:calc(var(--spacing)*3)!important}.top-4{top:calc(var(--spacing)*4)}.top-5{top:calc(var(--spacing)*5)}.top-6{top:calc(var(--spacing)*6)}.top-7{top:calc(var(--spacing)*7)}.top-10{top:calc(var(--spacing)*10)}.top-\[73px\]{top:73px}.-right-0{right:calc(var(--spacing)*0)}.-right-1{right:calc(var(--spacing)*-1)}.-right-2{right:calc(var(--spacing)*-2)}.-right-3{right:calc(var(--spacing)*-3)}.-right-4{right:calc(var(--spacing)*-4)}.-right-5{right:calc(var(--spacing)*-5)}.-right-6{right:calc(var(--spacing)*-6)}.right-0{right:calc(var(--spacing)*0)}.right-1{right:calc(var(--spacing)*1)}.right-2{right:calc(var(--spacing)*2)}.right-3{right:calc(var(--spacing)*3)}.right-4{right:calc(var(--spacing)*4)}.right-5{right:calc(var(--spacing)*5)}.right-6{right:calc(var(--spacing)*6)}.-bottom-0{bottom:calc(var(--spacing)*0)}.-bottom-1{bottom:calc(var(--spacing)*-1)}.-bottom-2{bottom:calc(var(--spacing)*-2)}.-bottom-3{bottom:calc(var(--spacing)*-3)}.-bottom-4{bottom:calc(var(--spacing)*-4)}.-bottom-5{bottom:calc(var(--spacing)*-5)}.-bottom-6{bottom:calc(var(--spacing)*-6)}.-bottom-px{bottom:-1px}.bottom-0{bottom:calc(var(--spacing)*0)}.bottom-1{bottom:calc(var(--spacing)*1)}.bottom-2{bottom:calc(var(--spacing)*2)}.bottom-3{bottom:calc(var(--spacing)*3)}.bottom-4{bottom:calc(var(--spacing)*4)}.bottom-5{bottom:calc(var(--spacing)*5)}.bottom-6{bottom:calc(var(--spacing)*6)}.-left-0{left:calc(var(--spacing)*0)}.-left-1{left:calc(var(--spacing)*-1)}.-left-2{left:calc(var(--spacing)*-2)}.-left-3{left:calc(var(--spacing)*-3)}.-left-4{left:calc(var(--spacing)*-4)}.-left-5{left:calc(var(--spacing)*-5)}.-left-6{left:calc(var(--spacing)*-6)}.left-0{left:calc(var(--spacing)*0)}.left-1{left:calc(var(--spacing)*1)}.left-2{left:calc(var(--spacing)*2)}.left-3{left:calc(var(--spacing)*3)}.left-4{left:calc(var(--spacing)*4)}.left-5{left:calc(var(--spacing)*5)}.left-6{left:calc(var(--spacing)*6)}.left-12{left:calc(var(--spacing)*12)}.left-\[65px\]{left:65px}.z-10{z-index:10}.z-20{z-index:20}.z-30{z-index:30}.z-40{z-index:40}.z-50{z-index:50}.col-span-1{grid-column:span 1/span 1}.col-span-2{grid-column:span 2/span 2}.col-span-3{grid-column:span 3/span 3}.col-span-4{grid-column:span 4/span 4}.col-span-5{grid-column:span 5/span 5}.col-span-6{grid-column:span 6/span 6}.col-span-7{grid-column:span 7/span 7}.col-span-8{grid-column:span 8/span 8}.col-span-9{grid-column:span 9/span 9}.col-span-10{grid-column:span 10/span 10}.col-span-11{grid-column:span 11/span 11}.col-span-12{grid-column:span 12/span 12}.float-left{float:left}.float-right{float:right}.container{width:100%}@media (min-width:40rem){.container{max-width:40rem}}@media (min-width:48rem){.container{max-width:48rem}}@media (min-width:64rem){.container{max-width:64rem}}@media (min-width:80rem){.container{max-width:80rem}}@media (min-width:96rem){.container{max-width:96rem}}.-m-0{margin:calc(var(--spacing)*0)}.-m-1{margin:calc(var(--spacing)*-1)}.-m-2{margin:calc(var(--spacing)*-2)}.-m-3{margin:calc(var(--spacing)*-3)}.-m-4{margin:calc(var(--spacing)*-4)}.-m-5{margin:calc(var(--spacing)*-5)}.-m-6{margin:calc(var(--spacing)*-6)}.m-0{margin:calc(var(--spacing)*0)}.m-0\!{margin:calc(var(--spacing)*0)!important}.m-1{margin:calc(var(--spacing)*1)}.m-2{margin:calc(var(--spacing)*2)}.m-3{margin:calc(var(--spacing)*3)}.m-4{margin:calc(var(--spacing)*4)}.m-5{margin:calc(var(--spacing)*5)}.m-6{margin:calc(var(--spacing)*6)}.-mx-0{margin-inline:calc(var(--spacing)*0)}.-mx-1{margin-inline:calc(var(--spacing)*-1)}.-mx-2{margin-inline:calc(var(--spacing)*-2)}.-mx-3{margin-inline:calc(var(--spacing)*-3)}.-mx-4{margin-inline:calc(var(--spacing)*-4)}.-mx-5{margin-inline:calc(var(--spacing)*-5)}.-mx-6{margin-inline:calc(var(--spacing)*-6)}.-mx-px{margin-inline:-1px}.mx-0{margin-inline:calc(var(--spacing)*0)}.mx-1{margin-inline:calc(var(--spacing)*1)}.mx-2{margin-inline:calc(var(--spacing)*2)}.mx-3{margin-inline:calc(var(--spacing)*3)}.mx-4{margin-inline:calc(var(--spacing)*4)}.mx-5{margin-inline:calc(var(--spacing)*5)}.mx-6{margin-inline:calc(var(--spacing)*6)}.mx-auto{margin-inline:auto}.-my-0{margin-block:calc(var(--spacing)*0)}.-my-1{margin-block:calc(var(--spacing)*-1)}.-my-2{margin-block:calc(var(--spacing)*-2)}.-my-3{margin-block:calc(var(--spacing)*-3)}.-my-4{margin-block:calc(var(--spacing)*-4)}.-my-5{margin-block:calc(var(--spacing)*-5)}.-my-6{margin-block:calc(var(--spacing)*-6)}.my-0{margin-block:calc(var(--spacing)*0)}.my-1{margin-block:calc(var(--spacing)*1)}.my-2{margin-block:calc(var(--spacing)*2)}.my-3{margin-block:calc(var(--spacing)*3)}.my-4{margin-block:calc(var(--spacing)*4)}.my-5{margin-block:calc(var(--spacing)*5)}.my-6{margin-block:calc(var(--spacing)*6)}.my-8{margin-block:calc(var(--spacing)*8)}.my-12{margin-block:calc(var(--spacing)*12)}.prose{color:var(--tw-prose-body);max-width:65ch}.prose :where(p):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.25em;margin-bottom:1.25em}.prose :where([class~=lead]):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-lead);margin-top:1.2em;margin-bottom:1.2em;font-size:1.25em;line-height:1.6}.prose :where(a):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-links);font-weight:500;text-decoration:underline}.prose :where(strong):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-bold);font-weight:600}.prose :where(a strong):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(blockquote strong):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(thead th strong):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit}.prose :where(ol):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.25em;margin-bottom:1.25em;padding-inline-start:1.625em;list-style-type:decimal}.prose :where(ol[type=A]):not(:where([class~=not-prose],[class~=not-prose] *)){list-style-type:upper-alpha}.prose :where(ol[type=a]):not(:where([class~=not-prose],[class~=not-prose] *)){list-style-type:lower-alpha}.prose :where(ol[type=A s]):not(:where([class~=not-prose],[class~=not-prose] *)){list-style-type:upper-alpha}.prose :where(ol[type=a s]):not(:where([class~=not-prose],[class~=not-prose] *)){list-style-type:lower-alpha}.prose :where(ol[type=I]):not(:where([class~=not-prose],[class~=not-prose] *)){list-style-type:upper-roman}.prose :where(ol[type=i]):not(:where([class~=not-prose],[class~=not-prose] *)){list-style-type:lower-roman}.prose :where(ol[type=I s]):not(:where([class~=not-prose],[class~=not-prose] *)){list-style-type:upper-roman}.prose :where(ol[type=i s]):not(:where([class~=not-prose],[class~=not-prose] *)){list-style-type:lower-roman}.prose :where(ol[type="1"]):not(:where([class~=not-prose],[class~=not-prose] *)){list-style-type:decimal}.prose :where(ul):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.25em;margin-bottom:1.25em;padding-inline-start:1.625em;list-style-type:disc}.prose :where(ol>li):not(:where([class~=not-prose],[class~=not-prose] *))::marker{color:var(--tw-prose-counters);font-weight:400}.prose :where(ul>li):not(:where([class~=not-prose],[class~=not-prose] *))::marker{color:var(--tw-prose-bullets)}.prose :where(dt):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-headings);margin-top:1.25em;font-weight:600}.prose :where(hr):not(:where([class~=not-prose],[class~=not-prose] *)){border-color:var(--tw-prose-hr);border-top-width:1px;margin-top:3em;margin-bottom:3em}.prose :where(blockquote):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-quotes);border-inline-start-width:.25rem;border-inline-start-color:var(--tw-prose-quote-borders);quotes:"“""”""‘""’";margin-top:1.6em;margin-bottom:1.6em;padding-inline-start:1em;font-style:italic;font-weight:500}.prose :where(blockquote p:first-of-type):not(:where([class~=not-prose],[class~=not-prose] *)):before{content:open-quote}.prose :where(blockquote p:last-of-type):not(:where([class~=not-prose],[class~=not-prose] *)):after{content:close-quote}.prose :where(h1):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-headings);margin-top:0;margin-bottom:.888889em;font-size:2.25em;font-weight:800;line-height:1.11111}.prose :where(h1 strong):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit;font-weight:900}.prose :where(h2):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-headings);margin-top:2em;margin-bottom:1em;font-size:1.5em;font-weight:700;line-height:1.33333}.prose :where(h2 strong):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit;font-weight:800}.prose :where(h3):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-headings);margin-top:1.6em;margin-bottom:.6em;font-size:1.25em;font-weight:600;line-height:1.6}.prose :where(h3 strong):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit;font-weight:700}.prose :where(h4):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-headings);margin-top:1.5em;margin-bottom:.5em;font-weight:600;line-height:1.5}.prose :where(h4 strong):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit;font-weight:700}.prose :where(img):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:2em;margin-bottom:2em}.prose :where(picture):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:2em;margin-bottom:2em;display:block}.prose :where(video):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:2em;margin-bottom:2em}.prose :where(kbd):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-kbd);box-shadow:0 0 0 1px rgb(var(--tw-prose-kbd-shadows)/10%),0 3px 0 rgb(var(--tw-prose-kbd-shadows)/10%);padding-top:.1875em;padding-inline-end:.375em;padding-bottom:.1875em;border-radius:.3125rem;padding-inline-start:.375em;font-family:inherit;font-size:.875em;font-weight:500}.prose :where(code):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-code);font-size:.875em;font-weight:600}.prose :where(code):not(:where([class~=not-prose],[class~=not-prose] *)):before,.prose :where(code):not(:where([class~=not-prose],[class~=not-prose] *)):after{content:"`"}.prose :where(a code):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(h1 code):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit}.prose :where(h2 code):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit;font-size:.875em}.prose :where(h3 code):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit;font-size:.9em}.prose :where(h4 code):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(blockquote code):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(thead th code):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit}.prose :where(pre):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-pre-code);background-color:var(--tw-prose-pre-bg);padding-top:.857143em;padding-inline-end:1.14286em;padding-bottom:.857143em;border-radius:.375rem;margin-top:1.71429em;margin-bottom:1.71429em;padding-inline-start:1.14286em;font-size:.875em;font-weight:400;line-height:1.71429;overflow-x:auto}.prose :where(pre code):not(:where([class~=not-prose],[class~=not-prose] *)){font-weight:inherit;color:inherit;font-size:inherit;font-family:inherit;line-height:inherit;background-color:#0000;border-width:0;border-radius:0;padding:0}.prose :where(pre code):not(:where([class~=not-prose],[class~=not-prose] *)):before,.prose :where(pre code):not(:where([class~=not-prose],[class~=not-prose] *)):after{content:none}.prose :where(table):not(:where([class~=not-prose],[class~=not-prose] *)){table-layout:auto;width:100%;margin-top:2em;margin-bottom:2em;font-size:.875em;line-height:1.71429}.prose :where(thead):not(:where([class~=not-prose],[class~=not-prose] *)){border-bottom-width:1px;border-bottom-color:var(--tw-prose-th-borders)}.prose :where(thead th):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-headings);vertical-align:bottom;padding-inline-end:.571429em;padding-bottom:.571429em;padding-inline-start:.571429em;font-weight:600}.prose :where(tbody tr):not(:where([class~=not-prose],[class~=not-prose] *)){border-bottom-width:1px;border-bottom-color:var(--tw-prose-td-borders)}.prose :where(tbody tr:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){border-bottom-width:0}.prose :where(tbody td):not(:where([class~=not-prose],[class~=not-prose] *)){vertical-align:baseline}.prose :where(tfoot):not(:where([class~=not-prose],[class~=not-prose] *)){border-top-width:1px;border-top-color:var(--tw-prose-th-borders)}.prose :where(tfoot td):not(:where([class~=not-prose],[class~=not-prose] *)){vertical-align:top}.prose :where(th,td):not(:where([class~=not-prose],[class~=not-prose] *)){text-align:start}.prose :where(figure>*):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0;margin-bottom:0}.prose :where(figcaption):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-captions);margin-top:.857143em;font-size:.875em;line-height:1.42857}.prose{--tw-prose-body:oklch(37.3% .034 259.733);--tw-prose-headings:oklch(21% .034 264.665);--tw-prose-lead:oklch(44.6% .03 256.802);--tw-prose-links:oklch(21% .034 264.665);--tw-prose-bold:oklch(21% .034 264.665);--tw-prose-counters:oklch(55.1% .027 264.364);--tw-prose-bullets:oklch(87.2% .01 258.338);--tw-prose-hr:oklch(92.8% .006 264.531);--tw-prose-quotes:oklch(21% .034 264.665);--tw-prose-quote-borders:oklch(92.8% .006 264.531);--tw-prose-captions:oklch(55.1% .027 264.364);--tw-prose-kbd:oklch(21% .034 264.665);--tw-prose-kbd-shadows:NaN NaN NaN;--tw-prose-code:oklch(21% .034 264.665);--tw-prose-pre-code:oklch(92.8% .006 264.531);--tw-prose-pre-bg:oklch(27.8% .033 256.848);--tw-prose-th-borders:oklch(87.2% .01 258.338);--tw-prose-td-borders:oklch(92.8% .006 264.531);--tw-prose-invert-body:oklch(87.2% .01 258.338);--tw-prose-invert-headings:#fff;--tw-prose-invert-lead:oklch(70.7% .022 261.325);--tw-prose-invert-links:#fff;--tw-prose-invert-bold:#fff;--tw-prose-invert-counters:oklch(70.7% .022 261.325);--tw-prose-invert-bullets:oklch(44.6% .03 256.802);--tw-prose-invert-hr:oklch(37.3% .034 259.733);--tw-prose-invert-quotes:oklch(96.7% .003 264.542);--tw-prose-invert-quote-borders:oklch(37.3% .034 259.733);--tw-prose-invert-captions:oklch(70.7% .022 261.325);--tw-prose-invert-kbd:#fff;--tw-prose-invert-kbd-shadows:255 255 255;--tw-prose-invert-code:#fff;--tw-prose-invert-pre-code:oklch(87.2% .01 258.338);--tw-prose-invert-pre-bg:#00000080;--tw-prose-invert-th-borders:oklch(44.6% .03 256.802);--tw-prose-invert-td-borders:oklch(37.3% .034 259.733);font-size:1rem;line-height:1.75}.prose :where(picture>img):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0;margin-bottom:0}.prose :where(li):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:.5em;margin-bottom:.5em}.prose :where(ol>li):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(ul>li):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-start:.375em}.prose :where(.prose>ul>li p):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:.75em;margin-bottom:.75em}.prose :where(.prose>ul>li>p:first-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.25em}.prose :where(.prose>ul>li>p:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-bottom:1.25em}.prose :where(.prose>ol>li>p:first-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.25em}.prose :where(.prose>ol>li>p:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-bottom:1.25em}.prose :where(ul ul,ul ol,ol ul,ol ol):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:.75em;margin-bottom:.75em}.prose :where(dl):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.25em;margin-bottom:1.25em}.prose :where(dd):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:.5em;padding-inline-start:1.625em}.prose :where(hr+*):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(h2+*):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(h3+*):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(h4+*):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0}.prose :where(thead th:first-child):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-start:0}.prose :where(thead th:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-end:0}.prose :where(tbody td,tfoot td):not(:where([class~=not-prose],[class~=not-prose] *)){padding-top:.571429em;padding-inline-end:.571429em;padding-bottom:.571429em;padding-inline-start:.571429em}.prose :where(tbody td:first-child,tfoot td:first-child):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-start:0}.prose :where(tbody td:last-child,tfoot td:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-end:0}.prose :where(figure):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:2em;margin-bottom:2em}.prose :where(.prose>:first-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0}.prose :where(.prose>:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-bottom:0}.prose-sm{font-size:.875rem;line-height:1.71429}.prose-sm :where(p):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.14286em;margin-bottom:1.14286em}.prose-sm :where([class~=lead]):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:.888889em;margin-bottom:.888889em;font-size:1.28571em;line-height:1.55556}.prose-sm :where(blockquote):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.33333em;margin-bottom:1.33333em;padding-inline-start:1.11111em}.prose-sm :where(h1):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0;margin-bottom:.8em;font-size:2.14286em;line-height:1.2}.prose-sm :where(h2):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.6em;margin-bottom:.8em;font-size:1.42857em;line-height:1.4}.prose-sm :where(h3):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.55556em;margin-bottom:.444444em;font-size:1.28571em;line-height:1.55556}.prose-sm :where(h4):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.42857em;margin-bottom:.571429em;line-height:1.42857}.prose-sm :where(img):not(:where([class~=not-prose],[class~=not-prose] *)),.prose-sm :where(picture):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.71429em;margin-bottom:1.71429em}.prose-sm :where(picture>img):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0;margin-bottom:0}.prose-sm :where(video):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.71429em;margin-bottom:1.71429em}.prose-sm :where(kbd):not(:where([class~=not-prose],[class~=not-prose] *)){padding-top:.142857em;padding-inline-end:.357143em;padding-bottom:.142857em;border-radius:.3125rem;padding-inline-start:.357143em;font-size:.857143em}.prose-sm :where(code):not(:where([class~=not-prose],[class~=not-prose] *)){font-size:.857143em}.prose-sm :where(h2 code):not(:where([class~=not-prose],[class~=not-prose] *)){font-size:.9em}.prose-sm :where(h3 code):not(:where([class~=not-prose],[class~=not-prose] *)){font-size:.888889em}.prose-sm :where(pre):not(:where([class~=not-prose],[class~=not-prose] *)){padding-top:.666667em;padding-inline-end:1em;padding-bottom:.666667em;border-radius:.25rem;margin-top:1.66667em;margin-bottom:1.66667em;padding-inline-start:1em;font-size:.857143em;line-height:1.66667}.prose-sm :where(ol):not(:where([class~=not-prose],[class~=not-prose] *)),.prose-sm :where(ul):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.14286em;margin-bottom:1.14286em;padding-inline-start:1.57143em}.prose-sm :where(li):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:.285714em;margin-bottom:.285714em}.prose-sm :where(ol>li):not(:where([class~=not-prose],[class~=not-prose] *)),.prose-sm :where(ul>li):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-start:.428571em}.prose-sm :where(.prose-sm>ul>li p):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:.571429em;margin-bottom:.571429em}.prose-sm :where(.prose-sm>ul>li>p:first-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.14286em}.prose-sm :where(.prose-sm>ul>li>p:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-bottom:1.14286em}.prose-sm :where(.prose-sm>ol>li>p:first-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.14286em}.prose-sm :where(.prose-sm>ol>li>p:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-bottom:1.14286em}.prose-sm :where(ul ul,ul ol,ol ul,ol ol):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:.571429em;margin-bottom:.571429em}.prose-sm :where(dl):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.14286em;margin-bottom:1.14286em}.prose-sm :where(dt):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.14286em}.prose-sm :where(dd):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:.285714em;padding-inline-start:1.57143em}.prose-sm :where(hr):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:2.85714em;margin-bottom:2.85714em}.prose-sm :where(hr+*):not(:where([class~=not-prose],[class~=not-prose] *)),.prose-sm :where(h2+*):not(:where([class~=not-prose],[class~=not-prose] *)),.prose-sm :where(h3+*):not(:where([class~=not-prose],[class~=not-prose] *)),.prose-sm :where(h4+*):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0}.prose-sm :where(table):not(:where([class~=not-prose],[class~=not-prose] *)){font-size:.857143em;line-height:1.5}.prose-sm :where(thead th):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-end:1em;padding-bottom:.666667em;padding-inline-start:1em}.prose-sm :where(thead th:first-child):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-start:0}.prose-sm :where(thead th:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-end:0}.prose-sm :where(tbody td,tfoot td):not(:where([class~=not-prose],[class~=not-prose] *)){padding-top:.666667em;padding-inline-end:1em;padding-bottom:.666667em;padding-inline-start:1em}.prose-sm :where(tbody td:first-child,tfoot td:first-child):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-start:0}.prose-sm :where(tbody td:last-child,tfoot td:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-end:0}.prose-sm :where(figure):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.71429em;margin-bottom:1.71429em}.prose-sm :where(figure>*):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0;margin-bottom:0}.prose-sm :where(figcaption):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:.666667em;font-size:.857143em;line-height:1.33333}.prose-sm :where(.prose-sm>:first-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0}.prose-sm :where(.prose-sm>:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-bottom:0}.-mt-0{margin-top:calc(var(--spacing)*0)}.-mt-1{margin-top:calc(var(--spacing)*-1)}.-mt-2{margin-top:calc(var(--spacing)*-2)}.-mt-3{margin-top:calc(var(--spacing)*-3)}.-mt-4{margin-top:calc(var(--spacing)*-4)}.-mt-5{margin-top:calc(var(--spacing)*-5)}.-mt-6{margin-top:calc(var(--spacing)*-6)}.mt-0{margin-top:calc(var(--spacing)*0)}.mt-0\.5{margin-top:calc(var(--spacing)*.5)}.mt-1{margin-top:calc(var(--spacing)*1)}.mt-2{margin-top:calc(var(--spacing)*2)}.mt-3{margin-top:calc(var(--spacing)*3)}.mt-4{margin-top:calc(var(--spacing)*4)}.mt-5{margin-top:calc(var(--spacing)*5)}.mt-6{margin-top:calc(var(--spacing)*6)}.mt-8{margin-top:calc(var(--spacing)*8)}.mt-9{margin-top:calc(var(--spacing)*9)}.mt-12{margin-top:calc(var(--spacing)*12)}.mt-auto{margin-top:auto}.-mr-0{margin-right:calc(var(--spacing)*0)}.-mr-1{margin-right:calc(var(--spacing)*-1)}.-mr-2{margin-right:calc(var(--spacing)*-2)}.-mr-3{margin-right:calc(var(--spacing)*-3)}.-mr-4{margin-right:calc(var(--spacing)*-4)}.-mr-5{margin-right:calc(var(--spacing)*-5)}.-mr-6{margin-right:calc(var(--spacing)*-6)}.-mr-px{margin-right:-1px}.mr-0{margin-right:calc(var(--spacing)*0)}.mr-1{margin-right:calc(var(--spacing)*1)}.mr-2{margin-right:calc(var(--spacing)*2)}.mr-3{margin-right:calc(var(--spacing)*3)}.mr-4{margin-right:calc(var(--spacing)*4)}.mr-5{margin-right:calc(var(--spacing)*5)}.mr-6{margin-right:calc(var(--spacing)*6)}.mr-\[12px\]{margin-right:12px}.mr-auto{margin-right:auto}.-mb-0{margin-bottom:calc(var(--spacing)*0)}.-mb-1{margin-bottom:calc(var(--spacing)*-1)}.-mb-2{margin-bottom:calc(var(--spacing)*-2)}.-mb-3{margin-bottom:calc(var(--spacing)*-3)}.-mb-4{margin-bottom:calc(var(--spacing)*-4)}.-mb-5{margin-bottom:calc(var(--spacing)*-5)}.-mb-6{margin-bottom:calc(var(--spacing)*-6)}.-mb-px{margin-bottom:-1px}.mb-0{margin-bottom:calc(var(--spacing)*0)}.mb-1{margin-bottom:calc(var(--spacing)*1)}.mb-2{margin-bottom:calc(var(--spacing)*2)}.mb-2\.5{margin-bottom:calc(var(--spacing)*2.5)}.mb-3{margin-bottom:calc(var(--spacing)*3)}.mb-4{margin-bottom:calc(var(--spacing)*4)}.mb-5{margin-bottom:calc(var(--spacing)*5)}.mb-6{margin-bottom:calc(var(--spacing)*6)}.mb-8{margin-bottom:calc(var(--spacing)*8)}.mb-10{margin-bottom:calc(var(--spacing)*10)}.mb-12{margin-bottom:calc(var(--spacing)*12)}.-ml-0{margin-left:calc(var(--spacing)*0)}.-ml-1{margin-left:calc(var(--spacing)*-1)}.-ml-2{margin-left:calc(var(--spacing)*-2)}.-ml-3{margin-left:calc(var(--spacing)*-3)}.-ml-4{margin-left:calc(var(--spacing)*-4)}.-ml-5{margin-left:calc(var(--spacing)*-5)}.-ml-6{margin-left:calc(var(--spacing)*-6)}.ml-0{margin-left:calc(var(--spacing)*0)}.ml-1{margin-left:calc(var(--spacing)*1)}.ml-2{margin-left:calc(var(--spacing)*2)}.ml-3{margin-left:calc(var(--spacing)*3)}.ml-4{margin-left:calc(var(--spacing)*4)}.ml-5{margin-left:calc(var(--spacing)*5)}.ml-6{margin-left:calc(var(--spacing)*6)}.ml-auto{margin-left:auto}.material-symbols-outlined{letter-spacing:normal;text-transform:none;white-space:nowrap;word-wrap:normal;-moz-font-feature-settings:"liga";-moz-osx-font-smoothing:grayscale;direction:ltr;font-family:Material Symbols Outlined;font-size:18px;font-style:normal;font-weight:400;line-height:1;display:inline-block}.block{display:block}.block\!{display:block!important}.contents{display:contents}.flex{display:flex}.grid{display:grid}.hidden{display:none}.hidden\!{display:none!important}.inline{display:inline}.inline-block{display:inline-block}.table{display:table}.field-sizing-content{field-sizing:content;field-sizing:content}.size-full{width:100%;height:100%}.h-0{height:calc(var(--spacing)*0)}.h-1{height:calc(var(--spacing)*1)}.h-1\.5{height:calc(var(--spacing)*1.5)}.h-3{height:calc(var(--spacing)*3)}.h-4{height:calc(var(--spacing)*4)}.h-5{height:calc(var(--spacing)*5)}.h-7{height:calc(var(--spacing)*7)}.h-8{height:calc(var(--spacing)*8)}.h-9{height:calc(var(--spacing)*9)}.h-12{height:calc(var(--spacing)*12)}.h-16{height:calc(var(--spacing)*16)}.h-24{height:calc(var(--spacing)*24)}.h-72{height:calc(var(--spacing)*72)}.h-\[18px\]{height:18px}.h-\[38px\]{height:38px}.h-\[54px\]{height:54px}.h-\[55px\]{height:55px}.h-\[64px\]{height:64px}.h-\[65px\]{height:65px}.h-\[66px\]{height:66px}.h-full{height:100%}.h-px{height:1px}.max-h-\[30px\]{max-height:30px}.max-h-screen{max-height:100vh}.min-h-13{min-height:calc(var(--spacing)*13)}.min-h-\[38px\]{min-height:38px}.min-h-\[62\.5px\]{min-height:62.5px}.min-h-screen{min-height:100vh}.w-0{width:calc(var(--spacing)*0)}.w-0\.5{width:calc(var(--spacing)*.5)}.w-1{width:calc(var(--spacing)*1)}.w-1\/2{width:50%}.w-1\/3{width:33.3333%}.w-1\/4{width:25%}.w-1\/5{width:20%}.w-1\/6{width:16.6667%}.w-1\/7{width:14.2857%}.w-2\/3{width:66.6667%}.w-2\/4{width:50%}.w-2\/5{width:40%}.w-2\/6{width:33.3333%}.w-2\/7{width:28.5714%}.w-3{width:calc(var(--spacing)*3)}.w-3\/4{width:75%}.w-3\/5{width:60%}.w-3\/6{width:50%}.w-3\/7{width:42.8571%}.w-4{width:calc(var(--spacing)*4)}.w-4\/5{width:80%}.w-4\/6{width:66.6667%}.w-4\/7{width:57.1429%}.w-5\/6{width:83.3333%}.w-5\/7{width:71.4286%}.w-6\/7{width:85.7143%}.w-7{width:calc(var(--spacing)*7)}.w-8{width:calc(var(--spacing)*8)}.w-9{width:calc(var(--spacing)*9)}.w-16{width:calc(var(--spacing)*16)}.w-24{width:calc(var(--spacing)*24)}.w-32{width:calc(var(--spacing)*32)}.w-40{width:calc(var(--spacing)*40)}.w-48{width:calc(var(--spacing)*48)}.w-52{width:calc(var(--spacing)*52)}.w-80{width:calc(var(--spacing)*80)}.w-\[0px\]{width:0}.w-\[18px\]{width:18px}.w-\[38px\]{width:38px}.w-\[65px\]\!{width:65px!important}.w-\[264px\]{width:264px}.w-\[288px\]{width:288px}.w-full{width:100%}.w-px{width:1px}.w-screen{width:100vw}.max-w-2xl{max-width:var(--container-2xl)}.max-w-3xl{max-width:var(--container-3xl)}.max-w-4xl{max-width:var(--container-4xl)}.max-w-8{max-width:calc(var(--spacing)*8)}.max-w-48{max-width:calc(var(--spacing)*48)}.max-w-full{max-width:100%}.max-w-none{max-width:none}.min-w-0{min-width:calc(var(--spacing)*0)}.min-w-4{min-width:calc(var(--spacing)*4)}.min-w-8{min-width:calc(var(--spacing)*8)}.min-w-20{min-width:calc(var(--spacing)*20)}.min-w-32{min-width:calc(var(--spacing)*32)}.min-w-52{min-width:calc(var(--spacing)*52)}.min-w-56{min-width:calc(var(--spacing)*56)}.min-w-72{min-width:calc(var(--spacing)*72)}.flex-none{flex:none}.shrink-0{flex-shrink:0}.grow{flex-grow:1}.basis-1\/2{flex-basis:50%}.basis-1\/3{flex-basis:33.3333%}.table-fixed{table-layout:fixed}.border-collapse{border-collapse:collapse}.border-separate{border-collapse:separate}.border-spacing-none{border-spacing:0}.-translate-x-1\/3{--tw-translate-x:calc(calc(1/3*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-x-1\/4{--tw-translate-x:calc(1/4*100%);translate:var(--tw-translate-x)var(--tw-translate-y)}.-translate-y-1\/2{--tw-translate-y:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.-translate-y-1\/4{--tw-translate-y:calc(calc(1/4*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-y-full{--tw-translate-y:100%;translate:var(--tw-translate-x)var(--tw-translate-y)}.-rotate-90{rotate:-90deg}.-rotate-90\!{rotate:-90deg!important}.rotate-0{rotate:none}.rotate-90{rotate:90deg}.rotate-180{rotate:180deg}.transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.transform\!{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)!important}.cursor-help{cursor:help}.cursor-not-allowed{cursor:not-allowed}.cursor-pointer{cursor:pointer}.resize{resize:both}.appearance-none{appearance:none}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.grid-cols-5{grid-template-columns:repeat(5,minmax(0,1fr))}.grid-cols-6{grid-template-columns:repeat(6,minmax(0,1fr))}.grid-cols-7{grid-template-columns:repeat(7,minmax(0,1fr))}.grid-cols-8{grid-template-columns:repeat(8,minmax(0,1fr))}.grid-cols-9{grid-template-columns:repeat(9,minmax(0,1fr))}.grid-cols-10{grid-template-columns:repeat(10,minmax(0,1fr))}.grid-cols-11{grid-template-columns:repeat(11,minmax(0,1fr))}.grid-cols-12{grid-template-columns:repeat(12,minmax(0,1fr))}.flex-col{flex-direction:column}.flex-col-reverse{flex-direction:column-reverse}.flex-row{flex-direction:row}.flex-wrap{flex-wrap:wrap}.items-center{align-items:center}.items-start{align-items:flex-start}.justify-between{justify-content:space-between}.justify-center{justify-content:center}.justify-end{justify-content:flex-end}.justify-start{justify-content:flex-start}.gap-0\.5{gap:calc(var(--spacing)*.5)}.gap-1{gap:calc(var(--spacing)*1)}.gap-1\.5{gap:calc(var(--spacing)*1.5)}.gap-2{gap:calc(var(--spacing)*2)}.gap-3{gap:calc(var(--spacing)*3)}.gap-4{gap:calc(var(--spacing)*4)}.gap-5{gap:calc(var(--spacing)*5)}.gap-6{gap:calc(var(--spacing)*6)}.gap-7{gap:calc(var(--spacing)*7)}.gap-8{gap:calc(var(--spacing)*8)}.gap-9{gap:calc(var(--spacing)*9)}.gap-10{gap:calc(var(--spacing)*10)}.gap-11{gap:calc(var(--spacing)*11)}.gap-12{gap:calc(var(--spacing)*12)}:where(.space-y-2>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*2)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-y-reverse)))}.self-center{align-self:center}.self-end{align-self:flex-end}.self-stretch{align-self:stretch}.truncate{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.overflow-auto{overflow:auto}.overflow-hidden{overflow:hidden}.overflow-x-auto{overflow-x:auto}.overflow-x-hidden{overflow-x:hidden}.overflow-y-auto{overflow-y:auto}.rounded{border-radius:.25rem}.rounded-\[4px\]{border-radius:4px}.rounded-default{border-radius:var(--border-radius,6px)}.rounded-full{border-radius:3.40282e38px}.rounded-xs{border-radius:var(--radius-xs)}.rounded-t{border-top-left-radius:.25rem;border-top-right-radius:.25rem}.rounded-t-default{border-top-left-radius:var(--border-radius,6px);border-top-right-radius:var(--border-radius,6px)}.rounded-t-none{border-top-left-radius:0;border-top-right-radius:0}.rounded-l{border-top-left-radius:.25rem;border-bottom-left-radius:.25rem}.rounded-l-default{border-top-left-radius:var(--border-radius,6px);border-bottom-left-radius:var(--border-radius,6px)}.rounded-r{border-top-right-radius:.25rem;border-bottom-right-radius:.25rem}.rounded-r-default{border-top-right-radius:var(--border-radius,6px);border-bottom-right-radius:var(--border-radius,6px)}.rounded-b{border-bottom-right-radius:.25rem;border-bottom-left-radius:.25rem}.rounded-b-default{border-bottom-right-radius:var(--border-radius,6px);border-bottom-left-radius:var(--border-radius,6px)}.rounded-b-none{border-bottom-right-radius:0;border-bottom-left-radius:0}.border{border-style:var(--tw-border-style);border-width:1px}.border\!{border-style:var(--tw-border-style)!important;border-width:1px!important}.border-0{border-style:var(--tw-border-style);border-width:0}.border-0\!{border-style:var(--tw-border-style)!important;border-width:0!important}.border-2{border-style:var(--tw-border-style);border-width:2px}.border-t{border-top-style:var(--tw-border-style);border-top-width:1px}.border-t-0{border-top-style:var(--tw-border-style);border-top-width:0}.border-t-0\!{border-top-style:var(--tw-border-style)!important;border-top-width:0!important}.border-t-2{border-top-style:var(--tw-border-style);border-top-width:2px}.border-r{border-right-style:var(--tw-border-style);border-right-width:1px}.border-r-0{border-right-style:var(--tw-border-style);border-right-width:0}.border-r-2{border-right-style:var(--tw-border-style);border-right-width:2px}.border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.border-b-0{border-bottom-style:var(--tw-border-style);border-bottom-width:0}.border-b-2{border-bottom-style:var(--tw-border-style);border-bottom-width:2px}.border-l{border-left-style:var(--tw-border-style);border-left-width:1px}.border-l-0{border-left-style:var(--tw-border-style);border-left-width:0}.border-l-2{border-left-style:var(--tw-border-style);border-left-width:2px}.border-dashed{--tw-border-style:dashed;border-style:dashed}.border-base-50{border-color:rgb(var(--color-base-50))}.border-base-100{border-color:rgb(var(--color-base-100))}.border-base-200{border-color:rgb(var(--color-base-200))}.border-base-200\!{border-color:rgb(var(--color-base-200))!important}.border-base-300{border-color:rgb(var(--color-base-300))}.border-base-400{border-color:rgb(var(--color-base-400))}.border-base-400\/10{border-color:color-mix(in srgb,rgb(rgb(var(--color-base-400)))10%,transparent)}@supports (color:color-mix(in lab, red, red)){.border-base-400\/10{border-color:color-mix(in oklab,rgb(var(--color-base-400))10%,transparent)}}.border-base-500{border-color:rgb(var(--color-base-500))}.border-base-600{border-color:rgb(var(--color-base-600))}.border-base-700{border-color:rgb(var(--color-base-700))}.border-base-800{border-color:rgb(var(--color-base-800))}.border-base-900{border-color:rgb(var(--color-base-900))}.border-base-950{border-color:rgb(var(--color-base-950))}.border-blue-200{border-color:var(--color-blue-200)}.border-blue-700{border-color:var(--color-blue-700)}.border-green-700{border-color:var(--color-green-700)}.border-orange-700{border-color:var(--color-orange-700)}.border-primary-600{border-color:rgb(var(--color-primary-600))}.border-primary-700{border-color:rgb(var(--color-primary-700))}.border-red-200{border-color:var(--color-red-200)}.border-red-700{border-color:var(--color-red-700)}.border-transparent{border-color:#0000}.border-white\/40{border-color:#fff6}@supports (color:color-mix(in lab, red, red)){.border-white\/40{border-color:color-mix(in oklab,var(--color-white)40%,transparent)}}.\!bg-white\/20{background-color:#fff3!important}@supports (color:color-mix(in lab, red, red)){.\!bg-white\/20{background-color:color-mix(in oklab,var(--color-white)20%,transparent)!important}}.bg-amber-100{background-color:var(--color-amber-100)}.bg-base-50{background-color:rgb(var(--color-base-50))}.bg-base-100{background-color:rgb(var(--color-base-100))}.bg-base-200{background-color:rgb(var(--color-base-200))}.bg-base-200\/10{background-color:color-mix(in srgb,rgb(rgb(var(--color-base-200)))10%,transparent)}@supports (color:color-mix(in lab, red, red)){.bg-base-200\/10{background-color:color-mix(in oklab,rgb(var(--color-base-200))10%,transparent)}}.bg-base-300{background-color:rgb(var(--color-base-300))}.bg-base-400{background-color:rgb(var(--color-base-400))}.bg-base-500{background-color:rgb(var(--color-base-500))}.bg-base-600{background-color:rgb(var(--color-base-600))}.bg-base-700{background-color:rgb(var(--color-base-700))}.bg-base-800{background-color:rgb(var(--color-base-800))}.bg-base-900{background-color:rgb(var(--color-base-900))}.bg-base-900\/80{background-color:color-mix(in srgb,rgb(rgb(var(--color-base-900)))80%,transparent)}@supports (color:color-mix(in lab, red, red)){.bg-base-900\/80{background-color:color-mix(in oklab,rgb(var(--color-base-900))80%,transparent)}}.bg-base-950{background-color:rgb(var(--color-base-950))}.bg-blue-50{background-color:var(--color-blue-50)}.bg-blue-100{background-color:var(--color-blue-100)}.bg-blue-600{background-color:var(--color-blue-600)}.bg-gray-50{background-color:var(--color-gray-50)}.bg-gray-100{background-color:var(--color-gray-100)}.bg-green-100{background-color:var(--color-green-100)}.bg-green-500{background-color:var(--color-green-500)}.bg-green-600{background-color:var(--color-green-600)}.bg-orange-100{background-color:var(--color-orange-100)}.bg-orange-600{background-color:var(--color-orange-600)}.bg-primary-50{background-color:rgb(var(--color-primary-50))}.bg-primary-100{background-color:rgb(var(--color-primary-100))}.bg-primary-200{background-color:rgb(var(--color-primary-200))}.bg-primary-300{background-color:rgb(var(--color-primary-300))}.bg-primary-400{background-color:rgb(var(--color-primary-400))}.bg-primary-500{background-color:rgb(var(--color-primary-500))}.bg-primary-600{background-color:rgb(var(--color-primary-600))}.bg-primary-700{background-color:rgb(var(--color-primary-700))}.bg-primary-800{background-color:rgb(var(--color-primary-800))}.bg-primary-900{background-color:rgb(var(--color-primary-900))}.bg-primary-950{background-color:rgb(var(--color-primary-950))}.bg-red-50{background-color:var(--color-red-50)}.bg-red-100{background-color:var(--color-red-100)}.bg-red-500{background-color:var(--color-red-500)}.bg-red-600{background-color:var(--color-red-600)}.bg-transparent{background-color:#0000}.bg-white{background-color:var(--color-white)}.bg-white\!{background-color:var(--color-white)!important}.bg-white\/20{background-color:#fff3}@supports (color:color-mix(in lab, red, red)){.bg-white\/20{background-color:color-mix(in oklab,var(--color-white)20%,transparent)}}.bg-white\/80{background-color:#fffc}@supports (color:color-mix(in lab, red, red)){.bg-white\/80{background-color:color-mix(in oklab,var(--color-white)80%,transparent)}}.bg-white\/\[\.06\]{background-color:#ffffff0f}@supports (color:color-mix(in lab, red, red)){.bg-white\/\[\.06\]{background-color:color-mix(in oklab,var(--color-white)6%,transparent)}}.bg-none{background-image:none}.bg-contain{background-size:contain}.bg-cover{background-size:cover}.bg-center{background-position:50%}.bg-no-repeat{background-repeat:no-repeat}.object-cover{object-fit:cover}.p-0{padding:calc(var(--spacing)*0)}.p-0\!{padding:calc(var(--spacing)*0)!important}.p-1{padding:calc(var(--spacing)*1)}.p-2{padding:calc(var(--spacing)*2)}.p-3{padding:calc(var(--spacing)*3)}.p-4{padding:calc(var(--spacing)*4)}.p-5{padding:calc(var(--spacing)*5)}.p-6{padding:calc(var(--spacing)*6)}.px-0{padding-inline:calc(var(--spacing)*0)}.px-1{padding-inline:calc(var(--spacing)*1)}.px-1\.5{padding-inline:calc(var(--spacing)*1.5)}.px-2{padding-inline:calc(var(--spacing)*2)}.px-2\.5{padding-inline:calc(var(--spacing)*2.5)}.px-3{padding-inline:calc(var(--spacing)*3)}.px-4{padding-inline:calc(var(--spacing)*4)}.px-5{padding-inline:calc(var(--spacing)*5)}.px-6{padding-inline:calc(var(--spacing)*6)}.px-8{padding-inline:calc(var(--spacing)*8)}.px-12{padding-inline:calc(var(--spacing)*12)}.px-px{padding-inline:1px}.py-0{padding-block:calc(var(--spacing)*0)}.py-0\!{padding-block:calc(var(--spacing)*0)!important}.py-1{padding-block:calc(var(--spacing)*1)}.py-1\.5{padding-block:calc(var(--spacing)*1.5)}.py-2{padding-block:calc(var(--spacing)*2)}.py-2\.5{padding-block:calc(var(--spacing)*2.5)}.py-3{padding-block:calc(var(--spacing)*3)}.py-4{padding-block:calc(var(--spacing)*4)}.py-4\.5{padding-block:calc(var(--spacing)*4.5)}.py-5{padding-block:calc(var(--spacing)*5)}.py-6{padding-block:calc(var(--spacing)*6)}.py-24{padding-block:calc(var(--spacing)*24)}.pt-0{padding-top:calc(var(--spacing)*0)}.pt-1{padding-top:calc(var(--spacing)*1)}.pt-2{padding-top:calc(var(--spacing)*2)}.pt-2\.5{padding-top:calc(var(--spacing)*2.5)}.pt-3{padding-top:calc(var(--spacing)*3)}.pt-4{padding-top:calc(var(--spacing)*4)}.pt-5{padding-top:calc(var(--spacing)*5)}.pt-6{padding-top:calc(var(--spacing)*6)}.pr-0{padding-right:calc(var(--spacing)*0)}.pr-1{padding-right:calc(var(--spacing)*1)}.pr-2{padding-right:calc(var(--spacing)*2)}.pr-3{padding-right:calc(var(--spacing)*3)}.pr-4{padding-right:calc(var(--spacing)*4)}.pr-5{padding-right:calc(var(--spacing)*5)}.pr-6{padding-right:calc(var(--spacing)*6)}.pr-8{padding-right:calc(var(--spacing)*8)}.pr-8\!{padding-right:calc(var(--spacing)*8)!important}.pr-9{padding-right:calc(var(--spacing)*9)}.pb-0{padding-bottom:calc(var(--spacing)*0)}.pb-1{padding-bottom:calc(var(--spacing)*1)}.pb-2{padding-bottom:calc(var(--spacing)*2)}.pb-2\.5{padding-bottom:calc(var(--spacing)*2.5)}.pb-3{padding-bottom:calc(var(--spacing)*3)}.pb-4{padding-bottom:calc(var(--spacing)*4)}.pb-5{padding-bottom:calc(var(--spacing)*5)}.pb-6{padding-bottom:calc(var(--spacing)*6)}.pl-0{padding-left:calc(var(--spacing)*0)}.pl-1{padding-left:calc(var(--spacing)*1)}.pl-2{padding-left:calc(var(--spacing)*2)}.pl-3{padding-left:calc(var(--spacing)*3)}.pl-4{padding-left:calc(var(--spacing)*4)}.pl-5{padding-left:calc(var(--spacing)*5)}.pl-6{padding-left:calc(var(--spacing)*6)}.pl-9{padding-left:calc(var(--spacing)*9)}.text-center{text-align:center}.text-left{text-align:left}.text-right{text-align:right}.align-middle{vertical-align:middle}.align-text-top{vertical-align:text-top}.align-top{vertical-align:top}.font-mono{font-family:var(--font-mono)}.font-sans{font-family:var(--font-sans)}.text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.text-5xl\!{font-size:var(--text-5xl)!important;line-height:var(--tw-leading,var(--text-5xl--line-height))!important}.text-6xl\!{font-size:var(--text-6xl)!important;line-height:var(--tw-leading,var(--text-6xl--line-height))!important}.text-base{font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height))}.text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}.text-xs{font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height))}.md-16{font-size:16px}.md-18{font-size:18px}.text-\[11px\]{font-size:11px}.text-\[15px\]{font-size:15px}.leading-7\!{--tw-leading:calc(var(--spacing)*7)!important;line-height:calc(var(--spacing)*7)!important}.leading-none{--tw-leading:1;line-height:1}.leading-normal{--tw-leading:var(--leading-normal);line-height:var(--leading-normal)}.leading-relaxed{--tw-leading:var(--leading-relaxed);line-height:var(--leading-relaxed)}.font-medium{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.font-normal{--tw-font-weight:var(--font-weight-normal);font-weight:var(--font-weight-normal)}.font-semibold{--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.tracking-tight{--tw-tracking:var(--tracking-tight);letter-spacing:var(--tracking-tight)}.break-words{overflow-wrap:break-word}.text-ellipsis{text-overflow:ellipsis}.whitespace-normal{white-space:normal}.whitespace-nowrap{white-space:nowrap}.\!text-white{color:var(--color-white)!important}.text-amber-600{color:var(--color-amber-600)}.text-base-50{color:rgb(var(--color-base-50))}.text-base-100{color:rgb(var(--color-base-100))}.text-base-200{color:rgb(var(--color-base-200))}.text-base-300{color:rgb(var(--color-base-300))}.text-base-400{color:rgb(var(--color-base-400))}.text-base-500{color:rgb(var(--color-base-500))}.text-base-600{color:rgb(var(--color-base-600))}.text-base-700{color:rgb(var(--color-base-700))}.text-base-800{color:rgb(var(--color-base-800))}.text-base-900{color:rgb(var(--color-base-900))}.text-base-950{color:rgb(var(--color-base-950))}.text-blue-500{color:var(--color-blue-500)}.text-blue-700{color:var(--color-blue-700)}.text-font-default-light{color:rgb(var(--color-font-default-light))}.text-font-important-light{color:rgb(var(--color-font-important-light))}.text-font-important-light\!{color:rgb(var(--color-font-important-light))!important}.text-font-subtle-light{color:rgb(var(--color-font-subtle-light))}.text-green-700{color:var(--color-green-700)}.text-orange-700{color:var(--color-orange-700)}.text-primary-600{color:rgb(var(--color-primary-600))}.text-primary-700{color:rgb(var(--color-primary-700))}.text-red-500{color:var(--color-red-500)}.text-red-600{color:var(--color-red-600)}.text-red-700{color:var(--color-red-700)}.text-white{color:var(--color-white)}.capitalize{text-transform:capitalize}.uppercase{text-transform:uppercase}.italic{font-style:italic}.underline{text-decoration-line:underline}.antialiased{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.placeholder-base-400::placeholder{color:rgb(var(--color-base-400))}.placeholder-font-subtle-light::placeholder{color:rgb(var(--color-font-subtle-light))}.opacity-0{opacity:0}.opacity-50{opacity:.5}.shadow-lg{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-sm{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-xs{--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.outline{outline-style:var(--tw-outline-style);outline-width:1px}.\!outline-primary-400{outline-color:rgb(var(--color-primary-400))!important}.outline-gray-500\/20{outline-color:#6a728233}@supports (color:color-mix(in lab, red, red)){.outline-gray-500\/20{outline-color:color-mix(in oklab,var(--color-gray-500)20%,transparent)}}.outline-green-200{outline-color:var(--color-green-200)}.outline-red-200{outline-color:var(--color-red-200)}.blur{--tw-blur:blur(8px);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.filter{filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.filter\!{filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}.backdrop-blur-xs{--tw-backdrop-blur:blur(var(--blur-xs));-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,);backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)}.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-all{transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-colors{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-transform{transition-property:transform,translate,scale,rotate;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.duration-75{--tw-duration:75ms;transition-duration:75ms}.ease-in-out{--tw-ease:var(--ease-in-out);transition-timing-function:var(--ease-in-out)}.select-none{-webkit-user-select:none;user-select:none}:is(.\*\:mb-0>*){margin-bottom:calc(var(--spacing)*0)}:is(.\*\:mb-0\!>*){margin-bottom:calc(var(--spacing)*0)!important}:is(.\*\:truncate>*){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:is(.\*\:rounded-default>*){border-radius:var(--border-radius,6px)}:is(.\*\:px-3>*){padding-inline:calc(var(--spacing)*3)}:is(.\*\:py-2>*){padding-block:calc(var(--spacing)*2)}:is(.\*\:text-left>*){text-align:left}:is(.\*\:align-middle>*){vertical-align:middle}:is(.\*\:font-semibold>*){--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}:is(.\*\:text-base-700>*){color:rgb(var(--color-base-700))}:is(.\*\:text-font-important-light>*){color:rgb(var(--color-font-important-light))}@media (hover:hover){.group-hover\:-right-1:is(:where(.group):hover *){right:calc(var(--spacing)*-1)}.group-hover\:-left-1:is(:where(.group):hover *){left:calc(var(--spacing)*-1)}.group-hover\:text-primary-600:is(:where(.group):hover *){color:rgb(var(--color-primary-600))}.group-hover\/action\:bg-base-100:is(:where(.group\/action):hover *){background-color:rgb(var(--color-base-100))}.group-hover\/action\:text-base-700:is(:where(.group\/action):hover *){color:rgb(var(--color-base-700))}}.group-has-\[input\.action-select\:checked\]\:flex:is(:where(.group):has(:is(input.action-select:checked)) *){display:flex}.group-\[\.errors\]\:border-red-600:is(:where(.group).errors *){border-color:var(--color-red-600)}.group-\[\.errors\]\:border-x-red-600:is(:where(.group).errors *){border-inline-color:var(--color-red-600)}.group-\[\.errors\]\:border-t-red-600:is(:where(.group).errors *){border-top-color:var(--color-red-600)}.group-\[\.field-row\]\:grow:is(:where(.group).field-row *){flex-grow:1}.group-\[\.field-row\]\:gap-2:is(:where(.group).field-row *){gap:calc(var(--spacing)*2)}.group-\[\.field-tabular\]\:grow:is(:where(.group).field-tabular *){flex-grow:1}.group-\[\.field-tabular\]\:gap-2:is(:where(.group).field-tabular *){gap:calc(var(--spacing)*2)}.group-\[\.fieldset\]\:-mx-3:is(:where(.group).fieldset *){margin-inline:calc(var(--spacing)*-3)}.group-\[\.fieldset\]\:flex:is(:where(.group).fieldset *){display:flex}.group-\[\.fieldset\]\:flex-row:is(:where(.group).fieldset *){flex-direction:row}.group-\[\.fieldset\]\:justify-end:is(:where(.group).fieldset *){justify-content:flex-end}.group-\[\.fieldset\]\:border-t:is(:where(.group).fieldset *){border-top-style:var(--tw-border-style);border-top-width:1px}.group-\[\.fieldset\]\:border-base-200:is(:where(.group).fieldset *){border-color:rgb(var(--color-base-200))}.group-\[\.fieldset\]\:px-3:is(:where(.group).fieldset *){padding-inline:calc(var(--spacing)*3)}.group-\[\.fieldset\]\:pt-3:is(:where(.group).fieldset *){padding-top:calc(var(--spacing)*3)}.group-\[\.inline-stacked\]\:mx-3:is(:where(.group).inline-stacked *){margin-inline:calc(var(--spacing)*3)}.group-\[\.inline-stacked\]\:mt-3:is(:where(.group).inline-stacked *){margin-top:calc(var(--spacing)*3)}.group-\[\.inline-stacked\]\:mb-3:is(:where(.group).inline-stacked *){margin-bottom:calc(var(--spacing)*3)}.group-\[\.inline-tabular\]\:mx-3:is(:where(.group).inline-tabular *){margin-inline:calc(var(--spacing)*3)}.group-\[\.inline-tabular\]\:mt-3:is(:where(.group).inline-tabular *){margin-top:calc(var(--spacing)*3)}.group-\[\.inline-tabular\]\:mb-0:is(:where(.group).inline-tabular *){margin-bottom:calc(var(--spacing)*0)}.group-\[\.last\]\/row\:border-b-0:is(:where(.group\/row).last *){border-bottom-style:var(--tw-border-style);border-bottom-width:0}.group-\[\.primary\]\:border-transparent:is(:where(.group).primary *){border-color:#0000}.group-\[\.primary\]\:text-white:is(:where(.group).primary *){color:var(--color-white)}.before\:mr-auto:before{content:var(--tw-content);margin-right:auto}.before\:block:before{content:var(--tw-content);display:block}.before\:flex:before{content:var(--tw-content);display:flex}.before\:items-center:before{content:var(--tw-content);align-items:center}.before\:pr-4:before{content:var(--tw-content);padding-right:calc(var(--spacing)*4)}.before\:font-semibold:before{content:var(--tw-content);--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.before\:text-base-500:before{content:var(--tw-content);color:rgb(var(--color-base-500))}.before\:text-font-important-light:before{content:var(--tw-content);color:rgb(var(--color-font-important-light))}.before\:capitalize:before{content:var(--tw-content);text-transform:capitalize}.before\:content-\[attr\(data-label\)\]:before{content:var(--tw-content);--tw-content:attr(data-label);content:var(--tw-content)}.after\:absolute:after{content:var(--tw-content);position:absolute}.after\:top-1:after{content:var(--tw-content);top:calc(var(--spacing)*1)}.after\:top-1\/2:after{content:var(--tw-content);top:50%}.after\:left-1:after{content:var(--tw-content);left:calc(var(--spacing)*1)}.after\:left-1\/2:after{content:var(--tw-content);left:50%}.after\:-mt-px:after{content:var(--tw-content);margin-top:-1px}.after\:-ml-px:after{content:var(--tw-content);margin-left:-1px}.after\:material-symbols-outlined:after{content:var(--tw-content);letter-spacing:normal;text-transform:none;white-space:nowrap;word-wrap:normal;-moz-font-feature-settings:"liga";-moz-osx-font-smoothing:grayscale;direction:ltr;font-family:Material Symbols Outlined;font-size:18px;font-style:normal;font-weight:400;line-height:1;display:inline-block}.after\:flex:after{content:var(--tw-content);display:flex}.after\:flex\!:after{content:var(--tw-content);display:flex!important}.after\:h-2:after{content:var(--tw-content);height:calc(var(--spacing)*2)}.after\:h-3:after{content:var(--tw-content);height:calc(var(--spacing)*3)}.after\:h-4:after{content:var(--tw-content);height:calc(var(--spacing)*4)}.after\:w-2:after{content:var(--tw-content);width:calc(var(--spacing)*2)}.after\:w-3:after{content:var(--tw-content);width:calc(var(--spacing)*3)}.after\:w-4:after{content:var(--tw-content);width:calc(var(--spacing)*4)}.after\:-translate-x-1\/2:after{content:var(--tw-content);--tw-translate-x:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.after\:-translate-y-1\/2:after{content:var(--tw-content);--tw-translate-y:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.after\:items-center:after{content:var(--tw-content);align-items:center}.after\:justify-center:after{content:var(--tw-content);justify-content:center}.after\:rounded-full:after{content:var(--tw-content);border-radius:3.40282e38px}.after\:bg-red-300:after{content:var(--tw-content);background-color:var(--color-red-300)}.after\:bg-transparent:after{content:var(--tw-content);background-color:#0000}.after\:bg-white:after{content:var(--tw-content);background-color:var(--color-white)}.after\:text-sm:after{content:var(--tw-content);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.after\:text-sm\!:after{content:var(--tw-content);font-size:var(--text-sm)!important;line-height:var(--tw-leading,var(--text-sm--line-height))!important}.after\:leading-none:after{content:var(--tw-content);--tw-leading:1;line-height:1}.after\:text-white:after{content:var(--tw-content);color:var(--color-white)}.after\:shadow-xs:after{content:var(--tw-content);--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.after\:transition-all:after{content:var(--tw-content);transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.after\:content-\[\'\'\]:after{content:var(--tw-content);--tw-content:"";content:var(--tw-content)}.after\:content-\[\'done\'\]:after{content:var(--tw-content);--tw-content:"done";content:var(--tw-content)}.first\:-mt-5:first-child{margin-top:calc(var(--spacing)*-5)}.first\:border-t-0:first-child{border-top-style:var(--tw-border-style);border-top-width:0}.first\:pl-3:first-child{padding-left:calc(var(--spacing)*3)}.first\:pl-6:first-child{padding-left:calc(var(--spacing)*6)}.last\:-mb-3:last-child{margin-bottom:calc(var(--spacing)*-3)}.last\:mb-0:last-child{margin-bottom:calc(var(--spacing)*0)}.last\:mb-4:last-child{margin-bottom:calc(var(--spacing)*4)}.last\:mb-8:last-child{margin-bottom:calc(var(--spacing)*8)}.last\:border-r-0:last-child{border-right-style:var(--tw-border-style);border-right-width:0}.last\:border-b-0:last-child{border-bottom-style:var(--tw-border-style);border-bottom-width:0}.last\:pr-3:last-child{padding-right:calc(var(--spacing)*3)}.last\:pr-6:last-child{padding-right:calc(var(--spacing)*6)}.checked\:border-primary-600:checked{border-color:rgb(var(--color-primary-600))}.checked\:bg-green-500:checked{background-color:var(--color-green-500)}.checked\:bg-primary-600:checked{background-color:rgb(var(--color-primary-600))}.checked\:transition-all:checked{transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.checked\:after\:left-4:checked:after{content:var(--tw-content);left:calc(var(--spacing)*4)}.checked\:after\:bg-white:checked:after{content:var(--tw-content);background-color:var(--color-white)}.empty\:hidden:empty{display:none}.focus-within\:outline-2:focus-within{outline-style:var(--tw-outline-style);outline-width:2px}.focus-within\:-outline-offset-2:focus-within{outline-offset:calc(2px*-1)}.focus-within\:outline-primary-600:focus-within{outline-color:rgb(var(--color-primary-600))}.focus-within\:group-\[\.errors\]\:outline-red-500:focus-within:is(:where(.group).errors *){outline-color:var(--color-red-500)}@media (hover:hover){.hover\:border-base-50:hover{border-color:rgb(var(--color-base-50))}.hover\:border-base-100:hover{border-color:rgb(var(--color-base-100))}.hover\:border-base-200:hover{border-color:rgb(var(--color-base-200))}.hover\:border-base-300:hover{border-color:rgb(var(--color-base-300))}.hover\:border-base-400:hover{border-color:rgb(var(--color-base-400))}.hover\:border-base-500:hover{border-color:rgb(var(--color-base-500))}.hover\:border-base-600:hover{border-color:rgb(var(--color-base-600))}.hover\:border-base-700:hover{border-color:rgb(var(--color-base-700))}.hover\:border-base-800:hover{border-color:rgb(var(--color-base-800))}.hover\:border-base-900:hover{border-color:rgb(var(--color-base-900))}.hover\:border-base-950:hover{border-color:rgb(var(--color-base-950))}.hover\:border-transparent:hover{border-color:#0000}.hover\:bg-base-50:hover{background-color:rgb(var(--color-base-50))}.hover\:bg-base-100:hover{background-color:rgb(var(--color-base-100))}.hover\:bg-base-200:hover{background-color:rgb(var(--color-base-200))}.hover\:bg-base-300:hover{background-color:rgb(var(--color-base-300))}.hover\:bg-base-400:hover{background-color:rgb(var(--color-base-400))}.hover\:bg-base-500:hover{background-color:rgb(var(--color-base-500))}.hover\:bg-base-600:hover{background-color:rgb(var(--color-base-600))}.hover\:bg-base-700:hover{background-color:rgb(var(--color-base-700))}.hover\:bg-base-700\/\[\.04\]:hover{background-color:color-mix(in srgb,rgb(rgb(var(--color-base-700)))4%,transparent)}@supports (color:color-mix(in lab, red, red)){.hover\:bg-base-700\/\[\.04\]:hover{background-color:color-mix(in oklab,rgb(var(--color-base-700))4%,transparent)}}.hover\:bg-base-800:hover{background-color:rgb(var(--color-base-800))}.hover\:bg-base-900:hover{background-color:rgb(var(--color-base-900))}.hover\:bg-base-950:hover{background-color:rgb(var(--color-base-950))}.hover\:bg-primary-50:hover{background-color:rgb(var(--color-primary-50))}.hover\:bg-primary-100:hover{background-color:rgb(var(--color-primary-100))}.hover\:bg-primary-200:hover{background-color:rgb(var(--color-primary-200))}.hover\:bg-primary-300:hover{background-color:rgb(var(--color-primary-300))}.hover\:bg-primary-400:hover{background-color:rgb(var(--color-primary-400))}.hover\:bg-primary-500:hover{background-color:rgb(var(--color-primary-500))}.hover\:bg-primary-600:hover{background-color:rgb(var(--color-primary-600))}.hover\:bg-primary-700:hover{background-color:rgb(var(--color-primary-700))}.hover\:bg-primary-800:hover{background-color:rgb(var(--color-primary-800))}.hover\:bg-primary-900:hover{background-color:rgb(var(--color-primary-900))}.hover\:bg-primary-950:hover{background-color:rgb(var(--color-primary-950))}.hover\:bg-red-100:hover{background-color:var(--color-red-100)}.hover\:bg-white:hover{background-color:var(--color-white)}.hover\:bg-white\/30:hover{background-color:#ffffff4d}@supports (color:color-mix(in lab, red, red)){.hover\:bg-white\/30:hover{background-color:color-mix(in oklab,var(--color-white)30%,transparent)}}.hover\:text-base-500:hover{color:rgb(var(--color-base-500))}.hover\:text-base-700:hover{color:rgb(var(--color-base-700))}.hover\:text-primary-600:hover{color:rgb(var(--color-primary-600))}.hover\:text-primary-600\!:hover{color:rgb(var(--color-primary-600))!important}.hover\:text-red-700:hover{color:var(--color-red-700)}.hover\:opacity-50:hover{opacity:.5}.hover\:shadow-md:hover{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.hover\:shadow-base-200:hover{--tw-shadow-color:rgb(rgb(var(--color-base-200)))}@supports (color:color-mix(in lab, red, red)){.hover\:shadow-base-200:hover{--tw-shadow-color:color-mix(in oklab,rgb(var(--color-base-200))var(--tw-shadow-alpha),transparent)}}.checked\:hover\:border-base-900\/20:checked:hover{border-color:color-mix(in srgb,rgb(rgb(var(--color-base-900)))20%,transparent)}@supports (color:color-mix(in lab, red, red)){.checked\:hover\:border-base-900\/20:checked:hover{border-color:color-mix(in oklab,rgb(var(--color-base-900))20%,transparent)}}.checked\:hover\:border-primary-600:checked:hover{border-color:rgb(var(--color-primary-600))}}.focus\:outline-hidden:focus{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.focus\:outline-hidden:focus{outline-offset:2px;outline:2px solid #0000}}.focus\:outline:focus{outline-style:var(--tw-outline-style);outline-width:1px}.focus\:outline-2:focus{outline-style:var(--tw-outline-style);outline-width:2px}.focus\:-outline-offset-2:focus{outline-offset:calc(2px*-1)}.focus\:outline-offset-2:focus{outline-offset:2px}.focus\:outline-primary-500:focus{outline-color:rgb(var(--color-primary-500))}.focus\:outline-primary-600:focus{outline-color:rgb(var(--color-primary-600))}.focus\:outline-red-500:focus{outline-color:var(--color-red-500)}.focus\:group-\[\.errors\]\:outline-red-600:focus:is(:where(.group).errors *){outline-color:var(--color-red-600)}.has-\[ol\]\:has-\[li\]\:block:has(:is(ol)):has(:is(li)){display:block}@media not all and (min-width:64rem){.max-lg\:flex-row-reverse{flex-direction:row-reverse}.max-lg\:justify-end{justify-content:flex-end}}@media not all and (min-width:48rem){.max-md\:-mt-px{margin-top:-1px}.max-md\:w-full{width:100%}.max-md\:border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.max-md\:first\:rounded-t-default:first-child{border-top-left-radius:var(--border-radius,6px);border-top-right-radius:var(--border-radius,6px)}.max-md\:last\:rounded-b-default:last-child{border-bottom-right-radius:var(--border-radius,6px);border-bottom-left-radius:var(--border-radius,6px)}}@media (min-width:40rem){.sm\:mt-0{margin-top:calc(var(--spacing)*0)}.sm\:ml-auto{margin-left:auto}.sm\:w-96{width:calc(var(--spacing)*96)}.sm\:flex-row{flex-direction:row}.sm\:items-center{align-items:center}}@media (min-width:48rem){.md\:absolute{position:absolute}.md\:relative{position:relative}.md\:sticky{position:sticky}.md\:left-72{left:calc(var(--spacing)*72)}.md\:col-span-1{grid-column:span 1/span 1}.md\:col-span-2{grid-column:span 2/span 2}.md\:col-span-3{grid-column:span 3/span 3}.md\:col-span-4{grid-column:span 4/span 4}.md\:col-span-5{grid-column:span 5/span 5}.md\:col-span-6{grid-column:span 6/span 6}.md\:col-span-7{grid-column:span 7/span 7}.md\:col-span-8{grid-column:span 8/span 8}.md\:col-span-9{grid-column:span 9/span 9}.md\:col-span-10{grid-column:span 10/span 10}.md\:col-span-11{grid-column:span 11/span 11}.md\:col-span-12{grid-column:span 12/span 12}.md\:mt-0{margin-top:calc(var(--spacing)*0)}.md\:mr-8{margin-right:calc(var(--spacing)*8)}.md\:mb-0{margin-bottom:calc(var(--spacing)*0)}.md\:-ml-px{margin-left:-1px}.md\:flex{display:flex}.md\:w-1\/2{width:50%}.md\:w-1\/3{width:33.3333%}.md\:w-1\/4{width:25%}.md\:w-1\/5{width:20%}.md\:w-1\/6{width:16.6667%}.md\:w-1\/7{width:14.2857%}.md\:w-2\/3{width:66.6667%}.md\:w-2\/4{width:50%}.md\:w-2\/5{width:40%}.md\:w-2\/6{width:33.3333%}.md\:w-2\/7{width:28.5714%}.md\:w-3\/4{width:75%}.md\:w-3\/5{width:60%}.md\:w-3\/6{width:50%}.md\:w-3\/7{width:42.8571%}.md\:w-4\/5{width:80%}.md\:w-4\/6{width:66.6667%}.md\:w-4\/7{width:57.1429%}.md\:w-5\/6{width:83.3333%}.md\:w-5\/7{width:71.4286%}.md\:w-6\/7{width:85.7143%}.md\:grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.md\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.md\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.md\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.md\:grid-cols-5{grid-template-columns:repeat(5,minmax(0,1fr))}.md\:grid-cols-6{grid-template-columns:repeat(6,minmax(0,1fr))}.md\:grid-cols-7{grid-template-columns:repeat(7,minmax(0,1fr))}.md\:grid-cols-8{grid-template-columns:repeat(8,minmax(0,1fr))}.md\:grid-cols-9{grid-template-columns:repeat(9,minmax(0,1fr))}.md\:grid-cols-10{grid-template-columns:repeat(10,minmax(0,1fr))}.md\:grid-cols-11{grid-template-columns:repeat(11,minmax(0,1fr))}.md\:grid-cols-12{grid-template-columns:repeat(12,minmax(0,1fr))}.md\:flex-row{flex-direction:row}.md\:items-center{align-items:center}.md\:gap-0\.5{gap:calc(var(--spacing)*.5)}.md\:gap-1{gap:calc(var(--spacing)*1)}.md\:gap-2{gap:calc(var(--spacing)*2)}.md\:gap-3{gap:calc(var(--spacing)*3)}.md\:gap-4{gap:calc(var(--spacing)*4)}.md\:gap-5{gap:calc(var(--spacing)*5)}.md\:gap-6{gap:calc(var(--spacing)*6)}.md\:gap-7{gap:calc(var(--spacing)*7)}.md\:gap-8{gap:calc(var(--spacing)*8)}.md\:gap-9{gap:calc(var(--spacing)*9)}.md\:gap-10{gap:calc(var(--spacing)*10)}.md\:gap-11{gap:calc(var(--spacing)*11)}.md\:gap-12{gap:calc(var(--spacing)*12)}.md\:border-0{border-style:var(--tw-border-style);border-width:0}.md\:border-2{border-style:var(--tw-border-style);border-width:2px}.md\:border-t-0{border-top-style:var(--tw-border-style);border-top-width:0}.md\:border-t-2{border-top-style:var(--tw-border-style);border-top-width:2px}.md\:border-r-0{border-right-style:var(--tw-border-style);border-right-width:0}.md\:border-r-2{border-right-style:var(--tw-border-style);border-right-width:2px}.md\:border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.md\:border-b-0{border-bottom-style:var(--tw-border-style);border-bottom-width:0}.md\:border-b-2{border-bottom-style:var(--tw-border-style);border-bottom-width:2px}.md\:border-l-0{border-left-style:var(--tw-border-style);border-left-width:0}.md\:border-l-2{border-left-style:var(--tw-border-style);border-left-width:2px}.md\:border-primary-500{border-color:rgb(var(--color-primary-500))}.md\:px-0{padding-inline:calc(var(--spacing)*0)}.md\:py-4{padding-block:calc(var(--spacing)*4)}.md\:first\:rounded-l-default:first-child{border-top-left-radius:var(--border-radius,6px);border-bottom-left-radius:var(--border-radius,6px)}.md\:last\:rounded-r-default:last-child{border-top-right-radius:var(--border-radius,6px);border-bottom-right-radius:var(--border-radius,6px)}}@media (min-width:64rem){.lg\:scrollable-top:after{content:var(--tw-content);content:var(--tw-content);content:var(--tw-content);top:-1px;right:calc(var(--spacing)*0);content:var(--tw-content);left:calc(var(--spacing)*0);content:var(--tw-content);height:calc(var(--spacing)*4);content:var(--tw-content);--tw-translate-y:-100%;translate:var(--tw-translate-x)var(--tw-translate-y);content:var(--tw-content);--tw-gradient-position:to top;position:absolute}@supports (background-image:linear-gradient(in lab, red, red)){.lg\:scrollable-top:after{--tw-gradient-position:to top in oklab}}.lg\:scrollable-top:after{background-image:linear-gradient(var(--tw-gradient-stops));content:var(--tw-content);--tw-gradient-from:var(--color-gray-100);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position));content:var(--tw-content);--tw-content:"";content:var(--tw-content)}@media not print{.lg\:scrollable-top:where(.dark,.dark *):after{content:var(--tw-content);background-image:none}}.lg\:absolute{position:absolute}.lg\:relative{position:relative}.lg\:sticky{position:sticky}.lg\:right-0{right:calc(var(--spacing)*0)}.lg\:bottom-0{bottom:calc(var(--spacing)*0)}.lg\:left-0{left:calc(var(--spacing)*0)}.lg\:col-span-1{grid-column:span 1/span 1}.lg\:col-span-2{grid-column:span 2/span 2}.lg\:col-span-3{grid-column:span 3/span 3}.lg\:col-span-4{grid-column:span 4/span 4}.lg\:col-span-5{grid-column:span 5/span 5}.lg\:col-span-6{grid-column:span 6/span 6}.lg\:col-span-7{grid-column:span 7/span 7}.lg\:col-span-8{grid-column:span 8/span 8}.lg\:col-span-9{grid-column:span 9/span 9}.lg\:col-span-10{grid-column:span 10/span 10}.lg\:col-span-11{grid-column:span 11/span 11}.lg\:col-span-12{grid-column:span 12/span 12}.lg\:mx-0{margin-inline:calc(var(--spacing)*0)}.lg\:-mt-2{margin-top:calc(var(--spacing)*-2)}.lg\:mt-2{margin-top:calc(var(--spacing)*2)}.lg\:mt-3{margin-top:calc(var(--spacing)*3)}.lg\:-mb-8{margin-bottom:calc(var(--spacing)*-8)}.lg\:mb-0{margin-bottom:calc(var(--spacing)*0)}.lg\:mb-12{margin-bottom:calc(var(--spacing)*12)}.lg\:ml-2{margin-left:calc(var(--spacing)*2)}.lg\:ml-auto{margin-left:auto}.lg\:block{display:block}.lg\:flex{display:flex}.lg\:hidden{display:none}.lg\:table{display:table}.lg\:table-cell{display:table-cell}.lg\:table-header-group{display:table-header-group}.lg\:table-row{display:table-row}.lg\:table-row-group{display:table-row-group}.lg\:h-\[64px\]{height:64px}.lg\:w-1\/2{width:50%}.lg\:w-1\/3{width:33.3333%}.lg\:w-1\/4{width:25%}.lg\:w-1\/5{width:20%}.lg\:w-1\/6{width:16.6667%}.lg\:w-1\/7{width:14.2857%}.lg\:w-2\/3{width:66.6667%}.lg\:w-2\/4{width:50%}.lg\:w-2\/5{width:40%}.lg\:w-2\/6{width:33.3333%}.lg\:w-2\/7{width:28.5714%}.lg\:w-3\/4{width:75%}.lg\:w-3\/5{width:60%}.lg\:w-3\/6{width:50%}.lg\:w-3\/7{width:42.8571%}.lg\:w-4\/5{width:80%}.lg\:w-4\/6{width:66.6667%}.lg\:w-4\/7{width:57.1429%}.lg\:w-5\/6{width:83.3333%}.lg\:w-5\/7{width:71.4286%}.lg\:w-6\/7{width:85.7143%}.lg\:w-10{width:calc(var(--spacing)*10)}.lg\:w-56{width:calc(var(--spacing)*56)}.lg\:w-72{width:calc(var(--spacing)*72)}.lg\:w-96{width:calc(var(--spacing)*96)}.lg\:w-auto{width:auto}.lg\:w-px{width:1px}.lg\:min-w-56{min-width:calc(var(--spacing)*56)}.lg\:grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.lg\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.lg\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.lg\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.lg\:grid-cols-5{grid-template-columns:repeat(5,minmax(0,1fr))}.lg\:grid-cols-6{grid-template-columns:repeat(6,minmax(0,1fr))}.lg\:grid-cols-7{grid-template-columns:repeat(7,minmax(0,1fr))}.lg\:grid-cols-8{grid-template-columns:repeat(8,minmax(0,1fr))}.lg\:grid-cols-9{grid-template-columns:repeat(9,minmax(0,1fr))}.lg\:grid-cols-10{grid-template-columns:repeat(10,minmax(0,1fr))}.lg\:grid-cols-11{grid-template-columns:repeat(11,minmax(0,1fr))}.lg\:grid-cols-12{grid-template-columns:repeat(12,minmax(0,1fr))}.lg\:flex-row{flex-direction:row}.lg\:flex-row-reverse{flex-direction:row-reverse}.lg\:items-center{align-items:center}.lg\:justify-start{justify-content:flex-start}.lg\:gap-0{gap:calc(var(--spacing)*0)}.lg\:gap-0\.5{gap:calc(var(--spacing)*.5)}.lg\:gap-1{gap:calc(var(--spacing)*1)}.lg\:gap-2{gap:calc(var(--spacing)*2)}.lg\:gap-3{gap:calc(var(--spacing)*3)}.lg\:gap-4{gap:calc(var(--spacing)*4)}.lg\:gap-5{gap:calc(var(--spacing)*5)}.lg\:gap-6{gap:calc(var(--spacing)*6)}.lg\:gap-7{gap:calc(var(--spacing)*7)}.lg\:gap-8{gap:calc(var(--spacing)*8)}.lg\:gap-9{gap:calc(var(--spacing)*9)}.lg\:gap-10{gap:calc(var(--spacing)*10)}.lg\:gap-11{gap:calc(var(--spacing)*11)}.lg\:gap-12{gap:calc(var(--spacing)*12)}.lg\:overflow-hidden{overflow:hidden}.lg\:rounded-default{border-radius:var(--border-radius,6px)}.lg\:rounded-none{border-radius:0}.lg\:rounded-t-default{border-top-left-radius:var(--border-radius,6px);border-top-right-radius:var(--border-radius,6px)}.lg\:rounded-b-default{border-bottom-right-radius:var(--border-radius,6px);border-bottom-left-radius:var(--border-radius,6px)}.lg\:border{border-style:var(--tw-border-style);border-width:1px}.lg\:border-0{border-style:var(--tw-border-style);border-width:0}.lg\:border-2{border-style:var(--tw-border-style);border-width:2px}.lg\:border-t{border-top-style:var(--tw-border-style);border-top-width:1px}.lg\:border-t-0{border-top-style:var(--tw-border-style);border-top-width:0}.lg\:border-t-2{border-top-style:var(--tw-border-style);border-top-width:2px}.lg\:border-r-0{border-right-style:var(--tw-border-style);border-right-width:0}.lg\:border-r-2{border-right-style:var(--tw-border-style);border-right-width:2px}.lg\:border-b-0{border-bottom-style:var(--tw-border-style);border-bottom-width:0}.lg\:border-b-2{border-bottom-style:var(--tw-border-style);border-bottom-width:2px}.lg\:border-l{border-left-style:var(--tw-border-style);border-left-width:1px}.lg\:border-l-0{border-left-style:var(--tw-border-style);border-left-width:0}.lg\:border-l-2{border-left-style:var(--tw-border-style);border-left-width:2px}.lg\:border-none{--tw-border-style:none;border-style:none}.lg\:border-base-200{border-color:rgb(var(--color-base-200))}.lg\:bg-transparent{background-color:#0000}.lg\:bg-white{background-color:var(--color-white)}.lg\:bg-white\/80{background-color:#fffc}@supports (color:color-mix(in lab, red, red)){.lg\:bg-white\/80{background-color:color-mix(in oklab,var(--color-white)80%,transparent)}}.lg\:p-3{padding:calc(var(--spacing)*3)}.lg\:px-0{padding-inline:calc(var(--spacing)*0)}.lg\:px-1\.5{padding-inline:calc(var(--spacing)*1.5)}.lg\:px-3{padding-inline:calc(var(--spacing)*3)}.lg\:py-0{padding-block:calc(var(--spacing)*0)}.lg\:py-3{padding-block:calc(var(--spacing)*3)}.lg\:pt-2{padding-top:calc(var(--spacing)*2)}.lg\:pr-0{padding-right:calc(var(--spacing)*0)}.lg\:pb-0{padding-bottom:calc(var(--spacing)*0)}.lg\:pb-11{padding-bottom:calc(var(--spacing)*11)}.lg\:pl-3{padding-left:calc(var(--spacing)*3)}.lg\:text-left{text-align:left}.lg\:align-top{vertical-align:top}.lg\:shadow-inner{--tw-shadow:inset 0 2px 4px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.lg\:shadow-none{--tw-shadow:0 0 #0000;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.lg\:shadow-xs{--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.lg\:backdrop-blur-xs{--tw-backdrop-blur:blur(var(--blur-xs));-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,);backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)}.lg\:group-\[\.field-row\]\:flex-row:is(:where(.group).field-row *){flex-direction:row}.lg\:group-\[\.field-row\]\:items-center:is(:where(.group).field-row *){align-items:center}.lg\:group-\[\.field-tabular\]\:flex-row:is(:where(.group).field-tabular *){flex-direction:row}.lg\:group-\[\.field-tabular\]\:items-center:is(:where(.group).field-tabular *){align-items:center}.lg\:group-\[\.first-row\]\:border-t-0:is(:where(.group).first-row *){border-top-style:var(--tw-border-style);border-top-width:0}.lg\:before\:hidden:before{content:var(--tw-content);display:none}.lg\:first\:border-t:first-child{border-top-style:var(--tw-border-style);border-top-width:1px}.lg\:first\:border-l-0:first-child{border-left-style:var(--tw-border-style);border-left-width:0}.lg\:first\:pl-3:first-child{padding-left:calc(var(--spacing)*3)}.lg\:first\:pl-6:first-child{padding-left:calc(var(--spacing)*6)}.lg\:last\:pr-3:last-child{padding-right:calc(var(--spacing)*3)}.lg\:last\:pr-6:last-child{padding-right:calc(var(--spacing)*6)}@media (hover:hover){.lg\:hover\:z-20:hover{z-index:20}.lg\:hover\:shadow-raised:hover{--tw-shadow:0 2px 12px var(--tw-shadow-color,rgb(var(--color-base-300)));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}}@media (min-width:80rem){.xl\:relative{position:relative}.xl\:ml-0{margin-left:calc(var(--spacing)*0)}.xl\:ml-72{margin-left:calc(var(--spacing)*72)}.xl\:block{display:block}.xl\:block\!{display:block!important}.xl\:hidden\!{display:none!important}.xl\:max-w-4xl{max-width:var(--container-4xl)}.xl\:text-base{font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height))}.xl\:text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}}@media (min-width:96rem){.\32 xl\:absolute{position:absolute}.\32 xl\:relative{position:relative}.\32 xl\:sticky{position:sticky}.\32 xl\:top-4{top:calc(var(--spacing)*4)}.\32 xl\:z-10{z-index:10}.\32 xl\:m-0{margin:calc(var(--spacing)*0)}.\32 xl\:-mx-1{margin-inline:calc(var(--spacing)*-1)}.\32 xl\:block\!{display:block!important}.\32 xl\:flex{display:flex}.\32 xl\:hidden{display:none}.\32 xl\:overflow-visible{overflow:visible}.\32 xl\:border-0{border-style:var(--tw-border-style);border-width:0}.\32 xl\:border-t-0\!{border-top-style:var(--tw-border-style)!important;border-top-width:0!important}.\32 xl\:bg-transparent{background-color:#0000}.\32 xl\:bg-transparent\!{background-color:#0000!important}.\32 xl\:px-0{padding-inline:calc(var(--spacing)*0)}.\32 xl\:px-1{padding-inline:calc(var(--spacing)*1)}.\32 xl\:py-0{padding-block:calc(var(--spacing)*0)}.\32 xl\:pb-1{padding-bottom:calc(var(--spacing)*1)}.\32 xl\:pb-24{padding-bottom:calc(var(--spacing)*24)}.\32 xl\:shadow-none{--tw-shadow:0 0 #0000;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}@container (min-width:1570px){.\@min-\[1570px\]\:-mx-4{margin-inline:calc(var(--spacing)*-4)}}@media not print{.dark\:block:where(.dark,.dark *){display:block}.dark\:hidden:where(.dark,.dark *){display:none}.dark\:border:where(.dark,.dark *){border-style:var(--tw-border-style);border-width:1px}.dark\:border-r:where(.dark,.dark *){border-right-style:var(--tw-border-style);border-right-width:1px}.dark\:border-amber-600\/10:where(.dark,.dark *){border-color:#dd74001a}@supports (color:color-mix(in lab, red, red)){.dark\:border-amber-600\/10:where(.dark,.dark *){border-color:color-mix(in oklab,var(--color-amber-600)10%,transparent)}}.dark\:border-base-50:where(.dark,.dark *){border-color:rgb(var(--color-base-50))}.dark\:border-base-100:where(.dark,.dark *){border-color:rgb(var(--color-base-100))}.dark\:border-base-200:where(.dark,.dark *){border-color:rgb(var(--color-base-200))}.dark\:border-base-300:where(.dark,.dark *){border-color:rgb(var(--color-base-300))}.dark\:border-base-400:where(.dark,.dark *){border-color:rgb(var(--color-base-400))}.dark\:border-base-500:where(.dark,.dark *){border-color:rgb(var(--color-base-500))}.dark\:border-base-600:where(.dark,.dark *){border-color:rgb(var(--color-base-600))}.dark\:border-base-700:where(.dark,.dark *){border-color:rgb(var(--color-base-700))}.dark\:border-base-700\!:where(.dark,.dark *){border-color:rgb(var(--color-base-700))!important}.dark\:border-base-800:where(.dark,.dark *){border-color:rgb(var(--color-base-800))}.dark\:border-base-900:where(.dark,.dark *){border-color:rgb(var(--color-base-900))}.dark\:border-base-950:where(.dark,.dark *){border-color:rgb(var(--color-base-950))}.dark\:border-blue-500:where(.dark,.dark *){border-color:var(--color-blue-500)}.dark\:border-blue-500\/10:where(.dark,.dark *){border-color:#3080ff1a}@supports (color:color-mix(in lab, red, red)){.dark\:border-blue-500\/10:where(.dark,.dark *){border-color:color-mix(in oklab,var(--color-blue-500)10%,transparent)}}.dark\:border-green-500:where(.dark,.dark *){border-color:var(--color-green-500)}.dark\:border-orange-500:where(.dark,.dark *){border-color:var(--color-orange-500)}.dark\:border-primary-500:where(.dark,.dark *){border-color:rgb(var(--color-primary-500))}.dark\:border-red-500:where(.dark,.dark *){border-color:var(--color-red-500)}.dark\:border-red-500\/20:where(.dark,.dark *){border-color:#fb2c3633}@supports (color:color-mix(in lab, red, red)){.dark\:border-red-500\/20:where(.dark,.dark *){border-color:color-mix(in oklab,var(--color-red-500)20%,transparent)}}.dark\:border-transparent:where(.dark,.dark *){border-color:#0000}.dark\:border-r-gray-700:where(.dark,.dark *){border-right-color:var(--color-gray-700)}.dark\:bg-amber-600\/20:where(.dark,.dark *){background-color:#dd740033}@supports (color:color-mix(in lab, red, red)){.dark\:bg-amber-600\/20:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-amber-600)20%,transparent)}}.dark\:bg-base-50:where(.dark,.dark *){background-color:rgb(var(--color-base-50))}.dark\:bg-base-100:where(.dark,.dark *){background-color:rgb(var(--color-base-100))}.dark\:bg-base-200:where(.dark,.dark *){background-color:rgb(var(--color-base-200))}.dark\:bg-base-300:where(.dark,.dark *){background-color:rgb(var(--color-base-300))}.dark\:bg-base-400:where(.dark,.dark *){background-color:rgb(var(--color-base-400))}.dark\:bg-base-500:where(.dark,.dark *){background-color:rgb(var(--color-base-500))}.dark\:bg-base-500\/20:where(.dark,.dark *){background-color:color-mix(in srgb,rgb(rgb(var(--color-base-500)))20%,transparent)}@supports (color:color-mix(in lab, red, red)){.dark\:bg-base-500\/20:where(.dark,.dark *){background-color:color-mix(in oklab,rgb(var(--color-base-500))20%,transparent)}}.dark\:bg-base-600:where(.dark,.dark *){background-color:rgb(var(--color-base-600))}.dark\:bg-base-700:where(.dark,.dark *){background-color:rgb(var(--color-base-700))}.dark\:bg-base-800:where(.dark,.dark *){background-color:rgb(var(--color-base-800))}.dark\:bg-base-800\!:where(.dark,.dark *){background-color:rgb(var(--color-base-800))!important}.dark\:bg-base-900:where(.dark,.dark *){background-color:rgb(var(--color-base-900))}.dark\:bg-base-900\/80:where(.dark,.dark *){background-color:color-mix(in srgb,rgb(rgb(var(--color-base-900)))80%,transparent)}@supports (color:color-mix(in lab, red, red)){.dark\:bg-base-900\/80:where(.dark,.dark *){background-color:color-mix(in oklab,rgb(var(--color-base-900))80%,transparent)}}.dark\:bg-base-950:where(.dark,.dark *){background-color:rgb(var(--color-base-950))}.dark\:bg-base-950\/20:where(.dark,.dark *){background-color:color-mix(in srgb,rgb(rgb(var(--color-base-950)))20%,transparent)}@supports (color:color-mix(in lab, red, red)){.dark\:bg-base-950\/20:where(.dark,.dark *){background-color:color-mix(in oklab,rgb(var(--color-base-950))20%,transparent)}}.dark\:bg-blue-500\/20:where(.dark,.dark *){background-color:#3080ff33}@supports (color:color-mix(in lab, red, red)){.dark\:bg-blue-500\/20:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-blue-500)20%,transparent)}}.dark\:bg-gray-900:where(.dark,.dark *){background-color:var(--color-gray-900)}.dark\:bg-green-500\/20:where(.dark,.dark *){background-color:#00c75833}@supports (color:color-mix(in lab, red, red)){.dark\:bg-green-500\/20:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-green-500)20%,transparent)}}.dark\:bg-orange-500\/20:where(.dark,.dark *){background-color:#fe6e0033}@supports (color:color-mix(in lab, red, red)){.dark\:bg-orange-500\/20:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-orange-500)20%,transparent)}}.dark\:bg-primary-50:where(.dark,.dark *){background-color:rgb(var(--color-primary-50))}.dark\:bg-primary-100:where(.dark,.dark *){background-color:rgb(var(--color-primary-100))}.dark\:bg-primary-200:where(.dark,.dark *){background-color:rgb(var(--color-primary-200))}.dark\:bg-primary-300:where(.dark,.dark *){background-color:rgb(var(--color-primary-300))}.dark\:bg-primary-400:where(.dark,.dark *){background-color:rgb(var(--color-primary-400))}.dark\:bg-primary-500:where(.dark,.dark *){background-color:rgb(var(--color-primary-500))}.dark\:bg-primary-500\/20:where(.dark,.dark *){background-color:color-mix(in srgb,rgb(rgb(var(--color-primary-500)))20%,transparent)}@supports (color:color-mix(in lab, red, red)){.dark\:bg-primary-500\/20:where(.dark,.dark *){background-color:color-mix(in oklab,rgb(var(--color-primary-500))20%,transparent)}}.dark\:bg-primary-600:where(.dark,.dark *){background-color:rgb(var(--color-primary-600))}.dark\:bg-primary-700:where(.dark,.dark *){background-color:rgb(var(--color-primary-700))}.dark\:bg-primary-800:where(.dark,.dark *){background-color:rgb(var(--color-primary-800))}.dark\:bg-primary-900:where(.dark,.dark *){background-color:rgb(var(--color-primary-900))}.dark\:bg-primary-950:where(.dark,.dark *){background-color:rgb(var(--color-primary-950))}.dark\:bg-red-500\/20:where(.dark,.dark *){background-color:#fb2c3633}@supports (color:color-mix(in lab, red, red)){.dark\:bg-red-500\/20:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-red-500)20%,transparent)}}.dark\:bg-transparent:where(.dark,.dark *){background-color:#0000}.dark\:bg-white\/\[\.02\]:where(.dark,.dark *){background-color:#ffffff05}@supports (color:color-mix(in lab, red, red)){.dark\:bg-white\/\[\.02\]:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-white)2%,transparent)}}.dark\:bg-white\/\[\.04\]:where(.dark,.dark *){background-color:#ffffff0a}@supports (color:color-mix(in lab, red, red)){.dark\:bg-white\/\[\.04\]:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-white)4%,transparent)}}.dark\:bg-white\/\[\.06\]:where(.dark,.dark *){background-color:#ffffff0f}@supports (color:color-mix(in lab, red, red)){.dark\:bg-white\/\[\.06\]:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-white)6%,transparent)}}.dark\:text-base-50:where(.dark,.dark *){color:rgb(var(--color-base-50))}.dark\:text-base-100:where(.dark,.dark *){color:rgb(var(--color-base-100))}.dark\:text-base-200:where(.dark,.dark *){color:rgb(var(--color-base-200))}.dark\:text-base-300:where(.dark,.dark *){color:rgb(var(--color-base-300))}.dark\:text-base-300\!:where(.dark,.dark *){color:rgb(var(--color-base-300))!important}.dark\:text-base-400:where(.dark,.dark *){color:rgb(var(--color-base-400))}.dark\:text-base-500:where(.dark,.dark *){color:rgb(var(--color-base-500))}.dark\:text-base-600:where(.dark,.dark *){color:rgb(var(--color-base-600))}.dark\:text-base-700:where(.dark,.dark *){color:rgb(var(--color-base-700))}.dark\:text-base-800:where(.dark,.dark *){color:rgb(var(--color-base-800))}.dark\:text-base-900:where(.dark,.dark *){color:rgb(var(--color-base-900))}.dark\:text-base-950:where(.dark,.dark *){color:rgb(var(--color-base-950))}.dark\:text-blue-400:where(.dark,.dark *){color:var(--color-blue-400)}.dark\:text-font-default-dark:where(.dark,.dark *){color:rgb(var(--color-font-default-dark))}.dark\:text-font-important-dark:where(.dark,.dark *){color:rgb(var(--color-font-important-dark))}.dark\:text-font-important-dark\!:where(.dark,.dark *){color:rgb(var(--color-font-important-dark))!important}.dark\:text-font-subtle-dark:where(.dark,.dark *){color:rgb(var(--color-font-subtle-dark))}.dark\:text-green-400:where(.dark,.dark *){color:var(--color-green-400)}.dark\:text-orange-400:where(.dark,.dark *){color:var(--color-orange-400)}.dark\:text-primary-400:where(.dark,.dark *){color:rgb(var(--color-primary-400))}.dark\:text-primary-500:where(.dark,.dark *){color:rgb(var(--color-primary-500))}.dark\:text-red-400:where(.dark,.dark *){color:var(--color-red-400)}.dark\:text-red-500:where(.dark,.dark *){color:var(--color-red-500)}.dark\:text-white:where(.dark,.dark *){color:var(--color-white)}.dark\:placeholder-font-subtle-dark:where(.dark,.dark *)::placeholder{color:rgb(var(--color-font-subtle-dark))}.dark\:scheme-dark:where(.dark,.dark *){color-scheme:dark}.dark\:\!outline-primary-700:where(.dark,.dark *){outline-color:rgb(var(--color-primary-700))!important}.dark\:outline-green-500\/20:where(.dark,.dark *){outline-color:#00c75833}@supports (color:color-mix(in lab, red, red)){.dark\:outline-green-500\/20:where(.dark,.dark *){outline-color:color-mix(in oklab,var(--color-green-500)20%,transparent)}}.dark\:outline-red-500\/20:where(.dark,.dark *){outline-color:#fb2c3633}@supports (color:color-mix(in lab, red, red)){.dark\:outline-red-500\/20:where(.dark,.dark *){outline-color:color-mix(in oklab,var(--color-red-500)20%,transparent)}}:is(.dark\:\*\:text-font-important-dark:where(.dark,.dark *)>*){color:rgb(var(--color-font-important-dark))}@media (hover:hover){.dark\:group-hover\:text-primary-500:where(.dark,.dark *):is(:where(.group):hover *){color:rgb(var(--color-primary-500))}.dark\:group-hover\/action\:bg-base-800:where(.dark,.dark *):is(:where(.group\/action):hover *){background-color:rgb(var(--color-base-800))}.dark\:group-hover\/action\:text-white:where(.dark,.dark *):is(:where(.group\/action):hover *){color:var(--color-white)}}.dark\:group-\[\.errors\]\:border-red-500:where(.dark,.dark *):is(:where(.group).errors *){border-color:var(--color-red-500)}.dark\:group-\[\.errors\]\:border-red-500\!:where(.dark,.dark *):is(:where(.group).errors *){border-color:var(--color-red-500)!important}.dark\:group-\[\.errors\]\:border-x-red-500:where(.dark,.dark *):is(:where(.group).errors *){border-inline-color:var(--color-red-500)}.dark\:group-\[\.errors\]\:border-t-red-500:where(.dark,.dark *):is(:where(.group).errors *){border-top-color:var(--color-red-500)}.dark\:before\:text-base-300:where(.dark,.dark *):before{content:var(--tw-content);color:rgb(var(--color-base-300))}.dark\:before\:text-font-important-dark:where(.dark,.dark *):before{content:var(--tw-content);color:rgb(var(--color-font-important-dark))}.dark\:after\:bg-transparent:where(.dark,.dark *):after{content:var(--tw-content);background-color:#0000}.dark\:after\:text-base-700:where(.dark,.dark *):after{content:var(--tw-content);color:rgb(var(--color-base-700))}.dark\:checked\:border-primary-600:where(.dark,.dark *):checked{border-color:rgb(var(--color-primary-600))}.dark\:checked\:bg-green-700:where(.dark,.dark *):checked{background-color:var(--color-green-700)}.dark\:checked\:bg-primary-600:where(.dark,.dark *):checked{background-color:rgb(var(--color-primary-600))}.dark\:checked\:after\:bg-base-900:where(.dark,.dark *):checked:after{content:var(--tw-content);background-color:rgb(var(--color-base-900))}.dark\:checked\:after\:text-white:where(.dark,.dark *):checked:after{content:var(--tw-content);color:var(--color-white)}.dark\:focus-within\:group-\[\.errors\]\:outline-red-500:where(.dark,.dark *):focus-within:is(:where(.group).errors *){outline-color:var(--color-red-500)}@media (hover:hover){.dark\:hover\:border-base-700:where(.dark,.dark *):hover{border-color:rgb(var(--color-base-700))}.dark\:hover\:bg-base-700:where(.dark,.dark *):hover{background-color:rgb(var(--color-base-700))}.dark\:hover\:bg-base-900:where(.dark,.dark *):hover{background-color:rgb(var(--color-base-900))}.dark\:hover\:bg-red-500\/20:where(.dark,.dark *):hover{background-color:#fb2c3633}@supports (color:color-mix(in lab, red, red)){.dark\:hover\:bg-red-500\/20:where(.dark,.dark *):hover{background-color:color-mix(in oklab,var(--color-red-500)20%,transparent)}}.dark\:hover\:bg-white\/\[\.04\]:where(.dark,.dark *):hover{background-color:#ffffff0a}@supports (color:color-mix(in lab, red, red)){.dark\:hover\:bg-white\/\[\.04\]:where(.dark,.dark *):hover{background-color:color-mix(in oklab,var(--color-white)4%,transparent)}}.dark\:hover\:bg-white\/\[\.06\]:where(.dark,.dark *):hover{background-color:#ffffff0f}@supports (color:color-mix(in lab, red, red)){.dark\:hover\:bg-white\/\[\.06\]:where(.dark,.dark *):hover{background-color:color-mix(in oklab,var(--color-white)6%,transparent)}}.dark\:hover\:text-base-200:where(.dark,.dark *):hover{color:rgb(var(--color-base-200))}.dark\:hover\:text-base-400:where(.dark,.dark *):hover{color:rgb(var(--color-base-400))}.dark\:hover\:text-primary-500:where(.dark,.dark *):hover{color:rgb(var(--color-primary-500))}.dark\:hover\:text-primary-500\!:where(.dark,.dark *):hover{color:rgb(var(--color-primary-500))!important}.dark\:hover\:text-red-500:where(.dark,.dark *):hover{color:var(--color-red-500)}.dark\:hover\:text-white:where(.dark,.dark *):hover{color:var(--color-white)}.dark\:hover\:shadow-base-800\/50:where(.dark,.dark *):hover{--tw-shadow-color:color-mix(in srgb,rgb(rgb(var(--color-base-800)))50%,transparent)}@supports (color:color-mix(in lab, red, red)){.dark\:hover\:shadow-base-800\/50:where(.dark,.dark *):hover{--tw-shadow-color:color-mix(in oklab,color-mix(in oklab,rgb(var(--color-base-800))50%,transparent)var(--tw-shadow-alpha),transparent)}}}.dark\:focus\:group-\[\.errors\]\:outline-red-500:where(.dark,.dark *):focus:is(:where(.group).errors *){outline-color:var(--color-red-500)}@media (min-width:48rem){.dark\:md\:border-base-800:where(.dark,.dark *){border-color:rgb(var(--color-base-800))}.dark\:md\:border-primary-600\!:where(.dark,.dark *){border-color:rgb(var(--color-primary-600))!important}}@media (min-width:64rem){.dark\:lg\:border-base-800:where(.dark,.dark *){border-color:rgb(var(--color-base-800))}}}@media (min-width:64rem){@media not print{.lg\:dark\:border-base-800:where(.dark,.dark *){border-color:rgb(var(--color-base-800))}}}@media not print{@media (min-width:64rem){.dark\:lg\:bg-transparent\!:where(.dark,.dark *){background-color:#0000!important}}}@media (min-width:64rem){@media not print{.lg\:dark\:bg-base-900:where(.dark,.dark *){background-color:rgb(var(--color-base-900))}.lg\:dark\:bg-base-900\/80:where(.dark,.dark *){background-color:color-mix(in srgb,rgb(rgb(var(--color-base-900)))80%,transparent)}@supports (color:color-mix(in lab, red, red)){.lg\:dark\:bg-base-900\/80:where(.dark,.dark *){background-color:color-mix(in oklab,rgb(var(--color-base-900))80%,transparent)}}@media (hover:hover){.lg\:dark\:hover\:shadow-raised-dark:where(.dark,.dark *):hover{--tw-shadow:0 2px 12px var(--tw-shadow-color,rgb(var(--color-base-700)));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}}}@media (min-width:96rem){@media not print{.\32 xl\:dark\:border-base-800:where(.dark,.dark *){border-color:rgb(var(--color-base-800))}.\32 xl\:dark\:bg-transparent\!:where(.dark,.dark *){background-color:#0000!important}}}.prose-headings\:font-medium :where(h1,h2,h3,h4,h5,h6,th):not(:where([class~=not-prose],[class~=not-prose] *)){--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.prose-headings\:text-base-700 :where(h1,h2,h3,h4,h5,h6,th):not(:where([class~=not-prose],[class~=not-prose] *)){color:rgb(var(--color-base-700))}@media not print{.dark\:prose-headings\:text-base-200:where(.dark,.dark *) :where(h1,h2,h3,h4,h5,h6,th):not(:where([class~=not-prose],[class~=not-prose] *)){color:rgb(var(--color-base-200))}}.prose-a\:text-primary-600 :where(a):not(:where([class~=not-prose],[class~=not-prose] *)){color:rgb(var(--color-primary-600))}.prose-blockquote\:border-l-4 :where(blockquote):not(:where([class~=not-prose],[class~=not-prose] *)){border-left-style:var(--tw-border-style);border-left-width:4px}.prose-blockquote\:not-italic :where(blockquote):not(:where([class~=not-prose],[class~=not-prose] *)){font-style:normal}@media not print{.dark\:prose-blockquote\:border-base-700:where(.dark,.dark *) :where(blockquote):not(:where([class~=not-prose],[class~=not-prose] *)){border-color:rgb(var(--color-base-700))}.dark\:prose-blockquote\:text-base-300:where(.dark,.dark *) :where(blockquote):not(:where([class~=not-prose],[class~=not-prose] *)){color:rgb(var(--color-base-300))}}.prose-strong\:text-base-700 :where(strong):not(:where([class~=not-prose],[class~=not-prose] *)){color:rgb(var(--color-base-700))}@media not print{.dark\:prose-strong\:text-base-200:where(.dark,.dark *) :where(strong):not(:where([class~=not-prose],[class~=not-prose] *)){color:rgb(var(--color-base-200))}}.prose-pre\:rounded-default :where(pre):not(:where([class~=not-prose],[class~=not-prose] *)){border-radius:var(--border-radius,6px)}.prose-pre\:bg-base-50 :where(pre):not(:where([class~=not-prose],[class~=not-prose] *)){background-color:rgb(var(--color-base-50))}@media not print{.dark\:prose-pre\:bg-base-800:where(.dark,.dark *) :where(pre):not(:where([class~=not-prose],[class~=not-prose] *)){background-color:rgb(var(--color-base-800))}}.prose-ol\:list-decimal :where(ol):not(:where([class~=not-prose],[class~=not-prose] *)){list-style-type:decimal}.prose-ul\:list-disc :where(ul):not(:where([class~=not-prose],[class~=not-prose] *)){list-style-type:disc}.\[\&_img\]\:rounded-default img{border-radius:var(--border-radius,6px)}.\[\&\>tbody\:nth-child\(odd\)\]\:bg-base-50>tbody:nth-child(odd){background-color:rgb(var(--color-base-50))}@media not print{.dark\:\[\&\>tbody\:nth-child\(odd\)\]\:bg-white\/\[\.02\]:where(.dark,.dark *)>tbody:nth-child(odd){background-color:#ffffff05}@supports (color:color-mix(in lab, red, red)){.dark\:\[\&\>tbody\:nth-child\(odd\)\]\:bg-white\/\[\.02\]:where(.dark,.dark *)>tbody:nth-child(odd){background-color:color-mix(in oklab,var(--color-white)2%,transparent)}}}}:is(.errorlist>*):before{content:var(--tw-content);margin-right:calc(var(--spacing)*1);content:var(--tw-content);letter-spacing:normal;text-transform:none;white-space:nowrap;word-wrap:normal;-moz-font-feature-settings:"liga";-moz-osx-font-smoothing:grayscale;content:var(--tw-content);vertical-align:bottom;content:var(--tw-content);--tw-content:"warning";content:var(--tw-content);direction:ltr;font-family:Material Symbols Outlined;font-size:18px;font-style:normal;font-weight:400;line-height:1;display:inline-block}[x-cloak]{display:none!important}.asteriskField{color:var(--color-red-600)}.sortable-ghost{opacity:.5}input[type=search]::-webkit-search-cancel-button{z-index:10}@media not print{input[type=search]::-webkit-search-cancel-button:where(.dark,.dark *){--tw-invert:invert(100%);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}}select:not([class*=bg-none]):not([multiple]){background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 48 48'%3e%3cpath fill='#6B7280' d='M24 31.4 11.3 18.7l2.85-2.8L24 25.8l9.85-9.85 2.85 2.8Z'/%3e%3c/svg%3e");background-position:right .7rem center;background-repeat:no-repeat;background-size:1.125rem 1.125rem}#changelist-actions select:not([class*=bg-none]):not([multiple]){background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 48 48'%3e%3cpath fill='#ffffff' d='M24 31.4 11.3 18.7l2.85-2.8L24 25.8l9.85-9.85 2.85 2.8Z'/%3e%3c/svg%3e")}select:after{content:"";display:block}table tr.selected td,table tr.selected th{background-color:#fff0851a}@supports (color:color-mix(in lab, red, red)){table tr.selected td,table tr.selected th{background-color:color-mix(in oklab,var(--color-yellow-200)10%,transparent)}}@media not print{:is(table tr.selected td,table tr.selected th):where(.dark,.dark *){background-color:#ffffff08}@supports (color:color-mix(in lab, red, red)){:is(table tr.selected td,table tr.selected th):where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-white)3%,transparent)}}}.datetimeshortcuts{flex-direction:row-reverse;align-items:center;font-size:0;display:flex;position:absolute;top:1px;right:1px}.datetimeshortcuts a{height:calc(var(--spacing)*9);color:rgb(var(--color-base-400));transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));align-items:center;font-size:0;display:flex}@media (hover:hover){.datetimeshortcuts a:hover{color:rgb(var(--color-base-700))}}.datetimeshortcuts a:first-child{display:none}.datetimeshortcuts a:first-child:after{letter-spacing:normal;text-transform:none;white-space:nowrap;word-wrap:normal;-moz-font-feature-settings:"liga";-moz-osx-font-smoothing:grayscale;height:calc(var(--spacing)*9);padding-inline:calc(var(--spacing)*3);padding-block:calc(var(--spacing)*2);text-align:center;--tw-leading:1;--tw-content:"update";content:var(--tw-content);direction:ltr;justify-content:center;align-items:center;font-family:Material Symbols Outlined;font-size:18px;font-style:normal;font-weight:400;line-height:1;display:flex}@media (hover:hover){.datetimeshortcuts a:first-child:after:hover{color:rgb(var(--color-base-700))}}@media not print{.datetimeshortcuts a:first-child:after:where(){border-color:rgb(var(--color-base-700))}@media (hover:hover){.datetimeshortcuts a:first-child:after:where():hover{color:rgb(var(--color-base-700))}}}.datetimeshortcuts a:first-child:after{display:flex}.clock-icon,.date-icon{color:rgb(var(--color-base-400));transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));display:block}@media (hover:hover){:is(.clock-icon,.date-icon):hover{color:rgb(var(--color-base-700))}}@media not print{:is(.clock-icon,.date-icon):where(.dark,.dark *){color:rgb(var(--color-base-500))}@media (hover:hover){:is(.clock-icon,.date-icon):where(.dark,.dark *):hover{color:rgb(var(--color-base-200))}}}:is(.clock-icon,.date-icon):after{content:var(--tw-content);letter-spacing:normal;text-transform:none;white-space:nowrap;word-wrap:normal;-moz-font-feature-settings:"liga";-moz-osx-font-smoothing:grayscale;content:var(--tw-content);border-color:rgb(var(--color-base-200));content:var(--tw-content);padding-inline:calc(var(--spacing)*3);direction:ltr;font-family:Material Symbols Outlined;font-size:18px;font-style:normal;font-weight:400;line-height:1;display:inline-block}@media not print{:is(.clock-icon,.date-icon):where(.dark,.dark *):after{content:var(--tw-content);border-color:rgb(var(--color-base-700))}}.date-icon:after{content:"calendar_today"}.clock-icon:after{content:"schedule"}.timezonewarning{top:calc(var(--spacing)*2);right:calc(var(--spacing)*11);text-overflow:ellipsis;white-space:nowrap;color:rgb(var(--color-base-500));align-items:center;font-size:0;display:block;position:absolute;overflow:hidden}.timezonewarning:hover{border-radius:var(--border-radius,6px);background-color:var(--color-white);padding-inline:calc(var(--spacing)*3);font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height));--tw-leading:calc(var(--spacing)*9);line-height:calc(var(--spacing)*9);inset:1px}@media not print{.timezonewarning:hover:where(.dark,.dark *){background-color:rgb(var(--color-base-900))}}.timezonewarning:hover:before{margin-right:calc(var(--spacing)*2);display:none}.timezonewarning:before{letter-spacing:normal;text-transform:none;white-space:nowrap;word-wrap:normal;-moz-font-feature-settings:"liga";-moz-osx-font-smoothing:grayscale;cursor:pointer;border-color:rgb(var(--color-base-200));text-align:center;font-family:Material Symbols Outlined;font-size:18px;font-style:normal;font-weight:400;line-height:1;font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height));color:var(--color-red-600);direction:ltr;justify-content:center;align-items:center;display:block}@media not print{.timezonewarning:before:where(){border-color:rgb(var(--color-base-700))}}.timezonewarning:before{content:"warning"}.selector{max-width:var(--container-5xl);flex-direction:column;flex-grow:1;align-items:center;display:flex}@media (min-width:48rem){.selector{flex-direction:row}}.selector .selector-available-title label,.selector .selector-chosen-title label{margin-bottom:calc(var(--spacing)*3);border-bottom-style:var(--tw-border-style);border-bottom-width:1px;border-color:rgb(var(--color-base-200));padding-inline:calc(var(--spacing)*4);padding-block:calc(var(--spacing)*2);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold);color:rgb(var(--color-font-important-light));display:block}@media not print{:is(.selector .selector-available-title label,.selector .selector-chosen-title label):where(.dark,.dark *){border-color:rgb(var(--color-base-700));color:rgb(var(--color-font-important-dark))}}.selector .helptext{margin-bottom:calc(var(--spacing)*3);border-bottom-style:var(--tw-border-style);border-bottom-width:1px;border-color:rgb(var(--color-base-200));padding-inline:calc(var(--spacing)*4);padding-bottom:calc(var(--spacing)*3)}@media not print{.selector .helptext:where(.dark,.dark *){border-color:rgb(var(--color-base-700))}}.selector select{background-image:none;flex-grow:1;width:100%}@media not print{.selector select:where(.dark,.dark *){background-color:rgb(var(--color-base-900));color-scheme:dark}}.selector option{text-overflow:ellipsis;white-space:nowrap;padding-inline:calc(var(--spacing)*3);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));color:rgb(var(--color-base-500));overflow:hidden}@media not print{.selector option:where(.dark,.dark *){color:rgb(var(--color-base-300))}}.selector .list-footer-display{border-top-style:var(--tw-border-style);border-top-width:1px;border-color:rgb(var(--color-base-200));padding-block:calc(var(--spacing)*2);text-align:center;font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}@media not print{.selector .list-footer-display:where(.dark,.dark *){border-color:rgb(var(--color-base-700))}}.selector-chosen,.selector-available{border-radius:var(--border-radius,6px);border-style:var(--tw-border-style);border-width:1px;border-color:rgb(var(--color-base-200));--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);flex-direction:column;flex-grow:1;align-self:stretch;display:flex}@media (min-width:48rem){.selector-chosen,.selector-available{width:calc(var(--spacing)*72)}}@media (min-width:64rem){.selector-chosen,.selector-available{width:calc(var(--spacing)*96)}}@media not print{:is(.selector-chosen,.selector-available):where(.dark,.dark *){border-color:rgb(var(--color-base-700))}}.selector-chosen h2,.selector-available h2{margin-bottom:calc(var(--spacing)*3);border-bottom-style:var(--tw-border-style);border-bottom-width:1px;border-color:rgb(var(--color-base-200));padding-inline:calc(var(--spacing)*3);padding-block:calc(var(--spacing)*2);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));color:rgb(var(--color-base-500))}@media not print{:is(.selector-chosen h2,.selector-available h2):where(.dark,.dark *){border-color:rgb(var(--color-base-700));color:rgb(var(--color-base-200))}}.selector-filter{display:flex}.selector-filter input{margin-inline:calc(var(--spacing)*3);margin-bottom:calc(var(--spacing)*3);border-radius:var(--border-radius,6px);background-color:rgb(var(--color-base-100));padding-inline:calc(var(--spacing)*3);padding-block:calc(var(--spacing)*2);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);color:rgb(var(--color-base-500));flex-grow:1;display:block}.selector-filter input:focus{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.selector-filter input:focus{outline-offset:2px;outline:2px solid #0000}}@media not print{.selector-filter input:where(.dark,.dark *){background-color:rgb(var(--color-base-800));color:rgb(var(--color-base-300))}}.selector-chooseall,.selector-clearall{border-top-style:var(--tw-border-style);border-top-width:1px;border-color:rgb(var(--color-base-200));padding-block:calc(var(--spacing)*2);text-align:center;font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));color:rgb(var(--color-primary-500));display:block}@media not print{:is(.selector-chooseall,.selector-clearall):where(.dark,.dark *){border-color:rgb(var(--color-base-700))}}.selector-clearall{color:var(--color-red-600)}@media not print{.selector-clearall:where(.dark,.dark *){color:var(--color-red-500)}}.selector-chooser{margin-block:calc(var(--spacing)*4);width:calc(var(--spacing)*14);flex-direction:column;font-size:0;display:flex}.selector-chooser li{padding-block:calc(var(--spacing)*1);text-align:center}.selector-add:after,.selector-remove:after{letter-spacing:normal;text-transform:none;white-space:nowrap;word-wrap:normal;-moz-font-feature-settings:"liga";-moz-osx-font-smoothing:grayscale;width:calc(var(--spacing)*5);color:rgb(var(--color-base-400));direction:ltr;font-family:Material Symbols Outlined;font-size:18px;font-style:normal;font-weight:400;line-height:1;display:inline-block}.selector-add:after{content:"arrow_forward"}.selector-remove:after{content:"arrow_back"}.related-widget-wrapper{flex-wrap:nowrap!important}.related-widget-wrapper-link{order:9999}.empty-form{display:none}.tabular-table tbody.has_original,.tabular-table .template tr{border-top-style:var(--tw-border-style);border-top-width:1px;border-color:rgb(var(--color-base-200))}@media not print{:is(.tabular-table tbody.has_original,.tabular-table .template tr):where(.dark,.dark *){border-color:rgb(var(--color-base-800))}}.tabular-table tbody.has_original:first-of-type{border-top-style:var(--tw-border-style);border-top-width:0}.add-row{background-color:var(--color-white);padding-inline:calc(var(--spacing)*3);padding-block:calc(var(--spacing)*5);text-align:right;vertical-align:middle;--tw-font-weight:var(--font-weight-normal);font-weight:var(--font-weight-normal)}@media not print{.add-row:where(.dark,.dark *){background-color:rgb(var(--color-base-900))}}[data-inline-type=stacked] .add-row{overflow:hidden}.add-row td{padding-inline:calc(var(--spacing)*3);padding-block:calc(var(--spacing)*4)}.add-row a{border-radius:var(--border-radius,6px);border-style:var(--tw-border-style);border-width:1px;border-color:rgb(var(--color-base-200));background-color:var(--color-white);padding-inline:calc(var(--spacing)*3);padding-block:calc(var(--spacing)*2);text-align:center;--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);display:block}@media (hover:hover){.add-row a:hover{color:rgb(var(--color-base-700))}}@media (min-width:64rem){.add-row a{float:right}}@media not print{.add-row a:where(.dark,.dark *){border-color:rgb(var(--color-base-700));background-color:rgb(var(--color-base-900))}@media (hover:hover){.add-row a:where(.dark,.dark *):hover{color:rgb(var(--color-base-200))}}}.inline-deletelink{--tw-leading:1;color:var(--color-red-600);line-height:1;display:block}@media not print{.inline-deletelink:where(.dark,.dark *){color:var(--color-red-500)}}td .inline-deletelink{display:block}@media (min-width:64rem){td .inline-deletelink{margin-top:calc(var(--spacing)*2.5)}}.select2.select2-container{max-width:var(--container-2xl);border-radius:var(--border-radius,6px);border-style:var(--tw-border-style);border-width:1px;border-color:rgb(var(--color-base-200));background-color:var(--color-white);--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);position:relative;width:100%!important;min-height:38px!important}@media not print{.select2.select2-container:where(.dark,.dark *){border-color:rgb(var(--color-base-700));background-color:rgb(var(--color-base-900))}}.select2-selection{--tw-outline-style:none;outline-style:none}.select2.select2-container--open{border-bottom-style:var(--tw-border-style);border-bottom-width:0;border-color:rgb(var(--color-primary-600));outline-style:var(--tw-outline-style);outline-offset:calc(2px*-1);outline-width:2px;outline-color:rgb(var(--color-primary-600));position:relative}.select2.select2-container.select2-container--focus{outline-style:var(--tw-outline-style);outline-offset:calc(2px*-1);outline-width:2px;outline-color:rgb(var(--color-primary-600))}.errors .select2.select2-container{border-color:var(--color-red-600)}.select2-container.select2-container--admin-autocomplete .select2-selection--single{height:auto}.select2-container.select2-container--admin-autocomplete .select2-selection--single .select2-selection__rendered{height:calc(var(--spacing)*9);padding-inline:calc(var(--spacing)*3);padding-block:calc(var(--spacing)*2);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);color:rgb(var(--color-font-default-light))}@media not print{.select2-container.select2-container--admin-autocomplete .select2-selection--single .select2-selection__rendered:where(.dark,.dark *){color:rgb(var(--color-font-default-dark))}}.select2-container.select2-container--admin-autocomplete .select2-selection--multiple .select2-selection__clear,.select2-container.select2-container--admin-autocomplete .select2-selection--single .select2-selection__clear{margin-top:calc(var(--spacing)*-2);height:calc(var(--spacing)*9);align-items:center;font-size:0;display:flex}:is(.select2-container.select2-container--admin-autocomplete .select2-selection--multiple .select2-selection__clear,.select2-container.select2-container--admin-autocomplete .select2-selection--single .select2-selection__clear):after{content:var(--tw-content);color:rgb(var(--color-base-400))}@media (hover:hover){:is(.select2-container.select2-container--admin-autocomplete .select2-selection--multiple .select2-selection__clear,.select2-container.select2-container--admin-autocomplete .select2-selection--single .select2-selection__clear):hover:after{content:var(--tw-content);color:rgb(var(--color-base-700))}}@media not print{:is(.select2-container.select2-container--admin-autocomplete .select2-selection--multiple .select2-selection__clear,.select2-container.select2-container--admin-autocomplete .select2-selection--single .select2-selection__clear):where(.dark,.dark *):after{content:var(--tw-content);color:rgb(var(--color-base-500))}@media (hover:hover){:is(.select2-container.select2-container--admin-autocomplete .select2-selection--multiple .select2-selection__clear,.select2-container.select2-container--admin-autocomplete .select2-selection--single .select2-selection__clear):where(.dark,.dark *):hover:after{content:var(--tw-content);color:rgb(var(--color-base-200))}}}.select2-container.select2-container--admin-autocomplete .select2-selection--multiple .select2-selection__clear:after,.select2-container.select2-container--admin-autocomplete .select2-selection--single .select2-selection__clear:after{letter-spacing:normal;text-transform:none;white-space:nowrap;word-wrap:normal;-moz-font-feature-settings:"liga";-moz-osx-font-smoothing:grayscale;transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));--tw-content:"close_small";content:var(--tw-content);direction:ltr;font-family:Material Symbols Outlined;font-size:18px;font-style:normal;font-weight:400;line-height:1;display:inline-block}.select2-container.select2-container--admin-autocomplete .select2-selection--single .select2-selection__arrow{margin-top:-1px;margin-right:calc(var(--spacing)*2);height:calc(var(--spacing)*9);align-items:center;display:flex}.select2-container.select2-container--admin-autocomplete .select2-selection--single .select2-selection__arrow:after{content:var(--tw-content);left:calc(var(--spacing)*0);content:var(--tw-content);margin:calc(var(--spacing)*0);content:var(--tw-content);letter-spacing:normal;text-transform:none;white-space:nowrap;word-wrap:normal;-moz-font-feature-settings:"liga";-moz-osx-font-smoothing:grayscale;content:var(--tw-content);font-family:Material Symbols Outlined;font-size:18px;font-style:normal;font-weight:400;line-height:1;font-size:var(--text-lg);line-height:var(--tw-leading,var(--text-lg--line-height));content:var(--tw-content);--tw-leading:1;content:var(--tw-content);color:rgb(var(--color-base-400));direction:ltr;line-height:1;display:inline-block}@media not print{.select2-container.select2-container--admin-autocomplete .select2-selection--single .select2-selection__arrow:where(.dark,.dark *):after{content:var(--tw-content);color:rgb(var(--color-base-500))}}.select2-container.select2-container--admin-autocomplete .select2-selection--single .select2-selection__arrow:after{content:"expand_more"}.select2-container.select2-container--admin-autocomplete .select2-selection--single .select2-selection__arrow b{display:none}.select2-container.select2-container--admin-autocomplete .select2-search--dropdown{border-bottom-style:var(--tw-border-style);border-bottom-width:1px;border-color:rgb(var(--color-base-200));padding-inline:calc(var(--spacing)*1);padding-block:calc(var(--spacing)*1.5);position:relative}.select2-container.select2-container--admin-autocomplete .select2-search--dropdown:before{content:var(--tw-content);content:var(--tw-content);top:calc(var(--spacing)*2.5);content:var(--tw-content);left:calc(var(--spacing)*4);content:var(--tw-content);letter-spacing:normal;text-transform:none;white-space:nowrap;word-wrap:normal;-moz-font-feature-settings:"liga";-moz-osx-font-smoothing:grayscale;content:var(--tw-content);font-family:Material Symbols Outlined;font-size:18px;font-style:normal;font-weight:400;line-height:1;font-size:var(--text-lg);line-height:var(--tw-leading,var(--text-lg--line-height));content:var(--tw-content);color:rgb(var(--color-base-400));content:var(--tw-content);--tw-content:"search";content:var(--tw-content);direction:ltr;display:inline-block;position:absolute}@media not print{.select2-container.select2-container--admin-autocomplete .select2-search--dropdown:where(.dark,.dark *){border-color:rgb(var(--color-base-700))}.select2-container.select2-container--admin-autocomplete .select2-search--dropdown:where(.dark,.dark *):before{content:var(--tw-content);color:rgb(var(--color-base-500))}}.select2-container.select2-container--admin-autocomplete .select2-search--dropdown .select2-search__field{margin-inline:calc(var(--spacing)*0);border-radius:var(--border-radius,6px);border-style:var(--tw-border-style);--tw-border-style:solid;border-style:solid;border-width:0;border-color:rgb(var(--color-base-200));background-color:rgb(var(--color-base-100));width:100%;padding-inline:calc(var(--spacing)*3);padding-block:calc(var(--spacing)*2);padding-left:calc(var(--spacing)*9);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);color:rgb(var(--color-font-default-light));--tw-outline-style:none;outline-style:none;flex-grow:1}@media (forced-colors:active){.select2-container.select2-container--admin-autocomplete .select2-search--dropdown .select2-search__field{outline-offset:2px;outline:2px solid #0000}}@media not print{.select2-container.select2-container--admin-autocomplete .select2-search--dropdown .select2-search__field:where(.dark,.dark *){border-color:rgb(var(--color-base-800));background-color:rgb(var(--color-base-800));color:rgb(var(--color-font-default-dark))}}.select2-container .select2-results__options{padding-block:calc(var(--spacing)*1)}.select2-container.select2-container--open .select2-dropdown{border-radius:var(--border-radius,6px);border-style:var(--tw-border-style);border-width:1px;border-color:rgb(var(--color-base-200));background-color:var(--color-white);--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media not print{.select2-container.select2-container--open .select2-dropdown:where(.dark,.dark *){border-color:rgb(var(--color-base-700));background-color:rgb(var(--color-base-900))}}.select2-container.select2-container--open .select2-dropdown--below{margin-top:calc(var(--spacing)*1)}.select2-container.select2-container--open .select2-dropdown--above{margin-bottom:calc(var(--spacing)*1)}.select2-container.select2-container--admin-autocomplete .select2-results__option{margin-inline:calc(var(--spacing)*1);margin-bottom:calc(var(--spacing)*.5);border-radius:var(--border-radius,6px);padding-inline:calc(var(--spacing)*2);padding-block:calc(var(--spacing)*2);color:rgb(var(--color-font-default-light));transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));display:block}.select2-container.select2-container--admin-autocomplete .select2-results__option:last-child{margin-bottom:calc(var(--spacing)*0)}@media (hover:hover){.select2-container.select2-container--admin-autocomplete .select2-results__option:hover{background-color:rgb(var(--color-base-100));color:rgb(var(--color-base-700))}}@media not print{.select2-container.select2-container--admin-autocomplete .select2-results__option:where(.dark,.dark *){color:rgb(var(--color-font-default-dark))}@media (hover:hover){.select2-container.select2-container--admin-autocomplete .select2-results__option:where(.dark,.dark *):hover{background-color:rgb(var(--color-base-800));color:rgb(var(--color-base-200))}}}.select2-container.select2-container--admin-autocomplete .select2-results__option[aria-selected=true]{color:rgb(var(--color-primary-600));align-items:center;display:flex}.select2-container.select2-container--admin-autocomplete .select2-results__option[aria-selected=true]:after{content:var(--tw-content);content:var(--tw-content);letter-spacing:normal;text-transform:none;white-space:nowrap;word-wrap:normal;-moz-font-feature-settings:"liga";-moz-osx-font-smoothing:grayscale;content:var(--tw-content);--tw-content:"check";content:var(--tw-content);direction:ltr;margin-left:auto;font-family:Material Symbols Outlined;font-size:18px;font-style:normal;font-weight:400;line-height:1;display:inline-block}@media not print{.select2-container.select2-container--admin-autocomplete .select2-results__option[aria-selected=true]:where(.dark,.dark *){color:rgb(var(--color-primary-500))}}.select2-container.select2-container--admin-autocomplete .select2-selection--multiple .select2-selection__clear{top:calc(var(--spacing)*0);margin:calc(var(--spacing)*0);margin-right:calc(var(--spacing)*3)}.select2-container.select2-container--admin-autocomplete .select2-selection--multiple .select2-selection__rendered{padding-block:calc(var(--spacing)*0);padding-top:calc(var(--spacing)*1);padding-right:calc(var(--spacing)*6);padding-left:calc(var(--spacing)*1)}.select2-container.select2-container--admin-autocomplete .select2-selection--single .select2-selection__rendered{padding-right:calc(var(--spacing)*8)}.select2-container--admin-autocomplete .select2-selection--multiple li.select2-selection__choice{margin:calc(var(--spacing)*0);margin-right:calc(var(--spacing)*1);margin-bottom:calc(var(--spacing)*1);height:calc(var(--spacing)*7);text-overflow:ellipsis;white-space:nowrap;background-color:rgb(var(--color-base-100));padding-inline:calc(var(--spacing)*2);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-leading:calc(var(--spacing)*7);line-height:calc(var(--spacing)*7);--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);color:rgb(var(--color-base-600));--tw-shadow:inset 0 2px 4px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);display:block;overflow:hidden}@media (hover:hover){.select2-container--admin-autocomplete .select2-selection--multiple li.select2-selection__choice:hover{color:rgb(var(--color-base-700))}}@media not print{.select2-container--admin-autocomplete .select2-selection--multiple li.select2-selection__choice:where(.dark,.dark *){background-color:rgb(var(--color-base-800));color:rgb(var(--color-base-300))}@media (hover:hover){.select2-container--admin-autocomplete .select2-selection--multiple li.select2-selection__choice:where(.dark,.dark *):hover{color:rgb(var(--color-base-200))}}}.select2-container--admin-autocomplete .select2-selection--multiple li.select2-selection__choice .select2-selection__choice__remove{vertical-align:top;font-size:0}@media (hover:hover){.select2-container--admin-autocomplete .select2-selection--multiple li.select2-selection__choice .select2-selection__choice__remove:hover{color:rgb(var(--color-base-600))}}@media not print{@media (hover:hover){.select2-container--admin-autocomplete .select2-selection--multiple li.select2-selection__choice .select2-selection__choice__remove:where(.dark,.dark *):hover{color:rgb(var(--color-base-200))}}}.select2-container--admin-autocomplete .select2-selection--multiple li.select2-selection__choice .select2-selection__choice__remove:after{letter-spacing:normal;text-transform:none;white-space:nowrap;word-wrap:normal;-moz-font-feature-settings:"liga";-moz-osx-font-smoothing:grayscale;height:calc(var(--spacing)*7);--tw-content:"close_small";content:var(--tw-content);direction:ltr;font-family:Material Symbols Outlined;font-size:14px;font-style:normal;font-weight:400;line-height:1;display:inline-block;--tw-leading:calc(var(--spacing)*7)!important;line-height:calc(var(--spacing)*7)!important}.select2-container--admin-autocomplete .select2-selection--multiple li.select2-search--inline .select2-search__field{margin:calc(var(--spacing)*0);margin-left:calc(var(--spacing)*2);height:calc(var(--spacing)*7);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));color:rgb(var(--color-base-500))}fieldset.collapsed>div{display:none}fieldset.collapse{visibility:visible}fieldset.collapsed h2,fieldset.collapsed{display:block}fieldset.collapsed .collapse-toggle{display:inline}fieldset details>summary{list-style-type:none}fieldset details>summary:after{top:calc(var(--spacing)*3.5);right:calc(var(--spacing)*3);letter-spacing:normal;text-transform:none;white-space:nowrap;word-wrap:normal;-moz-font-feature-settings:"liga";-moz-osx-font-smoothing:grayscale;cursor:pointer;content:"expand_more";direction:ltr;font-family:Material Symbols Outlined;font-size:18px;font-style:normal;font-weight:400;line-height:1;display:inline-block;position:absolute}fieldset details[open]>summary:after{rotate:180deg}.calendarbox,.clockbox{z-index:50;width:calc(var(--spacing)*80);--tw-translate-x:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y);--tw-translate-y:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y);border-radius:var(--border-radius,6px);border-style:var(--tw-border-style);border-width:1px;border-color:rgb(var(--color-base-200));background-color:var(--color-white);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));color:rgb(var(--color-base-500));--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);position:fixed!important;top:50%!important;left:50%!important}@media not print{:is(.calendarbox,.clockbox):where(.dark,.dark *){border-color:rgb(var(--color-base-700));background-color:rgb(var(--color-base-800))}}.calendar caption{margin-bottom:calc(var(--spacing)*3);padding-block:calc(var(--spacing)*3);--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);color:rgb(var(--color-base-700))}@media not print{.calendar caption:where(.dark,.dark *){color:rgb(var(--color-base-200))}}.calendar table{margin-bottom:calc(var(--spacing)*3);width:100%}.calendar table th{text-align:center;font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);color:rgb(var(--color-base-700))}@media not print{.calendar table th:where(.dark,.dark *){color:rgb(var(--color-base-200))}}.calendar table td{height:calc(var(--spacing)*10);width:calc(var(--spacing)*10);padding:calc(var(--spacing)*1);text-align:center}.calendar table td a{height:calc(var(--spacing)*8);width:calc(var(--spacing)*8);transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));border-radius:3.40282e38px;justify-content:center;align-items:center;display:flex}@media not print{.calendar table td a:where(.dark,.dark *){color:rgb(var(--color-base-300))}}.calendar table td a:hover{background-color:rgb(var(--color-base-100));color:rgb(var(--color-base-700))}.calendar table td.today a{background-color:rgb(var(--color-primary-600));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);color:var(--color-white)}.calendar-shortcuts{margin-bottom:calc(var(--spacing)*3);padding-inline:calc(var(--spacing)*1);border-bottom-right-radius:.25rem;border-bottom-left-radius:.25rem;flex-direction:row;justify-content:center;font-size:0;display:flex}.calendar-shortcuts a{margin-inline:calc(var(--spacing)*1);border-radius:var(--border-radius,6px);border-style:var(--tw-border-style);border-width:1px;border-color:rgb(var(--color-base-200));width:33.3333%;padding-inline:calc(var(--spacing)*2);padding-block:calc(var(--spacing)*2);text-align:center;font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height));--tw-leading:1;--tw-font-weight:var(--font-weight-medium);line-height:1;font-weight:var(--font-weight-medium);color:rgb(var(--color-base-500));--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}@media not print{.calendar-shortcuts a:where(.dark,.dark *){border-color:rgb(var(--color-base-700));color:rgb(var(--color-base-300))}@media (hover:hover){.calendar-shortcuts a:where(.dark,.dark *):hover{border-color:rgb(var(--color-base-600));color:rgb(var(--color-base-200))}}}.calendar-cancel{border-top-style:var(--tw-border-style);border-top-width:1px;border-color:rgb(var(--color-base-200));padding-block:calc(var(--spacing)*2);text-align:center;font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height));color:var(--color-red-600);display:block}@media not print{.calendar-cancel:where(.dark,.dark *){border-color:rgb(var(--color-base-700));color:var(--color-red-500)}}.calendarnav-previous{top:calc(var(--spacing)*0);left:calc(var(--spacing)*0);margin-top:calc(var(--spacing)*2);margin-left:calc(var(--spacing)*2);font-size:0;display:block;position:absolute}.calendarnav-next:after,.calendarnav-previous:after{letter-spacing:normal;text-transform:none;white-space:nowrap;word-wrap:normal;-moz-font-feature-settings:"liga";-moz-osx-font-smoothing:grayscale;height:calc(var(--spacing)*7);width:calc(var(--spacing)*7);border-style:var(--tw-border-style);border-width:1px;border-color:rgb(var(--color-base-200));color:rgb(var(--color-base-400));transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));direction:ltr;border-radius:3.40282e38px;justify-content:center;align-items:center;font-family:Material Symbols Outlined;font-size:18px;font-style:normal;font-weight:400;line-height:1;display:flex}@media (hover:hover){:is():hover{border-color:rgb(var(--color-primary-600));color:rgb(var(--color-primary-500))}}@media not print{:is():where(.dark,.dark *){border-color:rgb(var(--color-base-700));background-color:rgb(var(--color-base-800))}@media (hover:hover){:is():where(.dark,.dark *):hover{border-color:rgb(var(--color-base-800))}}}.calendarnav-next:after,.calendarnav-previous:after{content:"navigate_before";display:flex}@media not print{:is():where(.dark,.dark *){border-color:rgb(var(--color-base-600));color:rgb(var(--color-base-200))}}.calendarnav-next{top:calc(var(--spacing)*0);right:calc(var(--spacing)*0);margin-top:calc(var(--spacing)*2);margin-right:calc(var(--spacing)*2);font-size:0;display:block;position:absolute}.calendarnav-next:after{content:"navigate_next";display:flex}.clockbox{z-index:50;border-radius:var(--border-radius,6px);border-style:var(--tw-border-style);border-width:1px;border-color:rgb(var(--color-base-200));background-color:var(--color-white);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media not print{.clockbox:where(.dark,.dark *){border-color:rgb(var(--color-base-700));background-color:rgb(var(--color-base-800))}}.clockbox h2{padding-inline:calc(var(--spacing)*3);padding-block:calc(var(--spacing)*2);--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);color:rgb(var(--color-base-700))}@media not print{.clockbox h2:where(.dark,.dark *){color:rgb(var(--color-base-200))}}.clockbox .timelist{padding-inline:calc(var(--spacing)*3);padding-bottom:calc(var(--spacing)*2);color:rgb(var(--color-base-500))}@media not print{.clockbox .timelist:where(.dark,.dark *){color:rgb(var(--color-base-300))}}.clockbox .timelist li{padding-bottom:calc(var(--spacing)*1);transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));display:block}.clockbox .timelist li:hover{color:rgb(var(--color-primary-500))}.htmx-indicator.htmx-swapping:before{top:calc(var(--spacing)*0);right:calc(var(--spacing)*0);bottom:calc(var(--spacing)*0);left:calc(var(--spacing)*0);background-color:var(--color-white);opacity:.8;transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));content:"";position:absolute}.htmx-indicator.htmx-swapping:after{letter-spacing:normal;text-transform:none;white-space:nowrap;word-wrap:normal;-moz-font-feature-settings:"liga";-moz-osx-font-smoothing:grayscale;height:calc(var(--spacing)*4);width:calc(var(--spacing)*4);animation:var(--animate-spin);color:rgb(var(--color-base-400));content:"sync";direction:ltr;font-family:Material Symbols Outlined;font-size:16px;font-style:normal;font-weight:400;line-height:1;display:inline-block;position:absolute;inset:50%}#changelist-filter .admin-numeric-filter-slider .noUi-handle{right:calc(var(--spacing)*-4);height:calc(var(--spacing)*4);width:calc(var(--spacing)*4);cursor:pointer;border-style:var(--tw-border-style);border-width:1px;border-color:rgb(var(--color-base-200));background-color:var(--color-white);--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);border-radius:3.40282e38px}@media not print{#changelist-filter .admin-numeric-filter-slider .noUi-handle:where(.dark,.dark *){border-color:rgb(var(--color-base-200));background-color:rgb(var(--color-base-200))}}#changelist-filter .admin-numeric-filter-slider .noUi-handle-upper{right:calc(var(--spacing)*0)}#changelist-filter .admin-numeric-filter-slider .noUi-handle:after,#changelist-filter .admin-numeric-filter-slider .noUi-handle:before{content:none}#changelist-filter .admin-numeric-filter-slider.noUi-target{height:calc(var(--spacing)*1);border-style:var(--tw-border-style);background-color:rgb(var(--color-base-200));--tw-shadow:0 0 #0000;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);border-width:0}@media not print{#changelist-filter .admin-numeric-filter-slider.noUi-target:where(.dark,.dark *){background-color:rgb(var(--color-base-600))}}#changelist-filter .admin-numeric-filter-slider .noUi-connect{height:calc(var(--spacing)*1);border-style:var(--tw-border-style);background-color:rgb(var(--color-primary-600));border-width:0}#changelist-filter .admin-numeric-filter-slider-tooltips{margin-bottom:calc(var(--spacing)*5);flex-direction:row;display:flex}:where(#changelist-filter .admin-numeric-filter-slider-tooltips>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*4)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-x-reverse)))}#changelist-filter .admin-numeric-filter-slider-tooltips{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);color:rgb(var(--color-base-500))}trix-toolbar[id^=trix-toolbar-]{top:calc(var(--spacing)*0);position:sticky}@media not print{trix-toolbar[id^=trix-toolbar-]:where(.dark,.dark *){background-color:rgb(var(--color-base-900))}}.trix-active{color:rgb(var(--color-primary-600))}.ui-tabs{flex-direction:column;display:flex}.ui-tabs.ui-widget .ui-tabs-nav{margin:calc(var(--spacing)*0);margin-top:calc(var(--spacing)*3);margin-right:auto;margin-bottom:calc(var(--spacing)*0);margin-left:calc(var(--spacing)*3);gap:calc(var(--spacing)*2);border-radius:var(--border-radius,6px);border-style:var(--tw-border-style);background-color:rgb(var(--color-base-100));padding:calc(var(--spacing)*1);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));border-width:0;display:flex}.ui-tabs.ui-widget .ui-tabs-nav:after{content:var(--tw-content);display:none}@media not print{.ui-tabs.ui-widget .ui-tabs-nav:where(.dark,.dark *){background-color:#ffffff0a}@supports (color:color-mix(in lab, red, red)){.ui-tabs.ui-widget .ui-tabs-nav:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-white)4%,transparent)}}}.ui-tabs.ui-widget .ui-tabs-nav li{top:calc(var(--spacing)*0);border-style:var(--tw-border-style);border-width:0}.ui-tabs.ui-widget .ui-tabs-nav li a{margin:calc(var(--spacing)*0);border-radius:var(--border-radius,6px);border-style:var(--tw-border-style);padding-inline:calc(var(--spacing)*2.5);padding-block:calc(var(--spacing)*1);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);color:rgb(var(--color-base-400));background-color:#0000;border-width:0;flex-direction:row;align-items:center;display:flex}@media (hover:hover){.ui-tabs.ui-widget .ui-tabs-nav li a:hover{background-color:color-mix(in srgb,rgb(rgb(var(--color-base-700)))4%,transparent)}@supports (color:color-mix(in lab, red, red)){.ui-tabs.ui-widget .ui-tabs-nav li a:hover{background-color:color-mix(in oklab,rgb(var(--color-base-700))4%,transparent)}}.ui-tabs.ui-widget .ui-tabs-nav li a:hover{color:rgb(var(--color-base-700))}}@media not print{@media (hover:hover){.ui-tabs.ui-widget .ui-tabs-nav li a:where(.dark,.dark *):hover{background-color:#ffffff0a}@supports (color:color-mix(in lab, red, red)){.ui-tabs.ui-widget .ui-tabs-nav li a:where(.dark,.dark *):hover{background-color:color-mix(in oklab,var(--color-white)4%,transparent)}}.ui-tabs.ui-widget .ui-tabs-nav li a:where(.dark,.dark *):hover{color:var(--color-white)}}}.ui-tabs.ui-widget .ui-tabs-nav li.ui-tabs-active a{border-radius:var(--border-radius,6px);background-color:var(--color-white);padding-inline:calc(var(--spacing)*2.5);padding-block:calc(var(--spacing)*1);--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);color:rgb(var(--color-base-700));--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);flex-direction:row;align-items:center;display:flex}@media (hover:hover){.ui-tabs.ui-widget .ui-tabs-nav li.ui-tabs-active a:hover{background-color:var(--color-white)}}@media not print{.ui-tabs.ui-widget .ui-tabs-nav li.ui-tabs-active a:where(.dark,.dark *){background-color:rgb(var(--color-base-900));color:var(--color-white)}@media (hover:hover){.ui-tabs.ui-widget .ui-tabs-nav li.ui-tabs-active a:where(.dark,.dark *):hover{background-color:rgb(var(--color-base-900))}}}.simplebar-track .simplebar-scrollbar:before{background-color:color-mix(in srgb,rgb(rgb(var(--color-base-900)))80%,transparent)}@supports (color:color-mix(in lab, red, red)){.simplebar-track .simplebar-scrollbar:before{background-color:color-mix(in oklab,rgb(var(--color-base-900))80%,transparent)}}@media not print{.simplebar-track .simplebar-scrollbar:before:where(){background-color:color-mix(in srgb,rgb(rgb(var(--color-base-400)))80%,transparent)}@supports (color:color-mix(in lab, red, red)){.simplebar-track .simplebar-scrollbar:before:where(){background-color:color-mix(in oklab,rgb(var(--color-base-400))80%,transparent)}}}.simplebar-horizontal-scrollbar-top .simplebar-track.simplebar-horizontal{top:calc(var(--spacing)*9)}@property --tw-translate-x{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-y{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-z{syntax:"*";inherits:false;initial-value:0}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-space-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-leading{syntax:"*";inherits:false}@property --tw-font-weight{syntax:"*";inherits:false}@property --tw-tracking{syntax:"*";inherits:false}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-outline-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-blur{syntax:"*";inherits:false}@property --tw-brightness{syntax:"*";inherits:false}@property --tw-contrast{syntax:"*";inherits:false}@property --tw-grayscale{syntax:"*";inherits:false}@property --tw-hue-rotate{syntax:"*";inherits:false}@property --tw-invert{syntax:"*";inherits:false}@property --tw-opacity{syntax:"*";inherits:false}@property --tw-saturate{syntax:"*";inherits:false}@property --tw-sepia{syntax:"*";inherits:false}@property --tw-drop-shadow{syntax:"*";inherits:false}@property --tw-drop-shadow-color{syntax:"*";inherits:false}@property --tw-drop-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:"*";inherits:false}@property --tw-backdrop-blur{syntax:"*";inherits:false}@property --tw-backdrop-brightness{syntax:"*";inherits:false}@property --tw-backdrop-contrast{syntax:"*";inherits:false}@property --tw-backdrop-grayscale{syntax:"*";inherits:false}@property --tw-backdrop-hue-rotate{syntax:"*";inherits:false}@property --tw-backdrop-invert{syntax:"*";inherits:false}@property --tw-backdrop-opacity{syntax:"*";inherits:false}@property --tw-backdrop-saturate{syntax:"*";inherits:false}@property --tw-backdrop-sepia{syntax:"*";inherits:false}@property --tw-duration{syntax:"*";inherits:false}@property --tw-ease{syntax:"*";inherits:false}@property --tw-content{syntax:"*";inherits:false;initial-value:""}@property --tw-gradient-position{syntax:"*";inherits:false}@property --tw-gradient-from{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-via{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-to{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-stops{syntax:"*";inherits:false}@property --tw-gradient-via-stops{syntax:"*";inherits:false}@property --tw-gradient-from-position{syntax:"<length-percentage>";inherits:false;initial-value:0%}@property --tw-gradient-via-position{syntax:"<length-percentage>";inherits:false;initial-value:50%}@property --tw-gradient-to-position{syntax:"<length-percentage>";inherits:false;initial-value:100%}@property --tw-space-x-reverse{syntax:"*";inherits:false;initial-value:0}@keyframes spin{to{transform:rotate(360deg)}}
