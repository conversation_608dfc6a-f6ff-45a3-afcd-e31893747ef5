from rest_framework import generics, viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Count, Avg, Q
from django.utils import timezone
from datetime import datetime

from .models import (
    InsuranceCompany, ProductCategory, InsuranceProduct,
    ProductReview, ProductInquiry, Banner
)
from .serializers import (
    InsuranceCompanySerializer, ProductCategorySerializer,
    InsuranceProductListSerializer, InsuranceProductDetailSerializer,
    ProductReviewSerializer, ProductInquirySerializer, BannerSerializer,
    ProductStatisticsSerializer, HomePageDataSerializer
)


class InsuranceCompanyViewSet(viewsets.ReadOnlyModelViewSet):
    """保险公司视图集"""
    queryset = InsuranceCompany.objects.filter(is_active=True)
    serializer_class = InsuranceCompanySerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['established_year']
    search_fields = ['name', 'name_en', 'description']
    ordering_fields = ['sort_order', 'name', 'established_year']
    ordering = ['sort_order', 'name']


class ProductCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    """产品分类视图集"""
    queryset = ProductCategory.objects.filter(is_active=True)
    serializer_class = ProductCategorySerializer
    ordering = ['sort_order', 'name']


class InsuranceProductViewSet(viewsets.ReadOnlyModelViewSet):
    """保险产品视图集"""
    queryset = InsuranceProduct.objects.filter(is_active=True).select_related('company', 'category')
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['company', 'category', 'currency', 'is_featured', 'is_hot']
    search_fields = ['name', 'name_en', 'description']
    ordering_fields = ['sort_order', 'name', 'min_premium', 'expected_return', 'view_count', 'created_at']
    ordering = ['-is_featured', '-is_hot', 'sort_order', '-created_at']

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'retrieve':
            return InsuranceProductDetailSerializer
        return InsuranceProductListSerializer

    def retrieve(self, request, *args, **kwargs):
        """获取产品详情时增加浏览次数"""
        instance = self.get_object()
        instance.increment_view_count()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """获取推荐产品"""
        queryset = self.get_queryset().filter(is_featured=True)[:10]
        serializer = InsuranceProductListSerializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def hot(self, request):
        """获取热销产品"""
        queryset = self.get_queryset().filter(is_hot=True)[:10]
        serializer = InsuranceProductListSerializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def reviews(self, request, pk=None):
        """获取产品评价"""
        product = self.get_object()
        reviews = product.reviews.filter(is_approved=True).order_by('-created_at')

        # 分页
        page = self.paginate_queryset(reviews)
        if page is not None:
            serializer = ProductReviewSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ProductReviewSerializer(reviews, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticatedOrReadOnly])
    def add_review(self, request, pk=None):
        """添加产品评价"""
        product = self.get_object()
        serializer = ProductReviewSerializer(data=request.data, context={'request': request})

        if serializer.is_valid():
            serializer.save(user=request.user, product=product)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticatedOrReadOnly])
    def inquire(self, request, pk=None):
        """产品咨询"""
        product = self.get_object()
        serializer = ProductInquirySerializer(data=request.data, context={'request': request})

        if serializer.is_valid():
            serializer.save(user=request.user, product=product)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class BannerViewSet(viewsets.ReadOnlyModelViewSet):
    """轮播图视图集"""
    queryset = Banner.objects.all()
    serializer_class = BannerSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['position']
    ordering = ['position', 'sort_order', '-created_at']

    def get_queryset(self):
        """获取有效的轮播图"""
        now = timezone.now()
        return Banner.objects.filter(
            is_active=True
        ).filter(
            Q(start_time__isnull=True) | Q(start_time__lte=now)
        ).filter(
            Q(end_time__isnull=True) | Q(end_time__gte=now)
        )


class HomePageAPIView(generics.GenericAPIView):
    """首页数据API"""

    def get(self, request):
        """获取首页数据"""
        # 轮播图
        banners = Banner.objects.filter(
            is_active=True,
            position='home'
        ).order_by('sort_order')[:5]

        # 产品分类
        categories = ProductCategory.objects.filter(is_active=True).order_by('sort_order')[:4]

        # 推荐产品
        featured_products = InsuranceProduct.objects.filter(
            is_active=True,
            is_featured=True
        ).select_related('company', 'category').order_by('sort_order')[:6]

        # 热销产品
        hot_products = InsuranceProduct.objects.filter(
            is_active=True,
            is_hot=True
        ).select_related('company', 'category').order_by('sort_order')[:6]

        # 统计数据
        statistics = self.get_statistics()

        data = {
            'banners': BannerSerializer(banners, many=True).data,
            'categories': ProductCategorySerializer(categories, many=True).data,
            'featured_products': InsuranceProductListSerializer(featured_products, many=True).data,
            'hot_products': InsuranceProductListSerializer(hot_products, many=True).data,
            'statistics': statistics
        }

        serializer = HomePageDataSerializer(data)
        return Response(serializer.data)

    def get_statistics(self):
        """获取统计数据"""
        products = InsuranceProduct.objects.filter(is_active=True)

        return {
            'total_products': products.count(),
            'total_companies': InsuranceCompany.objects.filter(is_active=True).count(),
            'total_categories': ProductCategory.objects.filter(is_active=True).count(),
            'featured_products': products.filter(is_featured=True).count(),
            'hot_products': products.filter(is_hot=True).count(),
            'currency_distribution': dict(
                products.values('currency').annotate(count=Count('id')).values_list('currency', 'count')
            ),
            'category_distribution': dict(
                products.values('category__name').annotate(count=Count('id')).values_list('category__name', 'count')
            ),
            'company_distribution': dict(
                products.values('company__name').annotate(count=Count('id')).values_list('company__name', 'count')
            )
        }
