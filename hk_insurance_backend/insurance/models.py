from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
import uuid


class InsuranceCompany(models.Model):
    """保险公司模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, verbose_name="公司名称")
    name_en = models.CharField(max_length=100, verbose_name="英文名称", blank=True)
    logo = models.ImageField(upload_to='company_logos/', verbose_name="公司Logo", blank=True)
    description = models.TextField(verbose_name="公司简介", blank=True)
    website = models.URLField(verbose_name="官方网站", blank=True)
    phone = models.CharField(max_length=20, verbose_name="联系电话", blank=True)
    email = models.EmailField(verbose_name="联系邮箱", blank=True)
    address = models.TextField(verbose_name="公司地址", blank=True)
    established_year = models.PositiveIntegerField(verbose_name="成立年份", blank=True, null=True)
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    sort_order = models.PositiveIntegerField(default=0, verbose_name="排序")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "保险公司"
        verbose_name_plural = "保险公司"
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name


class ProductCategory(models.Model):
    """产品分类模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=50, verbose_name="分类名称")
    name_en = models.CharField(max_length=50, verbose_name="英文名称", blank=True)
    icon = models.CharField(max_length=50, verbose_name="图标", blank=True)
    description = models.TextField(verbose_name="分类描述", blank=True)
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    sort_order = models.PositiveIntegerField(default=0, verbose_name="排序")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "产品分类"
        verbose_name_plural = "产品分类"
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name


class InsuranceProduct(models.Model):
    """保险产品模型"""
    CURRENCY_CHOICES = [
        ('USD', '美元'),
        ('HKD', '港币'),
        ('CNY', '人民币'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    company = models.ForeignKey(InsuranceCompany, on_delete=models.CASCADE, verbose_name="保险公司")
    category = models.ForeignKey(ProductCategory, on_delete=models.CASCADE, verbose_name="产品分类")
    name = models.CharField(max_length=200, verbose_name="产品名称")
    name_en = models.CharField(max_length=200, verbose_name="英文名称", blank=True)
    subtitle = models.CharField(max_length=300, verbose_name="产品副标题", blank=True)
    description = models.TextField(verbose_name="产品描述")

    # 财务信息
    currency = models.CharField(max_length=3, choices=CURRENCY_CHOICES, default='USD', verbose_name="货币")
    min_premium = models.DecimalField(max_digits=12, decimal_places=2, verbose_name="最低保费", null=True, blank=True)
    max_premium = models.DecimalField(max_digits=12, decimal_places=2, verbose_name="最高保费", null=True, blank=True)
    expected_return = models.DecimalField(
        max_digits=5, decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name="预期年化收益率(%)", null=True, blank=True
    )

    # 产品特色
    features = models.JSONField(default=list, verbose_name="产品特色", blank=True)
    payment_periods = models.JSONField(default=list, verbose_name="缴费期选择", blank=True)

    # 保障信息
    coverage_amount = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="保障金额", null=True, blank=True)
    coverage_details = models.JSONField(default=dict, verbose_name="保障详情", blank=True)

    # 媒体文件
    main_image = models.ImageField(upload_to='product_images/', verbose_name="主图", blank=True)
    brochure = models.FileField(upload_to='product_brochures/', verbose_name="产品手册", blank=True)

    # 状态和排序
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    is_featured = models.BooleanField(default=False, verbose_name="是否推荐")
    is_hot = models.BooleanField(default=False, verbose_name="是否热销")
    sort_order = models.PositiveIntegerField(default=0, verbose_name="排序")

    # 统计信息
    view_count = models.PositiveIntegerField(default=0, verbose_name="浏览次数")
    inquiry_count = models.PositiveIntegerField(default=0, verbose_name="咨询次数")

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "保险产品"
        verbose_name_plural = "保险产品"
        ordering = ['-is_featured', '-is_hot', 'sort_order', '-created_at']

    def __str__(self):
        return f"{self.company.name} - {self.name}"

    def increment_view_count(self):
        """增加浏览次数"""
        self.view_count += 1
        self.save(update_fields=['view_count'])

    def increment_inquiry_count(self):
        """增加咨询次数"""
        self.inquiry_count += 1
        self.save(update_fields=['inquiry_count'])


class ProductReview(models.Model):
    """产品评价模型"""
    RATING_CHOICES = [
        (1, '1星'),
        (2, '2星'),
        (3, '3星'),
        (4, '4星'),
        (5, '5星'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    product = models.ForeignKey(InsuranceProduct, on_delete=models.CASCADE, related_name='reviews', verbose_name="产品")
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="用户")
    rating = models.PositiveIntegerField(choices=RATING_CHOICES, verbose_name="评分")
    content = models.TextField(verbose_name="评价内容")
    is_anonymous = models.BooleanField(default=False, verbose_name="匿名评价")
    is_approved = models.BooleanField(default=False, verbose_name="是否审核通过")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "产品评价"
        verbose_name_plural = "产品评价"
        ordering = ['-created_at']
        unique_together = ['product', 'user']

    def __str__(self):
        return f"{self.product.name} - {self.rating}星"


class ProductInquiry(models.Model):
    """产品咨询模型"""
    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('cancelled', '已取消'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    product = models.ForeignKey(InsuranceProduct, on_delete=models.CASCADE, related_name='inquiries', verbose_name="产品")
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="用户")

    # 联系信息
    name = models.CharField(max_length=50, verbose_name="姓名")
    phone = models.CharField(max_length=20, verbose_name="手机号")
    email = models.EmailField(verbose_name="邮箱", blank=True)

    # 咨询信息
    age = models.PositiveIntegerField(verbose_name="年龄", null=True, blank=True)
    annual_income = models.DecimalField(max_digits=12, decimal_places=2, verbose_name="年收入", null=True, blank=True)
    message = models.TextField(verbose_name="咨询内容", blank=True)

    # 状态
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="状态")
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='assigned_inquiries', verbose_name="分配给")

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "产品咨询"
        verbose_name_plural = "产品咨询"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.product.name} - {self.name}"


class Banner(models.Model):
    """轮播图模型"""
    POSITION_CHOICES = [
        ('home', '首页'),
        ('product', '产品页'),
        ('profile', '个人中心'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=100, verbose_name="标题")
    subtitle = models.CharField(max_length=200, verbose_name="副标题", blank=True)
    image = models.ImageField(upload_to='banners/', verbose_name="图片")
    link_type = models.CharField(max_length=20, choices=[
        ('product', '产品详情'),
        ('category', '产品分类'),
        ('url', '外部链接'),
        ('none', '无链接'),
    ], default='none', verbose_name="链接类型")
    link_value = models.CharField(max_length=200, verbose_name="链接值", blank=True)
    position = models.CharField(max_length=20, choices=POSITION_CHOICES, default='home', verbose_name="显示位置")
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    sort_order = models.PositiveIntegerField(default=0, verbose_name="排序")
    start_time = models.DateTimeField(verbose_name="开始时间", null=True, blank=True)
    end_time = models.DateTimeField(verbose_name="结束时间", null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "轮播图"
        verbose_name_plural = "轮播图"
        ordering = ['position', 'sort_order', '-created_at']

    def __str__(self):
        return self.title
