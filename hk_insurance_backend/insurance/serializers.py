from rest_framework import serializers
from .models import (
    InsuranceCompany, ProductCategory, InsuranceProduct, 
    ProductReview, ProductInquiry, Banner
)


class InsuranceCompanySerializer(serializers.ModelSerializer):
    """保险公司序列化器"""
    
    class Meta:
        model = InsuranceCompany
        fields = [
            'id', 'name', 'name_en', 'logo', 'description', 
            'website', 'phone', 'email', 'address', 
            'established_year', 'is_active', 'sort_order',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class ProductCategorySerializer(serializers.ModelSerializer):
    """产品分类序列化器"""
    
    class Meta:
        model = ProductCategory
        fields = [
            'id', 'name', 'name_en', 'icon', 'description',
            'is_active', 'sort_order', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class InsuranceProductListSerializer(serializers.ModelSerializer):
    """保险产品列表序列化器"""
    company_name = serializers.CharField(source='company.name', read_only=True)
    company_logo = serializers.ImageField(source='company.logo', read_only=True)
    category_name = serializers.CharField(source='category.name', read_only=True)
    category_icon = serializers.CharField(source='category.icon', read_only=True)
    
    class Meta:
        model = InsuranceProduct
        fields = [
            'id', 'name', 'subtitle', 'main_image',
            'company_name', 'company_logo', 'category_name', 'category_icon',
            'currency', 'min_premium', 'expected_return',
            'features', 'is_featured', 'is_hot',
            'view_count', 'created_at'
        ]


class InsuranceProductDetailSerializer(serializers.ModelSerializer):
    """保险产品详情序列化器"""
    company = InsuranceCompanySerializer(read_only=True)
    category = ProductCategorySerializer(read_only=True)
    reviews_count = serializers.SerializerMethodField()
    average_rating = serializers.SerializerMethodField()
    
    class Meta:
        model = InsuranceProduct
        fields = [
            'id', 'company', 'category', 'name', 'name_en', 'subtitle', 'description',
            'currency', 'min_premium', 'max_premium', 'expected_return',
            'features', 'payment_periods', 'coverage_amount', 'coverage_details',
            'main_image', 'brochure', 'is_featured', 'is_hot',
            'view_count', 'inquiry_count', 'reviews_count', 'average_rating',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'view_count', 'inquiry_count', 'created_at', 'updated_at']
    
    def get_reviews_count(self, obj):
        """获取评价数量"""
        return obj.reviews.filter(is_approved=True).count()
    
    def get_average_rating(self, obj):
        """获取平均评分"""
        reviews = obj.reviews.filter(is_approved=True)
        if reviews.exists():
            return round(sum(review.rating for review in reviews) / reviews.count(), 1)
        return 0


class ProductReviewSerializer(serializers.ModelSerializer):
    """产品评价序列化器"""
    user_nickname = serializers.SerializerMethodField()
    
    class Meta:
        model = ProductReview
        fields = [
            'id', 'product', 'user', 'user_nickname', 'rating', 'content',
            'is_anonymous', 'created_at'
        ]
        read_only_fields = ['id', 'user', 'created_at']
    
    def get_user_nickname(self, obj):
        """获取用户昵称"""
        if obj.is_anonymous:
            return f"{obj.user.username[:1]}***"
        return getattr(obj.user.profile, 'nickname', obj.user.username) if hasattr(obj.user, 'profile') else obj.user.username


class ProductInquirySerializer(serializers.ModelSerializer):
    """产品咨询序列化器"""
    product_name = serializers.CharField(source='product.name', read_only=True)
    
    class Meta:
        model = ProductInquiry
        fields = [
            'id', 'product', 'product_name', 'name', 'phone', 'email',
            'age', 'annual_income', 'message', 'status', 'created_at'
        ]
        read_only_fields = ['id', 'user', 'status', 'assigned_to', 'created_at', 'updated_at']
    
    def create(self, validated_data):
        """创建咨询记录"""
        validated_data['user'] = self.context['request'].user
        inquiry = super().create(validated_data)
        # 增加产品咨询次数
        inquiry.product.increment_inquiry_count()
        return inquiry


class BannerSerializer(serializers.ModelSerializer):
    """轮播图序列化器"""
    
    class Meta:
        model = Banner
        fields = [
            'id', 'title', 'subtitle', 'image', 'link_type', 'link_value',
            'position', 'sort_order', 'start_time', 'end_time'
        ]
        read_only_fields = ['id']


class ProductStatisticsSerializer(serializers.Serializer):
    """产品统计序列化器"""
    total_products = serializers.IntegerField()
    total_companies = serializers.IntegerField()
    total_categories = serializers.IntegerField()
    featured_products = serializers.IntegerField()
    hot_products = serializers.IntegerField()
    currency_distribution = serializers.DictField()
    category_distribution = serializers.DictField()
    company_distribution = serializers.DictField()


class HomePageDataSerializer(serializers.Serializer):
    """首页数据序列化器"""
    banners = BannerSerializer(many=True)
    categories = ProductCategorySerializer(many=True)
    featured_products = InsuranceProductListSerializer(many=True)
    hot_products = InsuranceProductListSerializer(many=True)
    statistics = ProductStatisticsSerializer()
