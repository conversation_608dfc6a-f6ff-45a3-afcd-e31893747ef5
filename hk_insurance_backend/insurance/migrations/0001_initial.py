# Generated by Django 5.2.4 on 2025-07-22 15:09

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Banner',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=100, verbose_name='标题')),
                ('subtitle', models.CharField(blank=True, max_length=200, verbose_name='副标题')),
                ('image', models.ImageField(upload_to='banners/', verbose_name='图片')),
                ('link_type', models.CharField(choices=[('product', '产品详情'), ('category', '产品分类'), ('url', '外部链接'), ('none', '无链接')], default='none', max_length=20, verbose_name='链接类型')),
                ('link_value', models.CharField(blank=True, max_length=200, verbose_name='链接值')),
                ('position', models.CharField(choices=[('home', '首页'), ('product', '产品页'), ('profile', '个人中心')], default='home', max_length=20, verbose_name='显示位置')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='排序')),
                ('start_time', models.DateTimeField(blank=True, null=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '轮播图',
                'verbose_name_plural': '轮播图',
                'ordering': ['position', 'sort_order', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InsuranceCompany',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, verbose_name='公司名称')),
                ('name_en', models.CharField(blank=True, max_length=100, verbose_name='英文名称')),
                ('logo', models.ImageField(blank=True, upload_to='company_logos/', verbose_name='公司Logo')),
                ('description', models.TextField(blank=True, verbose_name='公司简介')),
                ('website', models.URLField(blank=True, verbose_name='官方网站')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='联系电话')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='联系邮箱')),
                ('address', models.TextField(blank=True, verbose_name='公司地址')),
                ('established_year', models.PositiveIntegerField(blank=True, null=True, verbose_name='成立年份')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='排序')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '保险公司',
                'verbose_name_plural': '保险公司',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ProductCategory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=50, verbose_name='分类名称')),
                ('name_en', models.CharField(blank=True, max_length=50, verbose_name='英文名称')),
                ('icon', models.CharField(blank=True, max_length=50, verbose_name='图标')),
                ('description', models.TextField(blank=True, verbose_name='分类描述')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='排序')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '产品分类',
                'verbose_name_plural': '产品分类',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='InsuranceProduct',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='产品名称')),
                ('name_en', models.CharField(blank=True, max_length=200, verbose_name='英文名称')),
                ('subtitle', models.CharField(blank=True, max_length=300, verbose_name='产品副标题')),
                ('description', models.TextField(verbose_name='产品描述')),
                ('currency', models.CharField(choices=[('USD', '美元'), ('HKD', '港币'), ('CNY', '人民币')], default='USD', max_length=3, verbose_name='货币')),
                ('min_premium', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='最低保费')),
                ('max_premium', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='最高保费')),
                ('expected_return', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='预期年化收益率(%)')),
                ('features', models.JSONField(blank=True, default=list, verbose_name='产品特色')),
                ('payment_periods', models.JSONField(blank=True, default=list, verbose_name='缴费期选择')),
                ('coverage_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='保障金额')),
                ('coverage_details', models.JSONField(blank=True, default=dict, verbose_name='保障详情')),
                ('main_image', models.ImageField(blank=True, upload_to='product_images/', verbose_name='主图')),
                ('brochure', models.FileField(blank=True, upload_to='product_brochures/', verbose_name='产品手册')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('is_featured', models.BooleanField(default=False, verbose_name='是否推荐')),
                ('is_hot', models.BooleanField(default=False, verbose_name='是否热销')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='排序')),
                ('view_count', models.PositiveIntegerField(default=0, verbose_name='浏览次数')),
                ('inquiry_count', models.PositiveIntegerField(default=0, verbose_name='咨询次数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='insurance.insurancecompany', verbose_name='保险公司')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='insurance.productcategory', verbose_name='产品分类')),
            ],
            options={
                'verbose_name': '保险产品',
                'verbose_name_plural': '保险产品',
                'ordering': ['-is_featured', '-is_hot', 'sort_order', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductInquiry',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=50, verbose_name='姓名')),
                ('phone', models.CharField(max_length=20, verbose_name='手机号')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='邮箱')),
                ('age', models.PositiveIntegerField(blank=True, null=True, verbose_name='年龄')),
                ('annual_income', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='年收入')),
                ('message', models.TextField(blank=True, verbose_name='咨询内容')),
                ('status', models.CharField(choices=[('pending', '待处理'), ('processing', '处理中'), ('completed', '已完成'), ('cancelled', '已取消')], default='pending', max_length=20, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_inquiries', to=settings.AUTH_USER_MODEL, verbose_name='分配给')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inquiries', to='insurance.insuranceproduct', verbose_name='产品')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '产品咨询',
                'verbose_name_plural': '产品咨询',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductReview',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('rating', models.PositiveIntegerField(choices=[(1, '1星'), (2, '2星'), (3, '3星'), (4, '4星'), (5, '5星')], verbose_name='评分')),
                ('content', models.TextField(verbose_name='评价内容')),
                ('is_anonymous', models.BooleanField(default=False, verbose_name='匿名评价')),
                ('is_approved', models.BooleanField(default=False, verbose_name='是否审核通过')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='insurance.insuranceproduct', verbose_name='产品')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '产品评价',
                'verbose_name_plural': '产品评价',
                'ordering': ['-created_at'],
                'unique_together': {('product', 'user')},
            },
        ),
    ]
