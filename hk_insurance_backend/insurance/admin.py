from django.contrib import admin
from unfold.admin import ModelAdmin
from .models import (
    InsuranceCompany, ProductCategory, InsuranceProduct,
    ProductReview, ProductInquiry, Banner
)


@admin.register(InsuranceCompany)
class InsuranceCompanyAdmin(ModelAdmin):
    list_display = ['name', 'name_en', 'established_year', 'is_active', 'sort_order', 'created_at']
    list_filter = ['is_active', 'established_year', 'created_at']
    search_fields = ['name', 'name_en', 'description']
    list_editable = ['is_active', 'sort_order']
    ordering = ['sort_order', 'name']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = [
        ('基本信息', {
            'fields': ['name', 'name_en', 'logo', 'description']
        }),
        ('联系信息', {
            'fields': ['website', 'phone', 'email', 'address']
        }),
        ('其他信息', {
            'fields': ['established_year', 'is_active', 'sort_order']
        }),
        ('时间信息', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]


@admin.register(ProductCategory)
class ProductCategoryAdmin(ModelAdmin):
    list_display = ['name', 'name_en', 'icon', 'is_active', 'sort_order', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'name_en', 'description']
    list_editable = ['is_active', 'sort_order']
    ordering = ['sort_order', 'name']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(InsuranceProduct)
class InsuranceProductAdmin(ModelAdmin):
    list_display = ['name', 'company', 'category', 'currency', 'min_premium', 'expected_return',
                   'is_active', 'is_featured', 'is_hot', 'sort_order', 'view_count', 'created_at']
    list_filter = ['company', 'category', 'currency', 'is_active', 'is_featured', 'is_hot', 'created_at']
    search_fields = ['name', 'name_en', 'description']
    list_editable = ['is_active', 'is_featured', 'is_hot', 'sort_order']
    ordering = ['-is_featured', '-is_hot', 'sort_order', '-created_at']
    readonly_fields = ['view_count', 'inquiry_count', 'created_at', 'updated_at']

    fieldsets = [
        ('基本信息', {
            'fields': ['company', 'category', 'name', 'name_en', 'subtitle', 'description']
        }),
        ('财务信息', {
            'fields': ['currency', 'min_premium', 'max_premium', 'expected_return']
        }),
        ('产品特色', {
            'fields': ['features', 'payment_periods']
        }),
        ('保障信息', {
            'fields': ['coverage_amount', 'coverage_details']
        }),
        ('媒体文件', {
            'fields': ['main_image', 'brochure']
        }),
        ('状态设置', {
            'fields': ['is_active', 'is_featured', 'is_hot', 'sort_order']
        }),
        ('统计信息', {
            'fields': ['view_count', 'inquiry_count'],
            'classes': ['collapse']
        }),
        ('时间信息', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]


@admin.register(ProductReview)
class ProductReviewAdmin(ModelAdmin):
    list_display = ['product', 'user', 'rating', 'is_anonymous', 'is_approved', 'created_at']
    list_filter = ['rating', 'is_anonymous', 'is_approved', 'created_at']
    search_fields = ['product__name', 'user__username', 'content']
    list_editable = ['is_approved']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(ProductInquiry)
class ProductInquiryAdmin(ModelAdmin):
    list_display = ['product', 'name', 'phone', 'status', 'assigned_to', 'created_at']
    list_filter = ['status', 'assigned_to', 'created_at']
    search_fields = ['product__name', 'name', 'phone', 'email']
    list_editable = ['status', 'assigned_to']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = [
        ('产品信息', {
            'fields': ['product']
        }),
        ('联系信息', {
            'fields': ['name', 'phone', 'email']
        }),
        ('咨询信息', {
            'fields': ['age', 'annual_income', 'message']
        }),
        ('处理状态', {
            'fields': ['status', 'assigned_to']
        }),
        ('时间信息', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]


@admin.register(Banner)
class BannerAdmin(ModelAdmin):
    list_display = ['title', 'position', 'link_type', 'is_active', 'sort_order', 'start_time', 'end_time']
    list_filter = ['position', 'link_type', 'is_active', 'created_at']
    search_fields = ['title', 'subtitle']
    list_editable = ['is_active', 'sort_order']
    ordering = ['position', 'sort_order', '-created_at']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = [
        ('基本信息', {
            'fields': ['title', 'subtitle', 'image']
        }),
        ('链接设置', {
            'fields': ['link_type', 'link_value']
        }),
        ('显示设置', {
            'fields': ['position', 'is_active', 'sort_order']
        }),
        ('时间设置', {
            'fields': ['start_time', 'end_time']
        }),
        ('时间信息', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
