from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# 创建路由器
router = DefaultRouter()
router.register(r'companies', views.InsuranceCompanyViewSet)
router.register(r'categories', views.ProductCategoryViewSet)
router.register(r'products', views.InsuranceProductViewSet)
router.register(r'banners', views.BannerViewSet)

urlpatterns = [
    # API路由
    path('api/', include(router.urls)),
    
    # 自定义API端点
    path('api/home/', views.HomePageAPIView.as_view(), name='home-page'),
    path('api/statistics/', views.HomePageAPIView.as_view(), name='product-statistics'),
]
