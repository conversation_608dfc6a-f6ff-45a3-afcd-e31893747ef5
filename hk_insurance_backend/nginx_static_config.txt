# 在宝塔面板中添加以下配置到站点设置 -> 配置文件中
# 在 location / { 之前添加：

    # 静态文件配置
    location /static/ {
        alias /www/wwwroot/baoxian.weixinjishu.top/hk_insurance_backend/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
    }
    
    # 媒体文件配置
    location /media/ {
        alias /www/wwwroot/baoxian.weixinjishu.top/hk_insurance_backend/media/;
        expires 30d;
        add_header Cache-Control "public";
        add_header X-Content-Type-Options nosniff;
    }

# 同时需要修改 proxy_set_header Host 的值：
# 将：proxy_set_header Host 127.0.0.1:$server_port;
# 改为：proxy_set_header Host $host;
