# Generated by Django 5.2.4 on 2025-07-22 15:09

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('insurance', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserPolicy',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('policy_number', models.CharField(max_length=50, unique=True, verbose_name='保单号')),
                ('insured_name', models.Char<PERSON>ield(max_length=50, verbose_name='被保险人姓名')),
                ('insured_id_number', models.CharField(max_length=30, verbose_name='被保险人证件号')),
                ('beneficiary_name', models.CharField(max_length=50, verbose_name='受益人姓名')),
                ('beneficiary_relationship', models.Char<PERSON>ield(max_length=20, verbose_name='与被保险人关系')),
                ('premium_amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='保费金额')),
                ('currency', models.CharField(max_length=3, verbose_name='货币')),
                ('payment_frequency', models.CharField(max_length=20, verbose_name='缴费频率')),
                ('coverage_amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='保障金额')),
                ('start_date', models.DateField(verbose_name='保障开始日期')),
                ('end_date', models.DateField(verbose_name='保障结束日期')),
                ('status', models.CharField(choices=[('active', '生效中'), ('pending', '待生效'), ('expired', '已过期'), ('cancelled', '已取消'), ('suspended', '已暂停')], default='pending', max_length=20, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='insurance.insuranceproduct', verbose_name='保险产品')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='policies', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户保单',
                'verbose_name_plural': '用户保单',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserClaim',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('claim_number', models.CharField(max_length=50, unique=True, verbose_name='理赔号')),
                ('claim_type', models.CharField(max_length=50, verbose_name='理赔类型')),
                ('incident_date', models.DateField(verbose_name='事故发生日期')),
                ('incident_description', models.TextField(verbose_name='事故描述')),
                ('claim_amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='申请理赔金额')),
                ('approved_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='批准理赔金额')),
                ('status', models.CharField(choices=[('submitted', '已提交'), ('reviewing', '审核中'), ('approved', '已批准'), ('rejected', '已拒绝'), ('paid', '已赔付'), ('closed', '已关闭')], default='submitted', max_length=20, verbose_name='状态')),
                ('review_notes', models.TextField(blank=True, verbose_name='审核备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('reviewed_at', models.DateTimeField(blank=True, null=True, verbose_name='审核时间')),
                ('paid_at', models.DateTimeField(blank=True, null=True, verbose_name='赔付时间')),
                ('reviewer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_claims', to=settings.AUTH_USER_MODEL, verbose_name='审核人')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='claims', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
                ('policy', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='claims', to='users.userpolicy', verbose_name='保单')),
            ],
            options={
                'verbose_name': '用户理赔',
                'verbose_name_plural': '用户理赔',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('openid', models.CharField(max_length=100, unique=True, verbose_name='微信OpenID')),
                ('unionid', models.CharField(blank=True, max_length=100, verbose_name='微信UnionID')),
                ('session_key', models.CharField(blank=True, max_length=100, verbose_name='会话密钥')),
                ('nickname', models.CharField(blank=True, max_length=50, verbose_name='昵称')),
                ('avatar', models.URLField(blank=True, verbose_name='头像')),
                ('gender', models.CharField(blank=True, choices=[('M', '男'), ('F', '女'), ('O', '其他')], max_length=1, verbose_name='性别')),
                ('birthday', models.DateField(blank=True, null=True, verbose_name='生日')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='手机号')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='邮箱')),
                ('country', models.CharField(blank=True, max_length=50, verbose_name='国家')),
                ('province', models.CharField(blank=True, max_length=50, verbose_name='省份')),
                ('city', models.CharField(blank=True, max_length=50, verbose_name='城市')),
                ('address', models.TextField(blank=True, verbose_name='详细地址')),
                ('occupation', models.CharField(blank=True, max_length=100, verbose_name='职业')),
                ('annual_income', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='年收入')),
                ('preferred_language', models.CharField(default='zh-cn', max_length=10, verbose_name='首选语言')),
                ('preferred_currency', models.CharField(default='USD', max_length=3, verbose_name='首选货币')),
                ('is_verified', models.BooleanField(default=False, verbose_name='是否认证')),
                ('is_vip', models.BooleanField(default=False, verbose_name='是否VIP')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('last_login_at', models.DateTimeField(blank=True, null=True, verbose_name='最后登录时间')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户资料',
                'verbose_name_plural': '用户资料',
                'ordering': ['-created_at'],
            },
        ),
    ]
