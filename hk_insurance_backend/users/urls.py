from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# 创建路由器
router = DefaultRouter()
router.register(r'profiles', views.UserProfileViewSet)
router.register(r'policies', views.UserPolicyViewSet)
router.register(r'claims', views.UserClaimViewSet)

urlpatterns = [
    # API路由
    path('api/', include(router.urls)),
    
    # 自定义API端点
    path('api/wechat-login/', views.WeChatLoginAPIView.as_view(), name='wechat-login'),
    path('api/dashboard/', views.UserDashboardAPIView.as_view(), name='user-dashboard'),
]
