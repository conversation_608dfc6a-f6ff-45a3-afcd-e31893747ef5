from django.contrib import admin
from unfold.admin import ModelAdmin
from .models import UserProfile, UserPolicy, UserClaim


@admin.register(UserProfile)
class UserProfileAdmin(ModelAdmin):
    list_display = ['user', 'nickname', 'phone', 'gender', 'is_verified', 'is_vip', 'created_at']
    list_filter = ['gender', 'is_verified', 'is_vip', 'preferred_language', 'created_at']
    search_fields = ['user__username', 'nickname', 'phone', 'email', 'openid']
    list_editable = ['is_verified', 'is_vip']
    ordering = ['-created_at']
    readonly_fields = ['openid', 'created_at', 'updated_at', 'last_login_at']

    fieldsets = [
        ('用户信息', {
            'fields': ['user', 'openid', 'unionid', 'session_key']
        }),
        ('个人信息', {
            'fields': ['nickname', 'avatar', 'gender', 'birthday', 'phone', 'email']
        }),
        ('地址信息', {
            'fields': ['country', 'province', 'city', 'address']
        }),
        ('职业信息', {
            'fields': ['occupation', 'annual_income']
        }),
        ('偏好设置', {
            'fields': ['preferred_language', 'preferred_currency']
        }),
        ('状态设置', {
            'fields': ['is_verified', 'is_vip']
        }),
        ('时间信息', {
            'fields': ['created_at', 'updated_at', 'last_login_at'],
            'classes': ['collapse']
        }),
    ]


@admin.register(UserPolicy)
class UserPolicyAdmin(ModelAdmin):
    list_display = ['policy_number', 'user', 'product', 'insured_name', 'premium_amount',
                   'currency', 'status', 'start_date', 'end_date']
    list_filter = ['status', 'currency', 'payment_frequency', 'start_date', 'created_at']
    search_fields = ['policy_number', 'user__username', 'insured_name', 'insured_id_number']
    list_editable = ['status']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = [
        ('基本信息', {
            'fields': ['user', 'product', 'policy_number']
        }),
        ('被保险人信息', {
            'fields': ['insured_name', 'insured_id_number']
        }),
        ('受益人信息', {
            'fields': ['beneficiary_name', 'beneficiary_relationship']
        }),
        ('保费信息', {
            'fields': ['premium_amount', 'currency', 'payment_frequency']
        }),
        ('保障信息', {
            'fields': ['coverage_amount', 'start_date', 'end_date']
        }),
        ('状态信息', {
            'fields': ['status']
        }),
        ('时间信息', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]


@admin.register(UserClaim)
class UserClaimAdmin(ModelAdmin):
    list_display = ['claim_number', 'user', 'policy', 'claim_type', 'claim_amount',
                   'approved_amount', 'status', 'incident_date', 'created_at']
    list_filter = ['status', 'claim_type', 'incident_date', 'created_at']
    search_fields = ['claim_number', 'user__username', 'policy__policy_number', 'incident_description']
    list_editable = ['status']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = [
        ('基本信息', {
            'fields': ['user', 'policy', 'claim_number']
        }),
        ('理赔信息', {
            'fields': ['claim_type', 'incident_date', 'incident_description', 'claim_amount']
        }),
        ('审核信息', {
            'fields': ['status', 'reviewer', 'approved_amount', 'review_notes']
        }),
        ('时间信息', {
            'fields': ['created_at', 'updated_at', 'reviewed_at', 'paid_at'],
            'classes': ['collapse']
        }),
    ]
