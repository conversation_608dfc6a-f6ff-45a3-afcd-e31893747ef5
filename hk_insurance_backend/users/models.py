from django.db import models
from django.contrib.auth.models import User
import uuid


class UserProfile(models.Model):
    """用户资料模型"""
    GENDER_CHOICES = [
        ('M', '男'),
        ('F', '女'),
        ('O', '其他'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile', verbose_name="用户")

    # 微信信息
    openid = models.CharField(max_length=100, unique=True, verbose_name="微信OpenID")
    unionid = models.CharField(max_length=100, blank=True, verbose_name="微信UnionID")
    session_key = models.CharField(max_length=100, blank=True, verbose_name="会话密钥")

    # 个人信息
    nickname = models.CharField(max_length=50, verbose_name="昵称", blank=True)
    avatar = models.URLField(verbose_name="头像", blank=True)
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES, verbose_name="性别", blank=True)
    birthday = models.DateField(verbose_name="生日", null=True, blank=True)
    phone = models.CharField(max_length=20, verbose_name="手机号", blank=True)
    email = models.EmailField(verbose_name="邮箱", blank=True)

    # 地址信息
    country = models.CharField(max_length=50, verbose_name="国家", blank=True)
    province = models.CharField(max_length=50, verbose_name="省份", blank=True)
    city = models.CharField(max_length=50, verbose_name="城市", blank=True)
    address = models.TextField(verbose_name="详细地址", blank=True)

    # 职业信息
    occupation = models.CharField(max_length=100, verbose_name="职业", blank=True)
    annual_income = models.DecimalField(max_digits=12, decimal_places=2, verbose_name="年收入", null=True, blank=True)

    # 偏好设置
    preferred_language = models.CharField(max_length=10, default='zh-cn', verbose_name="首选语言")
    preferred_currency = models.CharField(max_length=3, default='USD', verbose_name="首选货币")

    # 状态
    is_verified = models.BooleanField(default=False, verbose_name="是否认证")
    is_vip = models.BooleanField(default=False, verbose_name="是否VIP")

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    last_login_at = models.DateTimeField(null=True, blank=True, verbose_name="最后登录时间")

    class Meta:
        verbose_name = "用户资料"
        verbose_name_plural = "用户资料"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.nickname}"

    @property
    def age(self):
        """计算年龄"""
        if self.birthday:
            from datetime import date
            today = date.today()
            return today.year - self.birthday.year - ((today.month, today.day) < (self.birthday.month, self.birthday.day))
        return None


class UserPolicy(models.Model):
    """用户保单模型"""
    STATUS_CHOICES = [
        ('active', '生效中'),
        ('pending', '待生效'),
        ('expired', '已过期'),
        ('cancelled', '已取消'),
        ('suspended', '已暂停'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='policies', verbose_name="用户")
    product = models.ForeignKey('insurance.InsuranceProduct', on_delete=models.CASCADE, verbose_name="保险产品")

    # 保单信息
    policy_number = models.CharField(max_length=50, unique=True, verbose_name="保单号")
    insured_name = models.CharField(max_length=50, verbose_name="被保险人姓名")
    insured_id_number = models.CharField(max_length=30, verbose_name="被保险人证件号")
    beneficiary_name = models.CharField(max_length=50, verbose_name="受益人姓名")
    beneficiary_relationship = models.CharField(max_length=20, verbose_name="与被保险人关系")

    # 保费信息
    premium_amount = models.DecimalField(max_digits=12, decimal_places=2, verbose_name="保费金额")
    currency = models.CharField(max_length=3, verbose_name="货币")
    payment_frequency = models.CharField(max_length=20, verbose_name="缴费频率")

    # 保障信息
    coverage_amount = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="保障金额")
    start_date = models.DateField(verbose_name="保障开始日期")
    end_date = models.DateField(verbose_name="保障结束日期")

    # 状态
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="状态")

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "用户保单"
        verbose_name_plural = "用户保单"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.policy_number} - {self.insured_name}"

    @property
    def is_active(self):
        """是否生效中"""
        return self.status == 'active'

    @property
    def days_remaining(self):
        """剩余天数"""
        if self.end_date:
            from datetime import date
            today = date.today()
            return (self.end_date - today).days
        return None


class UserClaim(models.Model):
    """用户理赔模型"""
    STATUS_CHOICES = [
        ('submitted', '已提交'),
        ('reviewing', '审核中'),
        ('approved', '已批准'),
        ('rejected', '已拒绝'),
        ('paid', '已赔付'),
        ('closed', '已关闭'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='claims', verbose_name="用户")
    policy = models.ForeignKey(UserPolicy, on_delete=models.CASCADE, related_name='claims', verbose_name="保单")

    # 理赔信息
    claim_number = models.CharField(max_length=50, unique=True, verbose_name="理赔号")
    claim_type = models.CharField(max_length=50, verbose_name="理赔类型")
    incident_date = models.DateField(verbose_name="事故发生日期")
    incident_description = models.TextField(verbose_name="事故描述")
    claim_amount = models.DecimalField(max_digits=12, decimal_places=2, verbose_name="申请理赔金额")
    approved_amount = models.DecimalField(max_digits=12, decimal_places=2, verbose_name="批准理赔金额", null=True, blank=True)

    # 状态
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='submitted', verbose_name="状态")
    reviewer = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                related_name='reviewed_claims', verbose_name="审核人")
    review_notes = models.TextField(verbose_name="审核备注", blank=True)

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    reviewed_at = models.DateTimeField(null=True, blank=True, verbose_name="审核时间")
    paid_at = models.DateTimeField(null=True, blank=True, verbose_name="赔付时间")

    class Meta:
        verbose_name = "用户理赔"
        verbose_name_plural = "用户理赔"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.claim_number} - {self.user.username}"
