from rest_framework import serializers
from django.contrib.auth.models import User
from .models import UserProfile, UserPolicy, UserClaim


class UserSerializer(serializers.ModelSerializer):
    """用户序列化器"""
    
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'date_joined']
        read_only_fields = ['id', 'date_joined']


class UserProfileSerializer(serializers.ModelSerializer):
    """用户资料序列化器"""
    user = UserSerializer(read_only=True)
    age = serializers.ReadOnlyField()
    
    class Meta:
        model = UserProfile
        fields = [
            'id', 'user', 'openid', 'nickname', 'avatar', 'gender', 'birthday', 'age',
            'phone', 'email', 'country', 'province', 'city', 'address',
            'occupation', 'annual_income', 'preferred_language', 'preferred_currency',
            'is_verified', 'is_vip', 'created_at', 'updated_at', 'last_login_at'
        ]
        read_only_fields = [
            'id', 'openid', 'is_verified', 'is_vip', 
            'created_at', 'updated_at', 'last_login_at'
        ]


class UserProfileUpdateSerializer(serializers.ModelSerializer):
    """用户资料更新序列化器"""
    
    class Meta:
        model = UserProfile
        fields = [
            'nickname', 'gender', 'birthday', 'phone', 'email',
            'country', 'province', 'city', 'address',
            'occupation', 'annual_income', 'preferred_language', 'preferred_currency'
        ]


class UserPolicySerializer(serializers.ModelSerializer):
    """用户保单序列化器"""
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_company = serializers.CharField(source='product.company.name', read_only=True)
    product_category = serializers.CharField(source='product.category.name', read_only=True)
    is_active = serializers.ReadOnlyField()
    days_remaining = serializers.ReadOnlyField()
    
    class Meta:
        model = UserPolicy
        fields = [
            'id', 'product', 'product_name', 'product_company', 'product_category',
            'policy_number', 'insured_name', 'insured_id_number',
            'beneficiary_name', 'beneficiary_relationship',
            'premium_amount', 'currency', 'payment_frequency',
            'coverage_amount', 'start_date', 'end_date',
            'status', 'is_active', 'days_remaining',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'user', 'policy_number', 'status',
            'created_at', 'updated_at'
        ]


class UserClaimSerializer(serializers.ModelSerializer):
    """用户理赔序列化器"""
    policy_number = serializers.CharField(source='policy.policy_number', read_only=True)
    product_name = serializers.CharField(source='policy.product.name', read_only=True)
    
    class Meta:
        model = UserClaim
        fields = [
            'id', 'policy', 'policy_number', 'product_name',
            'claim_number', 'claim_type', 'incident_date', 'incident_description',
            'claim_amount', 'approved_amount', 'status',
            'review_notes', 'created_at', 'updated_at',
            'reviewed_at', 'paid_at'
        ]
        read_only_fields = [
            'id', 'user', 'claim_number', 'approved_amount',
            'status', 'reviewer', 'review_notes',
            'created_at', 'updated_at', 'reviewed_at', 'paid_at'
        ]


class UserClaimCreateSerializer(serializers.ModelSerializer):
    """用户理赔创建序列化器"""
    
    class Meta:
        model = UserClaim
        fields = [
            'policy', 'claim_type', 'incident_date', 'incident_description', 'claim_amount'
        ]
    
    def validate_policy(self, value):
        """验证保单是否属于当前用户"""
        if value.user != self.context['request'].user:
            raise serializers.ValidationError("只能为自己的保单申请理赔")
        if value.status != 'active':
            raise serializers.ValidationError("只能为生效中的保单申请理赔")
        return value
    
    def create(self, validated_data):
        """创建理赔记录"""
        validated_data['user'] = self.context['request'].user
        # 生成理赔号
        import uuid
        validated_data['claim_number'] = f"CL{uuid.uuid4().hex[:10].upper()}"
        return super().create(validated_data)


class WeChatLoginSerializer(serializers.Serializer):
    """微信登录序列化器"""
    code = serializers.CharField(max_length=100, help_text="微信登录code")
    
    def validate_code(self, value):
        """验证微信登录code"""
        if not value:
            raise serializers.ValidationError("微信登录code不能为空")
        return value


class UserDashboardSerializer(serializers.Serializer):
    """用户仪表板序列化器"""
    profile = UserProfileSerializer()
    policies_count = serializers.IntegerField()
    active_policies_count = serializers.IntegerField()
    claims_count = serializers.IntegerField()
    pending_claims_count = serializers.IntegerField()
    recent_policies = UserPolicySerializer(many=True)
    recent_claims = UserClaimSerializer(many=True)
