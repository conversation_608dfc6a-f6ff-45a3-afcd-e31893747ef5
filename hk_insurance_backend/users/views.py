from rest_framework import generics, viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth.models import User
from django.contrib.auth import login
from django.utils import timezone
import requests
import json

from .models import UserProfile, UserPolicy, UserClaim
from .serializers import (
    UserProfileSerializer, UserProfileUpdateSerializer,
    UserPolicySerializer, UserClaimSerializer, UserClaimCreateSerializer,
    WeChatLoginSerializer, UserDashboardSerializer
)


class UserProfileViewSet(viewsets.ModelViewSet):
    """用户资料视图集"""
    queryset = UserProfile.objects.all()
    serializer_class = UserProfileSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """只返回当前用户的资料"""
        return UserProfile.objects.filter(user=self.request.user)

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action in ['update', 'partial_update']:
            return UserProfileUpdateSerializer
        return UserProfileSerializer

    @action(detail=False, methods=['get'])
    def me(self, request):
        """获取当前用户资料"""
        try:
            profile = request.user.profile
            serializer = UserProfileSerializer(profile)
            return Response(serializer.data)
        except UserProfile.DoesNotExist:
            return Response({'error': '用户资料不存在'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['put', 'patch'])
    def update_me(self, request):
        """更新当前用户资料"""
        try:
            profile = request.user.profile
            serializer = UserProfileUpdateSerializer(profile, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response(UserProfileSerializer(profile).data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except UserProfile.DoesNotExist:
            return Response({'error': '用户资料不存在'}, status=status.HTTP_404_NOT_FOUND)


class UserPolicyViewSet(viewsets.ReadOnlyModelViewSet):
    """用户保单视图集"""
    queryset = UserPolicy.objects.all()
    serializer_class = UserPolicySerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """只返回当前用户的保单"""
        return UserPolicy.objects.filter(user=self.request.user).select_related('product__company', 'product__category')

    @action(detail=False, methods=['get'])
    def active(self, request):
        """获取生效中的保单"""
        queryset = self.get_queryset().filter(status='active')
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def expired(self, request):
        """获取已过期的保单"""
        queryset = self.get_queryset().filter(status='expired')
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class UserClaimViewSet(viewsets.ModelViewSet):
    """用户理赔视图集"""
    queryset = UserClaim.objects.all()
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """只返回当前用户的理赔记录"""
        return UserClaim.objects.filter(user=self.request.user).select_related('policy__product')

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return UserClaimCreateSerializer
        return UserClaimSerializer

    @action(detail=False, methods=['get'])
    def pending(self, request):
        """获取待处理的理赔"""
        queryset = self.get_queryset().filter(status__in=['submitted', 'reviewing'])
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def completed(self, request):
        """获取已完成的理赔"""
        queryset = self.get_queryset().filter(status__in=['paid', 'closed'])
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class WeChatLoginAPIView(generics.GenericAPIView):
    """微信登录API"""
    serializer_class = WeChatLoginSerializer

    def post(self, request):
        """微信登录"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            code = serializer.validated_data['code']

            # 调用微信API获取用户信息
            wx_data = self.get_wechat_user_info(code)
            if not wx_data:
                return Response({'error': '微信登录失败'}, status=status.HTTP_400_BAD_REQUEST)

            # 获取或创建用户
            user, profile = self.get_or_create_user(wx_data)

            # 登录用户
            login(request, user)

            # 更新最后登录时间
            profile.last_login_at = timezone.now()
            profile.save(update_fields=['last_login_at'])

            return Response({
                'user': UserProfileSerializer(profile).data,
                'message': '登录成功'
            })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_wechat_user_info(self, code):
        """获取微信用户信息"""
        # 这里应该调用微信API，暂时返回模拟数据
        # 实际项目中需要配置微信小程序的AppID和AppSecret
        return {
            'openid': f'mock_openid_{code}',
            'session_key': f'mock_session_key_{code}',
            'unionid': '',
        }

    def get_or_create_user(self, wx_data):
        """获取或创建用户"""
        openid = wx_data['openid']

        try:
            # 查找已存在的用户
            profile = UserProfile.objects.get(openid=openid)
            user = profile.user
        except UserProfile.DoesNotExist:
            # 创建新用户
            user = User.objects.create_user(
                username=f'wx_{openid[:10]}',
                email=''
            )
            profile = UserProfile.objects.create(
                user=user,
                openid=openid,
                session_key=wx_data.get('session_key', ''),
                unionid=wx_data.get('unionid', '')
            )

        return user, profile


class UserDashboardAPIView(generics.GenericAPIView):
    """用户仪表板API"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取用户仪表板数据"""
        user = request.user

        # 用户资料
        try:
            profile = user.profile
        except UserProfile.DoesNotExist:
            return Response({'error': '用户资料不存在'}, status=status.HTTP_404_NOT_FOUND)

        # 保单统计
        policies = UserPolicy.objects.filter(user=user)
        policies_count = policies.count()
        active_policies_count = policies.filter(status='active').count()

        # 理赔统计
        claims = UserClaim.objects.filter(user=user)
        claims_count = claims.count()
        pending_claims_count = claims.filter(status__in=['submitted', 'reviewing']).count()

        # 最近保单
        recent_policies = policies.select_related('product__company', 'product__category').order_by('-created_at')[:3]

        # 最近理赔
        recent_claims = claims.select_related('policy__product').order_by('-created_at')[:3]

        data = {
            'profile': UserProfileSerializer(profile).data,
            'policies_count': policies_count,
            'active_policies_count': active_policies_count,
            'claims_count': claims_count,
            'pending_claims_count': pending_claims_count,
            'recent_policies': UserPolicySerializer(recent_policies, many=True).data,
            'recent_claims': UserClaimSerializer(recent_claims, many=True).data,
        }

        serializer = UserDashboardSerializer(data)
        return Response(serializer.data)
