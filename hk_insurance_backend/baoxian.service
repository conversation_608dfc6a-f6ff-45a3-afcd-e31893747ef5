[Unit]
Description=Hong Kong Insurance Backend Service
After=network.target mysql.service redis.service
Wants=mysql.service redis.service

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/www/wwwroot/baoxian.weixinjishu.top/hk_insurance_backend
Environment=PATH=/www/wwwroot/baoxian.weixinjishu.top/hk_insurance_backend/venv/bin
Environment=DJANGO_SETTINGS_MODULE=hk_insurance.settings_production
ExecStart=/www/wwwroot/baoxian.weixinjishu.top/hk_insurance_backend/start_production.sh
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=3
StandardOutput=journal
StandardError=journal

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/www/wwwroot/baoxian.weixinjishu.top

[Install]
WantedBy=multi-user.target
