#!/usr/bin/env python
"""
创建示例数据脚本
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hk_insurance.settings')
django.setup()

from insurance.models import InsuranceCompany, ProductCategory, InsuranceProduct, Banner
from users.models import UserProfile
from django.contrib.auth.models import User


def create_companies():
    """创建保险公司"""
    companies_data = [
        {
            'name': '友邦保险',
            'name_en': 'AIA',
            'description': '友邦保险是亚洲领先的保险集团，为个人和企业客户提供一系列的产品及服务。',
            'website': 'https://www.aia.com.hk',
            'established_year': 1919,
            'sort_order': 1
        },
        {
            'name': '保诚保险',
            'name_en': 'Prudential',
            'description': '保诚保险是英国最大的保险公司之一，在香港提供多元化的保险产品。',
            'website': 'https://www.prudential.com.hk',
            'established_year': 1848,
            'sort_order': 2
        },
        {
            'name': '宏利保险',
            'name_en': 'Manulife',
            'description': '宏利金融是加拿大领先的金融服务集团，在香港提供保险和财富管理服务。',
            'website': 'https://www.manulife.com.hk',
            'established_year': 1887,
            'sort_order': 3
        },
        {
            'name': '中银人寿',
            'name_en': 'BOC Life',
            'description': '中银人寿保险有限公司是中国银行（香港）有限公司的全资附属公司。',
            'website': 'https://www.boclife.com.hk',
            'established_year': 2001,
            'sort_order': 4
        }
    ]
    
    for data in companies_data:
        company, created = InsuranceCompany.objects.get_or_create(
            name=data['name'],
            defaults=data
        )
        if created:
            print(f"创建保险公司: {company.name}")


def create_categories():
    """创建产品分类"""
    categories_data = [
        {
            'name': '储蓄分红险',
            'name_en': 'Savings Insurance',
            'icon': 'piggy-bank',
            'description': '长期储蓄，稳健增值，预期年化收益6%-7%',
            'sort_order': 1
        },
        {
            'name': '重疾保险',
            'name_en': 'Critical Illness',
            'icon': 'shield-alt',
            'description': '保障范围广，保费低，含早期疾病保障',
            'sort_order': 2
        },
        {
            'name': '高端医疗',
            'name_en': 'Premium Medical',
            'icon': 'hospital',
            'description': '全球医疗网络，免垫付直付服务',
            'sort_order': 3
        },
        {
            'name': '投资理财',
            'name_en': 'Investment',
            'icon': 'chart-line',
            'description': '保障与投资并重，灵活缴费提取',
            'sort_order': 4
        }
    ]
    
    for data in categories_data:
        category, created = ProductCategory.objects.get_or_create(
            name=data['name'],
            defaults=data
        )
        if created:
            print(f"创建产品分类: {category.name}")


def create_products():
    """创建保险产品"""
    # 获取公司和分类
    aia = InsuranceCompany.objects.get(name='友邦保险')
    prudential = InsuranceCompany.objects.get(name='保诚保险')
    manulife = InsuranceCompany.objects.get(name='宏利保险')
    boc = InsuranceCompany.objects.get(name='中银人寿')
    
    savings_cat = ProductCategory.objects.get(name='储蓄分红险')
    critical_cat = ProductCategory.objects.get(name='重疾保险')
    medical_cat = ProductCategory.objects.get(name='高端医疗')
    investment_cat = ProductCategory.objects.get(name='投资理财')
    
    products_data = [
        {
            'company': aia,
            'category': savings_cat,
            'name': '友邦传世金生储蓄计划',
            'name_en': 'AIA Legacy Savings Plan',
            'subtitle': '长期储蓄，稳健增值，预期年化收益6.5%',
            'description': '友邦传世金生储蓄计划是一份长期储蓄保险计划，旨在为您提供稳健的财富增值机会。',
            'currency': 'USD',
            'min_premium': 20000,
            'expected_return': 6.5,
            'features': ['美元资产', '分红保险', '财富传承', '灵活提取'],
            'payment_periods': [5, 10, 15, 20],
            'coverage_amount': 100000,
            'is_featured': True,
            'is_hot': True,
            'sort_order': 1
        },
        {
            'company': prudential,
            'category': critical_cat,
            'name': '保诚守护健康危疾保',
            'name_en': 'Prudential Health Guardian',
            'subtitle': '保障范围广，保费低，含早期疾病保障',
            'description': '保诚守护健康危疾保为您提供全面的重疾保障，涵盖100多种疾病。',
            'currency': 'HKD',
            'min_premium': 5000,
            'expected_return': None,
            'features': ['100+疾病', '早期疾病保障', '身故保障', '保费豁免'],
            'payment_periods': [10, 15, 20, 25],
            'coverage_amount': 1000000,
            'is_featured': True,
            'sort_order': 2
        },
        {
            'company': manulife,
            'category': medical_cat,
            'name': '宏利环球医疗保险',
            'name_en': 'Manulife Global Medical',
            'subtitle': '全球医疗网络，免垫付直付服务',
            'description': '宏利环球医疗保险提供全球医疗保障，年度保障无限额。',
            'currency': 'USD',
            'min_premium': 3000,
            'expected_return': None,
            'features': ['全球保障', '免垫付', '直付服务', '无限额'],
            'payment_periods': [1],
            'coverage_amount': None,
            'is_hot': True,
            'sort_order': 3
        },
        {
            'company': boc,
            'category': investment_cat,
            'name': '中银人寿万能寿险',
            'name_en': 'BOC Universal Life',
            'subtitle': '保障与投资并重，灵活缴费提取',
            'description': '中银人寿万能寿险结合保障与投资功能，提供灵活的缴费和提取选择。',
            'currency': 'USD',
            'min_premium': 10000,
            'expected_return': 5.5,
            'features': ['保证利率5.5%', '灵活缴费', '部分提取', '投资账户'],
            'payment_periods': [5, 10, 15],
            'coverage_amount': 200000,
            'sort_order': 4
        }
    ]
    
    for data in products_data:
        product, created = InsuranceProduct.objects.get_or_create(
            name=data['name'],
            company=data['company'],
            defaults=data
        )
        if created:
            print(f"创建保险产品: {product.name}")


def create_banners():
    """创建轮播图"""
    banners_data = [
        {
            'title': '储蓄分红保险',
            'subtitle': '预期年化收益6%-7%',
            'position': 'home',
            'link_type': 'category',
            'link_value': 'savings',
            'sort_order': 1
        },
        {
            'title': '重疾保险优惠',
            'subtitle': '保障100+种疾病',
            'position': 'home',
            'link_type': 'category',
            'link_value': 'critical',
            'sort_order': 2
        },
        {
            'title': '高端医疗保险',
            'subtitle': '全球医疗网络',
            'position': 'home',
            'link_type': 'category',
            'link_value': 'medical',
            'sort_order': 3
        }
    ]
    
    for data in banners_data:
        banner, created = Banner.objects.get_or_create(
            title=data['title'],
            position=data['position'],
            defaults=data
        )
        if created:
            print(f"创建轮播图: {banner.title}")


def main():
    """主函数"""
    print("开始创建示例数据...")
    
    create_companies()
    create_categories()
    create_products()
    create_banners()
    
    print("示例数据创建完成！")


if __name__ == '__main__':
    main()
