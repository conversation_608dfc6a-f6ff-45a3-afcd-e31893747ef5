"""
生产环境配置文件
"""

from .settings import *
import os

# 生产环境设置
DEBUG = False

# 允许的主机
ALLOWED_HOSTS = ['baoxian.weixinjishu.top', 'www.baoxian.weixinjishu.top']

# 安全设置
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_SSL_REDIRECT = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'

# 数据库配置（生产环境）
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'baoxian',
        'USER': 'baoxian_user',
        'PASSWORD': os.environ.get('DB_PASSWORD', '8c9af62b8832a8e6'),
        'HOST': '127.0.0.1',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}

# 静态文件配置
STATIC_ROOT = '/www/wwwroot/baoxian.weixinjishu.top/hk_insurance_backend/staticfiles'
MEDIA_ROOT = '/www/wwwroot/baoxian.weixinjishu.top/hk_insurance_backend/media'

# 缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        },
        'KEY_PREFIX': 'hk_insurance',
        'TIMEOUT': 300,
    }
}

# 会话配置
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'

# CORS设置（生产环境）
CORS_ALLOWED_ORIGINS = [
    "https://baoxian.weixinjishu.top",
    "https://www.baoxian.weixinjishu.top",
    "https://servicewechat.com",
    "https://developers.weixin.qq.com",
]
CORS_ALLOW_ALL_ORIGINS = False

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/www/wwwroot/baoxian.weixinjishu.top/logs/django.log',
            'formatter': 'verbose',
        },
        'error_file': {
            'level': 'ERROR',
            'class': 'logging.FileHandler',
            'filename': '/www/wwwroot/baoxian.weixinjishu.top/logs/django_error.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file', 'error_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.request': {
            'handlers': ['error_file'],
            'level': 'ERROR',
            'propagate': False,
        },
    },
}

# 邮件配置（用于错误报告）
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'  # 或其他SMTP服务器
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD', '')

# 管理员邮箱（接收错误报告）
ADMINS = [
    ('Admin', '<EMAIL>'),
]

# 服务器错误邮件
SERVER_EMAIL = '<EMAIL>'

# 性能优化
DATA_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB
FILE_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB

# 微信小程序配置
WECHAT_MINIPROGRAM = {
    'APP_ID': os.environ.get('WECHAT_APP_ID', ''),
    'APP_SECRET': os.environ.get('WECHAT_APP_SECRET', ''),
}

# API限流配置
REST_FRAMEWORK.update({
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.AnonRateThrottle',
        'rest_framework.throttling.UserRateThrottle'
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': '100/hour',
        'user': '1000/hour'
    }
})

# 密钥配置（从环境变量读取）
SECRET_KEY = os.environ.get('SECRET_KEY', SECRET_KEY)

# 时区设置
USE_TZ = True
TIME_ZONE = 'Asia/Shanghai'
