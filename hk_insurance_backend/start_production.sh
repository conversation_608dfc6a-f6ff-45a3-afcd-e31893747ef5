#!/bin/bash

# 香港保险小程序后端生产环境启动脚本

# 设置环境变量
export DJANGO_SETTINGS_MODULE=hk_insurance.settings_production

# 项目根目录
PROJECT_DIR="/www/wwwroot/baoxian.weixinjishu.top/hk_insurance_backend"
VENV_DIR="$PROJECT_DIR/venv"
LOG_DIR="/www/wwwroot/baoxian.weixinjishu.top/logs"

# 创建日志目录
mkdir -p $LOG_DIR

# 进入项目目录
cd $PROJECT_DIR

# 激活虚拟环境
source $VENV_DIR/bin/activate

# 检查数据库连接
echo "检查数据库连接..."
python manage.py check --database default

# 运行数据库迁移
echo "运行数据库迁移..."
python manage.py migrate --noinput

# 收集静态文件
echo "收集静态文件..."
python manage.py collectstatic --noinput

# 创建超级用户（如果不存在）
echo "检查超级用户..."
python manage.py shell -c "
from django.contrib.auth.models import User
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('超级用户已创建')
else:
    print('超级用户已存在')
"

# 创建示例数据（如果需要）
if [ "$1" = "--with-sample-data" ]; then
    echo "创建示例数据..."
    python create_sample_data.py
fi

# 启动应用
echo "启动应用..."
exec gunicorn hk_insurance.wsgi:application \
    --bind 127.0.0.1:8000 \
    --workers 4 \
    --worker-class sync \
    --worker-connections 1000 \
    --max-requests 1000 \
    --max-requests-jitter 100 \
    --timeout 30 \
    --keep-alive 2 \
    --user www-data \
    --group www-data \
    --log-level info \
    --access-logfile $LOG_DIR/gunicorn_access.log \
    --error-logfile $LOG_DIR/gunicorn_error.log \
    --capture-output \
    --enable-stdio-inheritance
