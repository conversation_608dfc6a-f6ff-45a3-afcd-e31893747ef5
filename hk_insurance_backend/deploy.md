# 香港保险小程序后端部署指南

## 域名配置

项目已配置使用域名：`baoxian.weixinjishu.top`

## 部署步骤

### 1. 服务器环境准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Python 3.11
sudo apt install python3.11 python3.11-venv python3.11-dev -y

# 安装MySQL
sudo apt install mysql-server mysql-client -y

# 安装Nginx
sudo apt install nginx -y

# 安装其他依赖
sudo apt install git curl wget -y
```

### 2. 项目部署

```bash
# 克隆项目到服务器
cd /www/wwwroot/
git clone <your-repo-url> baoxian.weixinjishu.top

# 进入项目目录
cd baoxian.weixinjishu.top/hk_insurance_backend

# 创建虚拟环境
python3.11 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 配置数据库
mysql -u root -p
CREATE DATABASE baoxian CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'baoxian_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON baoxian.* TO 'baoxian_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# 运行数据库迁移
python manage.py makemigrations
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser

# 创建示例数据
python create_sample_data.py

# 收集静态文件
python manage.py collectstatic --noinput
```

### 3. Nginx配置

创建Nginx配置文件：`/etc/nginx/sites-available/baoxian.weixinjishu.top`

```nginx
server {
    listen 80;
    server_name baoxian.weixinjishu.top;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name baoxian.weixinjishu.top;
    
    # SSL证书配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 静态文件
    location /static/ {
        alias /www/wwwroot/baoxian.weixinjishu.top/hk_insurance_backend/staticfiles/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    location /media/ {
        alias /www/wwwroot/baoxian.weixinjishu.top/hk_insurance_backend/media/;
        expires 30d;
        add_header Cache-Control "public";
    }
    
    # API请求
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS头
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization";
        
        # 处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization";
            return 204;
        }
    }
}
```

启用站点：
```bash
sudo ln -s /etc/nginx/sites-available/baoxian.weixinjishu.top /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 4. 系统服务配置

创建systemd服务文件：`/etc/systemd/system/baoxian.service`

```ini
[Unit]
Description=Hong Kong Insurance Backend
After=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/www/wwwroot/baoxian.weixinjishu.top/hk_insurance_backend
Environment=PATH=/www/wwwroot/baoxian.weixinjishu.top/hk_insurance_backend/venv/bin
ExecStart=/www/wwwroot/baoxian.weixinjishu.top/hk_insurance_backend/venv/bin/python manage.py runserver 127.0.0.1:8000
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable baoxian
sudo systemctl start baoxian
sudo systemctl status baoxian
```

### 5. SSL证书配置

使用Let's Encrypt免费SSL证书：

```bash
# 安装certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取SSL证书
sudo certbot --nginx -d baoxian.weixinjishu.top

# 设置自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 6. 防火墙配置

```bash
# 配置UFW防火墙
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 7. 监控和日志

```bash
# 查看应用日志
sudo journalctl -u baoxian -f

# 查看Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

## 环境变量配置

创建`.env`文件（生产环境）：

```env
DEBUG=False
SECRET_KEY=your-production-secret-key
DATABASE_URL=mysql://baoxian_user:your_password@localhost/baoxian
ALLOWED_HOSTS=baoxian.weixinjishu.top
```

## 备份策略

### 数据库备份
```bash
# 创建备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u baoxian_user -p baoxian > /backup/baoxian_$DATE.sql
find /backup -name "baoxian_*.sql" -mtime +7 -delete
```

### 文件备份
```bash
# 备份媒体文件和配置
tar -czf /backup/baoxian_files_$DATE.tar.gz /www/wwwroot/baoxian.weixinjishu.top/hk_insurance_backend/media/
```

## 性能优化

1. **数据库优化**
   - 配置MySQL慢查询日志
   - 添加适当的索引
   - 配置连接池

2. **缓存配置**
   - 安装Redis
   - 配置Django缓存

3. **静态文件优化**
   - 启用Gzip压缩
   - 配置CDN（可选）

## 安全检查清单

- [ ] SSL证书已配置
- [ ] 防火墙已启用
- [ ] 数据库用户权限最小化
- [ ] Django SECRET_KEY已更换
- [ ] DEBUG模式已关闭
- [ ] 安全头已配置
- [ ] 定期备份已设置
- [ ] 日志监控已配置

## 故障排除

### 常见问题

1. **502 Bad Gateway**
   - 检查Django应用是否运行
   - 检查端口是否正确

2. **静态文件404**
   - 运行`python manage.py collectstatic`
   - 检查Nginx静态文件配置

3. **数据库连接错误**
   - 检查数据库服务状态
   - 验证连接参数

4. **CORS错误**
   - 检查CORS配置
   - 验证域名设置
