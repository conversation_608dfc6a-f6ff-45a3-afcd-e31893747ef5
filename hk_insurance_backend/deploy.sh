#!/bin/bash

# 香港保险小程序后端快速部署脚本
# 使用方法: ./deploy.sh

set -e  # 遇到错误立即退出

echo "🚀 开始部署香港保险小程序后端..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="baoxian"
DOMAIN="baoxian.weixinjishu.top"
PROJECT_DIR="/www/wwwroot/$DOMAIN/hk_insurance_backend"
NGINX_CONF="/etc/nginx/sites-available/$DOMAIN"
SERVICE_FILE="/etc/systemd/system/$PROJECT_NAME.service"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}请使用root用户运行此脚本${NC}"
    exit 1
fi

# 1. 更新系统
echo -e "${YELLOW}📦 更新系统包...${NC}"
apt update && apt upgrade -y

# 2. 安装必要软件
echo -e "${YELLOW}📦 安装必要软件...${NC}"
apt install -y python3.11 python3.11-venv python3.11-dev \
    mysql-server mysql-client \
    nginx \
    git curl wget \
    certbot python3-certbot-nginx \
    redis-server

# 3. 创建项目目录
echo -e "${YELLOW}📁 创建项目目录...${NC}"
mkdir -p /www/wwwroot/$DOMAIN
mkdir -p /www/wwwroot/$DOMAIN/logs

# 4. 设置权限
echo -e "${YELLOW}🔐 设置目录权限...${NC}"
chown -R www-data:www-data /www/wwwroot/$DOMAIN

# 5. 配置MySQL数据库
echo -e "${YELLOW}🗄️ 配置MySQL数据库...${NC}"
mysql -e "CREATE DATABASE IF NOT EXISTS baoxian CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
mysql -e "CREATE USER IF NOT EXISTS 'baoxian_user'@'localhost' IDENTIFIED BY '8c9af62b8832a8e6';"
mysql -e "GRANT ALL PRIVILEGES ON baoxian.* TO 'baoxian_user'@'localhost';"
mysql -e "FLUSH PRIVILEGES;"

# 6. 进入项目目录并设置Python环境
echo -e "${YELLOW}🐍 设置Python环境...${NC}"
cd $PROJECT_DIR

# 创建虚拟环境
if [ ! -d "venv" ]; then
    python3.11 -m venv venv
fi

# 激活虚拟环境并安装依赖
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt

# 7. 运行Django设置
echo -e "${YELLOW}⚙️ 配置Django应用...${NC}"
export DJANGO_SETTINGS_MODULE=hk_insurance.settings_production

# 运行迁移
python manage.py makemigrations
python manage.py migrate

# 收集静态文件
python manage.py collectstatic --noinput

# 创建超级用户
python manage.py shell -c "
from django.contrib.auth.models import User
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', 'admin@$DOMAIN', 'admin123')
    print('超级用户已创建: admin/admin123')
else:
    print('超级用户已存在')
"

# 创建示例数据
echo -e "${YELLOW}📊 创建示例数据...${NC}"
python create_sample_data.py

# 8. 配置Nginx
echo -e "${YELLOW}🌐 配置Nginx...${NC}"
cp nginx.conf $NGINX_CONF

# 启用站点
ln -sf $NGINX_CONF /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# 测试Nginx配置
nginx -t

# 9. 配置systemd服务
echo -e "${YELLOW}🔧 配置系统服务...${NC}"
cp baoxian.service $SERVICE_FILE

# 重新加载systemd
systemctl daemon-reload
systemctl enable $PROJECT_NAME

# 10. 配置SSL证书
echo -e "${YELLOW}🔒 配置SSL证书...${NC}"
certbot --nginx -d $DOMAIN -d www.$DOMAIN --non-interactive --agree-tos --email admin@$DOMAIN

# 11. 配置防火墙
echo -e "${YELLOW}🛡️ 配置防火墙...${NC}"
ufw --force enable
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp

# 12. 启动服务
echo -e "${YELLOW}🚀 启动服务...${NC}"
systemctl restart nginx
systemctl start $PROJECT_NAME

# 13. 检查服务状态
echo -e "${YELLOW}✅ 检查服务状态...${NC}"
sleep 5

if systemctl is-active --quiet nginx; then
    echo -e "${GREEN}✅ Nginx 运行正常${NC}"
else
    echo -e "${RED}❌ Nginx 启动失败${NC}"
    systemctl status nginx
fi

if systemctl is-active --quiet $PROJECT_NAME; then
    echo -e "${GREEN}✅ Django应用 运行正常${NC}"
else
    echo -e "${RED}❌ Django应用 启动失败${NC}"
    systemctl status $PROJECT_NAME
fi

# 14. 设置定时任务
echo -e "${YELLOW}⏰ 设置定时任务...${NC}"
(crontab -l 2>/dev/null; echo "0 2 * * * certbot renew --quiet") | crontab -
(crontab -l 2>/dev/null; echo "0 3 * * * systemctl reload nginx") | crontab -

# 15. 创建备份脚本
echo -e "${YELLOW}💾 创建备份脚本...${NC}"
cat > /usr/local/bin/backup_baoxian.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/baoxian"
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u baoxian_user -p8c9af62b8832a8e6 baoxian > $BACKUP_DIR/baoxian_db_$DATE.sql

# 备份媒体文件
tar -czf $BACKUP_DIR/baoxian_media_$DATE.tar.gz /www/wwwroot/baoxian.weixinjishu.top/hk_insurance_backend/media/

# 删除7天前的备份
find $BACKUP_DIR -name "baoxian_*" -mtime +7 -delete

echo "备份完成: $DATE"
EOF

chmod +x /usr/local/bin/backup_baoxian.sh

# 添加备份定时任务
(crontab -l 2>/dev/null; echo "0 1 * * * /usr/local/bin/backup_baoxian.sh") | crontab -

# 16. 显示部署结果
echo -e "${GREEN}"
echo "🎉 部署完成！"
echo "=================================="
echo "网站地址: https://$DOMAIN"
echo "管理后台: https://$DOMAIN/admin/"
echo "API文档: https://$DOMAIN/api/docs/"
echo "管理员账号: admin"
echo "管理员密码: admin123"
echo "=================================="
echo -e "${NC}"

echo -e "${YELLOW}📝 重要提醒:${NC}"
echo "1. 请及时修改管理员密码"
echo "2. 请配置微信小程序AppID和AppSecret"
echo "3. 请检查防火墙和安全组设置"
echo "4. 建议配置监控和日志分析"

echo -e "${GREEN}✅ 部署脚本执行完成！${NC}"
