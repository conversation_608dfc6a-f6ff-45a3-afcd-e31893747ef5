<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>投保流程</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
        .card-shadow { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); }
        .step-active { background: #1E40AF; color: white; }
        .step-completed { background: #10B981; color: white; }
        .step-inactive { background: #E5E7EB; color: #6B7280; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航 -->
    <div class="bg-white border-b border-gray-200 px-4 py-3 flex items-center">
        <i class="fas fa-arrow-left text-gray-600 mr-4"></i>
        <h1 class="text-lg font-semibold flex-1">投保流程</h1>
        <i class="fas fa-question-circle text-gray-600"></i>
    </div>
    
    <!-- 进度条 -->
    <div class="bg-white px-4 py-4 border-b border-gray-100">
        <div class="flex items-center justify-between">
            <div class="flex flex-col items-center">
                <div class="w-8 h-8 rounded-full step-completed flex items-center justify-center text-sm font-semibold mb-1">
                    <i class="fas fa-check"></i>
                </div>
                <span class="text-xs text-gray-600">产品咨询</span>
            </div>
            <div class="flex-1 h-0.5 bg-green-500 mx-2"></div>
            <div class="flex flex-col items-center">
                <div class="w-8 h-8 rounded-full step-active flex items-center justify-center text-sm font-semibold mb-1">
                    2
                </div>
                <span class="text-xs text-blue-600">预约面谈</span>
            </div>
            <div class="flex-1 h-0.5 bg-gray-200 mx-2"></div>
            <div class="flex flex-col items-center">
                <div class="w-8 h-8 rounded-full step-inactive flex items-center justify-center text-sm font-semibold mb-1">
                    3
                </div>
                <span class="text-xs text-gray-400">签约投保</span>
            </div>
        </div>
    </div>
    
    <!-- 产品信息 -->
    <div class="bg-white mx-4 mt-4 rounded-2xl p-4 card-shadow">
        <h3 class="font-semibold mb-3 flex items-center">
            <i class="fas fa-shield-alt text-blue-600 mr-2"></i>
            已选产品
        </h3>
        <div class="bg-blue-50 rounded-xl p-3">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="font-semibold">友邦传世金生储蓄计划</h4>
                    <p class="text-sm text-gray-600">年缴保费：2万美元，缴费期：10年</p>
                </div>
                <div class="text-right">
                    <p class="text-lg font-bold text-blue-600">2万美元</p>
                    <p class="text-xs text-gray-500">年缴保费</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 被保险人信息 -->
    <div class="bg-white mx-4 mt-4 rounded-2xl p-4 card-shadow">
        <h3 class="font-semibold mb-4 flex items-center">
            <i class="fas fa-user text-green-600 mr-2"></i>
            被保险人信息
        </h3>
        <div class="space-y-4">
            <div>
                <label class="text-sm text-gray-600 block mb-2">姓名 *</label>
                <input type="text" placeholder="请输入姓名" class="w-full border border-gray-300 rounded-lg p-3 text-sm">
            </div>
            <div class="grid grid-cols-2 gap-3">
                <div>
                    <label class="text-sm text-gray-600 block mb-2">性别 *</label>
                    <select class="w-full border border-gray-300 rounded-lg p-3 text-sm">
                        <option>请选择</option>
                        <option>男</option>
                        <option>女</option>
                    </select>
                </div>
                <div>
                    <label class="text-sm text-gray-600 block mb-2">出生日期 *</label>
                    <input type="date" class="w-full border border-gray-300 rounded-lg p-3 text-sm">
                </div>
            </div>
            <div>
                <label class="text-sm text-gray-600 block mb-2">证件类型 *</label>
                <select class="w-full border border-gray-300 rounded-lg p-3 text-sm">
                    <option>身份证</option>
                    <option>护照</option>
                    <option>港澳通行证</option>
                    <option>台胞证</option>
                </select>
            </div>
            <div>
                <label class="text-sm text-gray-600 block mb-2">证件号码 *</label>
                <input type="text" placeholder="请输入证件号码" class="w-full border border-gray-300 rounded-lg p-3 text-sm">
            </div>
            <div>
                <label class="text-sm text-gray-600 block mb-2">手机号码 *</label>
                <input type="tel" placeholder="请输入手机号码" class="w-full border border-gray-300 rounded-lg p-3 text-sm">
            </div>
            <div>
                <label class="text-sm text-gray-600 block mb-2">邮箱地址</label>
                <input type="email" placeholder="请输入邮箱地址" class="w-full border border-gray-300 rounded-lg p-3 text-sm">
            </div>
        </div>
    </div>
    
    <!-- 出行信息 -->
    <div class="bg-white mx-4 mt-4 rounded-2xl p-4 card-shadow">
        <h3 class="font-semibold mb-4 flex items-center">
            <i class="fas fa-plane text-purple-600 mr-2"></i>
            出行信息
        </h3>
        <div class="space-y-4">
            <div>
                <label class="text-sm text-gray-600 block mb-2">目的地国家/地区 *</label>
                <select class="w-full border border-gray-300 rounded-lg p-3 text-sm">
                    <option>请选择目的地</option>
                    <option>法国</option>
                    <option>德国</option>
                    <option>意大利</option>
                    <option>西班牙</option>
                    <option>荷兰</option>
                    <option>其他欧洲国家</option>
                </select>
            </div>
            <div class="grid grid-cols-2 gap-3">
                <div>
                    <label class="text-sm text-gray-600 block mb-2">出发日期 *</label>
                    <input type="date" value="2024-03-15" class="w-full border border-gray-300 rounded-lg p-3 text-sm">
                </div>
                <div>
                    <label class="text-sm text-gray-600 block mb-2">返回日期 *</label>
                    <input type="date" value="2024-03-20" class="w-full border border-gray-300 rounded-lg p-3 text-sm">
                </div>
            </div>
            <div>
                <label class="text-sm text-gray-600 block mb-2">出行目的 *</label>
                <select class="w-full border border-gray-300 rounded-lg p-3 text-sm">
                    <option>旅游观光</option>
                    <option>商务出差</option>
                    <option>探亲访友</option>
                    <option>学术交流</option>
                    <option>其他</option>
                </select>
            </div>
        </div>
    </div>
    
    <!-- 受益人信息 -->
    <div class="bg-white mx-4 mt-4 rounded-2xl p-4 card-shadow">
        <h3 class="font-semibold mb-4 flex items-center">
            <i class="fas fa-users text-orange-600 mr-2"></i>
            受益人信息
        </h3>
        <div class="space-y-4">
            <div>
                <label class="text-sm text-gray-600 block mb-2">受益人姓名 *</label>
                <input type="text" placeholder="请输入受益人姓名" class="w-full border border-gray-300 rounded-lg p-3 text-sm">
            </div>
            <div>
                <label class="text-sm text-gray-600 block mb-2">与被保险人关系 *</label>
                <select class="w-full border border-gray-300 rounded-lg p-3 text-sm">
                    <option>请选择关系</option>
                    <option>配偶</option>
                    <option>父母</option>
                    <option>子女</option>
                    <option>兄弟姐妹</option>
                    <option>其他</option>
                </select>
            </div>
            <div>
                <label class="text-sm text-gray-600 block mb-2">受益人手机号 *</label>
                <input type="tel" placeholder="请输入受益人手机号" class="w-full border border-gray-300 rounded-lg p-3 text-sm">
            </div>
        </div>
    </div>
    
    <!-- 重要提示 -->
    <div class="bg-yellow-50 mx-4 mt-4 rounded-2xl p-4 border border-yellow-200">
        <h3 class="font-semibold mb-2 flex items-center text-yellow-800">
            <i class="fas fa-exclamation-triangle text-yellow-600 mr-2"></i>
            重要提示
        </h3>
        <div class="text-sm text-yellow-700 space-y-1">
            <p>• 请确保所填信息真实有效，虚假信息将影响理赔</p>
            <p>• 保险生效时间为出发日期当天0时</p>
            <p>• 如需修改信息，请在出发前联系客服</p>
        </div>
    </div>
    
    <!-- 协议确认 -->
    <div class="mx-4 mt-4 mb-24">
        <div class="flex items-start">
            <input type="checkbox" id="agreement" class="mt-1 mr-3">
            <label for="agreement" class="text-sm text-gray-600 flex-1">
                我已阅读并同意
                <span class="text-blue-600 underline">《保险条款》</span>、
                <span class="text-blue-600 underline">《投保须知》</span>和
                <span class="text-blue-600 underline">《隐私政策》</span>
            </label>
        </div>
    </div>
    
    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
        <div class="flex items-center justify-between mb-3">
            <span class="text-gray-600">保险费用</span>
            <span class="text-2xl font-bold text-blue-600">￥145</span>
        </div>
        <button class="w-full bg-blue-600 text-white py-3 rounded-xl font-semibold">
            下一步：确认支付
        </button>
    </div>
</body>
</html>
