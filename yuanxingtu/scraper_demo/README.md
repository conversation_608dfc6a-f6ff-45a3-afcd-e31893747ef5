# 香港保险数据爬虫系统

## 项目简介

这是一个专门用于爬取香港保险公司产品数据的系统，支持多家保险公司数据采集、数据清洗、去重和标准化处理。

## 功能特性

- 🏢 **多保险公司支持**: 友邦、保诚、宏利等主要香港保险公司
- 🔄 **自动化数据处理**: 数据清洗、去重、标准化
- ⏰ **定时任务**: 支持定时自动爬取
- 📊 **数据统计**: 自动生成数据统计报告
- 🛡️ **合规设计**: 遵循robots.txt，支持频率限制
- 📁 **多格式输出**: JSON、CSV格式数据导出

## 项目结构

```
scraper_demo/
├── scrapers/              # 爬虫模块
│   ├── base_scraper.py   # 基础爬虫类
│   ├── aia_scraper.py    # 友邦保险爬虫
│   └── ...               # 其他保险公司爬虫
├── data/                 # 数据目录
│   ├── raw/             # 原始数据
│   └── processed/       # 处理后数据
├── logs/                # 日志文件
├── config/              # 配置文件
├── main.py              # 主程序
├── data_processor.py    # 数据处理模块
├── requirements.txt     # 依赖包
└── README.md           # 说明文档
```

## 安装配置

### 1. 环境要求

- Python 3.8+
- Chrome浏览器
- ChromeDriver

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 安装ChromeDriver

```bash
# 方法1: 使用webdriver-manager (推荐)
# 已包含在requirements.txt中，会自动下载

# 方法2: 手动下载
# 下载地址: https://chromedriver.chromium.org/
# 将chromedriver放入PATH环境变量
```

## 使用方法

### 1. 单次运行

```bash
# 运行所有爬虫
python main.py --mode once --scraper all

# 运行指定爬虫
python main.py --mode once --scraper aia

# 仅处理现有数据
python main.py --process-only
```

### 2. 定时运行

```bash
# 启动定时任务
python main.py --mode schedule
```

### 3. 配置文件

系统会自动生成配置文件 `config/scraper_config.json`:

```json
{
  "scrapers": {
    "aia": {
      "enabled": true,
      "base_url": "https://www.aia.com.hk/zh-hk/our-products.html",
      "rate_limit": 2
    }
  },
  "schedule": {
    "enabled": false,
    "frequency": "daily",
    "time": "02:00"
  },
  "data_retention": {
    "raw_data_days": 30,
    "processed_data_days": 90
  }
}
```

## 数据结构

### 原始数据格式

```json
{
  "company_name": "友邦保险",
  "product_name": "传世金生储蓄计划",
  "product_type": "储蓄分红险",
  "currency": "USD",
  "min_premium": 20000,
  "max_premium": null,
  "expected_return": 6.5,
  "payment_period": [5, 10, 15, 20],
  "features": ["美元资产", "分红保险", "财富传承"],
  "description": "长期储蓄，稳健增值...",
  "url": "https://www.aia.com.hk/...",
  "last_updated": "2024-01-15T10:30:00"
}
```

### 处理后数据

经过清洗和标准化的数据，包含额外字段：
- `data_source`: 数据来源
- `processed_at`: 处理时间
- 标准化的产品类型和货币

## 合规说明

### 1. 数据来源合规性

- ✅ **公开信息**: 仅爬取保险公司官网公开展示的产品信息
- ✅ **Robots.txt**: 遵循网站robots.txt规则
- ✅ **频率限制**: 内置请求频率限制，避免对服务器造成压力
- ✅ **用户代理**: 使用标准浏览器用户代理

### 2. 数据使用合规性

- ✅ **非商业用途**: 仅用于产品比较和信息展示
- ✅ **数据更新**: 定期更新确保信息准确性
- ✅ **来源标注**: 保留原始数据来源链接

### 3. 法律风险控制

- ⚠️ **版权声明**: 产品描述等内容版权归原保险公司所有
- ⚠️ **使用限制**: 不得用于误导性营销或虚假宣传
- ⚠️ **数据准确性**: 最终产品信息以保险公司官方为准

## 扩展开发

### 1. 添加新的保险公司爬虫

```python
from base_scraper import BaseScraper, InsuranceProduct

class NewCompanyScraper(BaseScraper):
    def __init__(self):
        super().__init__(
            company_name="新公司",
            base_url="https://example.com"
        )
    
    def parse_product_list(self, html: str) -> List[str]:
        # 实现产品列表解析
        pass
    
    def parse_product_detail(self, html: str, url: str) -> Optional[InsuranceProduct]:
        # 实现产品详情解析
        pass
```

### 2. 数据库集成

```python
# 可选：添加数据库支持
import sqlalchemy
from sqlalchemy import create_engine

# 在data_processor.py中添加数据库保存功能
def save_to_database(self, data: List[Dict]):
    engine = create_engine('sqlite:///insurance_data.db')
    df = pd.DataFrame(data)
    df.to_sql('insurance_products', engine, if_exists='replace')
```

### 3. API服务

```python
# 可选：创建API服务
from flask import Flask, jsonify
import json

app = Flask(__name__)

@app.route('/api/products')
def get_products():
    with open('data/processed/latest.json', 'r') as f:
        data = json.load(f)
    return jsonify(data)
```

## 监控和维护

### 1. 日志监控

- 日志文件位置: `logs/scraper_YYYYMMDD.log`
- 监控爬虫成功率和错误信息
- 定期检查数据质量

### 2. 数据质量检查

```bash
# 检查最新数据
python -c "
import json
with open('data/processed/statistics_latest.json', 'r') as f:
    stats = json.load(f)
    print(f'总产品数: {stats[\"total_products\"]}')
    print(f'公司数: {len(stats[\"companies\"])}')
"
```

### 3. 定期维护

- 每月检查网站结构变化
- 更新爬虫解析规则
- 清理过期数据文件

## 注意事项

1. **网站变化**: 保险公司网站可能会更新，需要相应调整爬虫代码
2. **反爬措施**: 如遇到反爬措施，可以调整请求频率或使用代理
3. **数据准确性**: 爬取的数据仅供参考，具体产品信息以官方为准
4. **法律合规**: 使用前请确保符合当地法律法规要求

## 技术支持

如有问题或建议，请通过以下方式联系：

- 📧 Email: <EMAIL>
- 📱 微信: insurance_tech
- 🌐 网站: https://example.com

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。
