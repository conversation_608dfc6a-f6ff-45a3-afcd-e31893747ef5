#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
保险数据清洗和整合模块
"""

import json
import pandas as pd
import re
from datetime import datetime
from typing import List, Dict, Any
import logging
from pathlib import Path

class InsuranceDataProcessor:
    """保险数据处理器"""
    
    def __init__(self):
        self.setup_logging()
        self.raw_data_dir = Path("data/raw")
        self.processed_data_dir = Path("data/processed")
        self.processed_data_dir.mkdir(parents=True, exist_ok=True)
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger('DataProcessor')
        
    def load_raw_data(self) -> List[Dict]:
        """加载所有原始数据文件"""
        all_data = []
        
        # 遍历原始数据目录
        for json_file in self.raw_data_dir.glob("*.json"):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        all_data.extend(data)
                    else:
                        all_data.append(data)
                self.logger.info(f"加载数据文件: {json_file}")
            except Exception as e:
                self.logger.error(f"加载文件失败 {json_file}: {str(e)}")
                
        self.logger.info(f"总共加载 {len(all_data)} 条原始数据")
        return all_data
        
    def clean_product_name(self, name: str) -> str:
        """清洗产品名称"""
        if not name:
            return ""
            
        # 移除多余空格和特殊字符
        name = re.sub(r'\s+', ' ', name.strip())
        
        # 移除HTML标签
        name = re.sub(r'<[^>]+>', '', name)
        
        # 移除特殊符号
        name = re.sub(r'[^\w\s\u4e00-\u9fff\-\(\)（）]', '', name)
        
        return name.strip()
        
    def standardize_product_type(self, product_type: str, features: List[str], description: str) -> str:
        """标准化产品类型"""
        if not product_type:
            product_type = ""
            
        # 合并所有文本进行分析
        all_text = f"{product_type} {' '.join(features)} {description}".lower()
        
        # 产品类型映射
        type_mapping = {
            '储蓄分红险': ['储蓄', '分红', '传世', '盈聚', '充裕'],
            '重疾保险': ['重疾', '危疾', '疾病', '癌症', '健康'],
            '医疗保险': ['医疗', '住院', '门诊', '手术'],
            '人寿保险': ['人寿', '寿险', '身故', '终身'],
            '万能寿险': ['万能', '投资', '账户价值'],
            '年金保险': ['年金', '退休', '养老', '定期给付'],
            '意外保险': ['意外', '伤害', '残疾']
        }
        
        for standard_type, keywords in type_mapping.items():
            if any(keyword in all_text for keyword in keywords):
                return standard_type
                
        return '其他保险'
        
    def standardize_currency(self, currency: str, description: str) -> str:
        """标准化货币"""
        if not currency:
            currency = ""
            
        currency = currency.upper()
        all_text = f"{currency} {description}".upper()
        
        if any(keyword in all_text for keyword in ['USD', '美元', 'US$', '$']):
            return 'USD'
        elif any(keyword in all_text for keyword in ['HKD', '港币', '港元', 'HK$']):
            return 'HKD'
        elif any(keyword in all_text for keyword in ['CNY', '人民币', '元']):
            return 'CNY'
        else:
            return 'HKD'  # 默认港币
            
    def clean_premium_amount(self, amount: Any) -> float:
        """清洗保费金额"""
        if amount is None:
            return None
            
        if isinstance(amount, (int, float)):
            return float(amount)
            
        if isinstance(amount, str):
            # 移除货币符号和逗号
            amount = re.sub(r'[^\d.]', '', amount)
            try:
                return float(amount)
            except ValueError:
                return None
                
        return None
        
    def clean_expected_return(self, return_rate: Any) -> float:
        """清洗预期收益率"""
        if return_rate is None:
            return None
            
        if isinstance(return_rate, (int, float)):
            return float(return_rate)
            
        if isinstance(return_rate, str):
            # 提取数字
            match = re.search(r'(\d+\.?\d*)', return_rate)
            if match:
                try:
                    rate = float(match.group(1))
                    # 如果大于1，假设是百分比形式
                    if rate > 1:
                        rate = rate / 100
                    return rate
                except ValueError:
                    return None
                    
        return None
        
    def clean_payment_period(self, periods: Any) -> List[int]:
        """清洗缴费期"""
        if not periods:
            return []
            
        if isinstance(periods, str):
            # 解析逗号分隔的字符串
            periods = periods.split(',')
            
        if isinstance(periods, list):
            cleaned_periods = []
            for period in periods:
                try:
                    if isinstance(period, str):
                        # 提取数字
                        match = re.search(r'(\d+)', period)
                        if match:
                            p = int(match.group(1))
                            if 1 <= p <= 30:  # 合理范围
                                cleaned_periods.append(p)
                    elif isinstance(period, (int, float)):
                        p = int(period)
                        if 1 <= p <= 30:
                            cleaned_periods.append(p)
                except (ValueError, TypeError):
                    continue
                    
            return sorted(list(set(cleaned_periods)))
            
        return []
        
    def clean_features(self, features: Any) -> List[str]:
        """清洗产品特色"""
        if not features:
            return []
            
        if isinstance(features, str):
            # 解析逗号分隔的字符串
            features = features.split(',')
            
        if isinstance(features, list):
            cleaned_features = []
            for feature in features:
                if isinstance(feature, str):
                    feature = feature.strip()
                    if feature and len(feature) > 1:
                        cleaned_features.append(feature)
                        
            return cleaned_features[:5]  # 最多保留5个特色
            
        return []
        
    def remove_duplicates(self, data: List[Dict]) -> List[Dict]:
        """去除重复数据"""
        seen = set()
        unique_data = []
        
        for item in data:
            # 使用公司名称和产品名称作为唯一标识
            key = (item.get('company_name', ''), item.get('product_name', ''))
            if key not in seen and key[1]:  # 产品名称不能为空
                seen.add(key)
                unique_data.append(item)
                
        self.logger.info(f"去重后保留 {len(unique_data)} 条数据")
        return unique_data
        
    def process_data(self, raw_data: List[Dict]) -> List[Dict]:
        """处理数据"""
        processed_data = []
        
        for item in raw_data:
            try:
                # 清洗各个字段
                processed_item = {
                    'company_name': item.get('company_name', '').strip(),
                    'product_name': self.clean_product_name(item.get('product_name', '')),
                    'product_type': self.standardize_product_type(
                        item.get('product_type', ''),
                        item.get('features', []),
                        item.get('description', '')
                    ),
                    'currency': self.standardize_currency(
                        item.get('currency', ''),
                        item.get('description', '')
                    ),
                    'min_premium': self.clean_premium_amount(item.get('min_premium')),
                    'max_premium': self.clean_premium_amount(item.get('max_premium')),
                    'expected_return': self.clean_expected_return(item.get('expected_return')),
                    'payment_period': self.clean_payment_period(item.get('payment_period')),
                    'features': self.clean_features(item.get('features')),
                    'description': item.get('description', '').strip()[:500],  # 限制长度
                    'url': item.get('url', '').strip(),
                    'last_updated': item.get('last_updated', datetime.now().isoformat()),
                    'data_source': 'web_scraping',
                    'processed_at': datetime.now().isoformat()
                }
                
                # 验证必要字段
                if processed_item['product_name'] and processed_item['company_name']:
                    processed_data.append(processed_item)
                    
            except Exception as e:
                self.logger.error(f"处理数据项失败: {str(e)}")
                continue
                
        self.logger.info(f"成功处理 {len(processed_data)} 条数据")
        return processed_data
        
    def generate_statistics(self, data: List[Dict]) -> Dict:
        """生成数据统计"""
        stats = {
            'total_products': len(data),
            'companies': {},
            'product_types': {},
            'currencies': {},
            'avg_min_premium': 0,
            'avg_expected_return': 0,
            'generated_at': datetime.now().isoformat()
        }
        
        # 统计各维度数据
        min_premiums = []
        expected_returns = []
        
        for item in data:
            # 公司统计
            company = item.get('company_name', '未知')
            stats['companies'][company] = stats['companies'].get(company, 0) + 1
            
            # 产品类型统计
            product_type = item.get('product_type', '其他')
            stats['product_types'][product_type] = stats['product_types'].get(product_type, 0) + 1
            
            # 货币统计
            currency = item.get('currency', 'HKD')
            stats['currencies'][currency] = stats['currencies'].get(currency, 0) + 1
            
            # 保费统计
            if item.get('min_premium'):
                min_premiums.append(item['min_premium'])
                
            # 收益率统计
            if item.get('expected_return'):
                expected_returns.append(item['expected_return'])
                
        # 计算平均值
        if min_premiums:
            stats['avg_min_premium'] = sum(min_premiums) / len(min_premiums)
            
        if expected_returns:
            stats['avg_expected_return'] = sum(expected_returns) / len(expected_returns)
            
        return stats
        
    def save_processed_data(self, data: List[Dict], stats: Dict):
        """保存处理后的数据"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存JSON格式
        json_file = self.processed_data_dir / f"insurance_products_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
            
        # 保存CSV格式
        df = pd.DataFrame(data)
        csv_file = self.processed_data_dir / f"insurance_products_{timestamp}.csv"
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        
        # 保存统计信息
        stats_file = self.processed_data_dir / f"statistics_{timestamp}.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
            
        self.logger.info(f"数据已保存到 {self.processed_data_dir}")
        
    def run(self):
        """运行数据处理流程"""
        self.logger.info("开始数据处理流程...")
        
        # 加载原始数据
        raw_data = self.load_raw_data()
        if not raw_data:
            self.logger.error("没有找到原始数据")
            return
            
        # 处理数据
        processed_data = self.process_data(raw_data)
        
        # 去重
        unique_data = self.remove_duplicates(processed_data)
        
        # 生成统计
        stats = self.generate_statistics(unique_data)
        
        # 保存结果
        self.save_processed_data(unique_data, stats)
        
        # 打印统计信息
        print(f"\n数据处理完成:")
        print(f"总产品数: {stats['total_products']}")
        print(f"保险公司数: {len(stats['companies'])}")
        print(f"产品类型: {list(stats['product_types'].keys())}")
        print(f"平均最低保费: {stats['avg_min_premium']:.2f}")
        print(f"平均预期收益: {stats['avg_expected_return']:.2%}")

if __name__ == "__main__":
    processor = InsuranceDataProcessor()
    processor.run()
