#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
友邦保险(AIA)产品数据爬虫
"""

import re
import json
from datetime import datetime
from typing import List, Optional
from bs4 import BeautifulSoup
from base_scraper import BaseScraper, InsuranceProduct

class AIAScraper(BaseScraper):
    """友邦保险爬虫"""
    
    def __init__(self):
        super().__init__(
            company_name="AIA",
            base_url="https://www.aia.com.hk/zh-hk/our-products.html"
        )
        
    def parse_product_list(self, html: str) -> List[str]:
        """解析友邦保险产品列表页面"""
        soup = BeautifulSoup(html, 'html.parser')
        product_urls = []
        
        # 查找产品链接 - 需要根据实际页面结构调整选择器
        product_links = soup.find_all('a', href=re.compile(r'/products/'))
        
        for link in product_links:
            href = link.get('href')
            if href:
                # 处理相对链接
                if href.startswith('/'):
                    full_url = f"https://www.aia.com.hk{href}"
                else:
                    full_url = href
                    
                if full_url not in product_urls:
                    product_urls.append(full_url)
                    
        return product_urls
        
    def parse_product_detail(self, html: str, url: str) -> Optional[InsuranceProduct]:
        """解析友邦保险产品详情页面"""
        soup = BeautifulSoup(html, 'html.parser')
        
        try:
            # 提取产品名称
            product_name = self.extract_product_name(soup)
            if not product_name:
                return None
                
            # 提取产品类型
            product_type = self.extract_product_type(soup)
            
            # 提取货币
            currency = self.extract_currency(soup)
            
            # 提取保费信息
            min_premium, max_premium = self.extract_premium_range(soup)
            
            # 提取预期收益
            expected_return = self.extract_expected_return(soup)
            
            # 提取缴费期
            payment_period = self.extract_payment_period(soup)
            
            # 提取产品特色
            features = self.extract_features(soup)
            
            # 提取产品描述
            description = self.extract_description(soup)
            
            return InsuranceProduct(
                company_name="友邦保险",
                product_name=product_name,
                product_type=product_type,
                currency=currency,
                min_premium=min_premium,
                max_premium=max_premium,
                expected_return=expected_return,
                payment_period=payment_period,
                features=features,
                description=description,
                url=url,
                last_updated=datetime.now().isoformat()
            )
            
        except Exception as e:
            self.logger.error(f"解析产品详情失败 {url}: {str(e)}")
            return None
            
    def extract_product_name(self, soup: BeautifulSoup) -> Optional[str]:
        """提取产品名称"""
        # 尝试多种可能的选择器
        selectors = [
            'h1.product-title',
            'h1.page-title',
            '.product-header h1',
            'h1',
            '.product-name'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                name = element.get_text(strip=True)
                if name and len(name) > 2:
                    return name
                    
        return None
        
    def extract_product_type(self, soup: BeautifulSoup) -> str:
        """提取产品类型"""
        # 查找包含产品类型的文本
        type_keywords = {
            '储蓄': '储蓄分红险',
            '分红': '储蓄分红险',
            '重疾': '重疾保险',
            '危疾': '重疾保险',
            '医疗': '医疗保险',
            '人寿': '人寿保险',
            '万能': '万能寿险',
            '投连': '投连险'
        }
        
        page_text = soup.get_text()
        for keyword, product_type in type_keywords.items():
            if keyword in page_text:
                return product_type
                
        return "其他保险"
        
    def extract_currency(self, soup: BeautifulSoup) -> str:
        """提取货币类型"""
        page_text = soup.get_text()
        
        if '美元' in page_text or 'USD' in page_text or '$' in page_text:
            return "USD"
        elif '港币' in page_text or 'HKD' in page_text or 'HK$' in page_text:
            return "HKD"
        else:
            return "HKD"  # 默认港币
            
    def extract_premium_range(self, soup: BeautifulSoup) -> tuple:
        """提取保费范围"""
        page_text = soup.get_text()
        
        # 查找保费相关数字
        premium_patterns = [
            r'最低.*?(\d+(?:,\d+)*)',
            r'起.*?(\d+(?:,\d+)*)',
            r'保费.*?(\d+(?:,\d+)*)',
            r'(\d+(?:,\d+)*).*?美元',
            r'(\d+(?:,\d+)*).*?港币'
        ]
        
        min_premium = None
        max_premium = None
        
        for pattern in premium_patterns:
            matches = re.findall(pattern, page_text)
            if matches:
                try:
                    amount = int(matches[0].replace(',', ''))
                    if not min_premium or amount < min_premium:
                        min_premium = amount
                    if not max_premium or amount > max_premium:
                        max_premium = amount
                except ValueError:
                    continue
                    
        return min_premium, max_premium
        
    def extract_expected_return(self, soup: BeautifulSoup) -> Optional[float]:
        """提取预期收益率"""
        page_text = soup.get_text()
        
        # 查找收益率相关文本
        return_patterns = [
            r'(\d+\.?\d*)%.*?年化',
            r'年化.*?(\d+\.?\d*)%',
            r'预期.*?(\d+\.?\d*)%',
            r'回报.*?(\d+\.?\d*)%'
        ]
        
        for pattern in return_patterns:
            matches = re.findall(pattern, page_text)
            if matches:
                try:
                    return float(matches[0])
                except ValueError:
                    continue
                    
        return None
        
    def extract_payment_period(self, soup: BeautifulSoup) -> List[int]:
        """提取缴费期选择"""
        page_text = soup.get_text()
        
        # 查找缴费期相关文本
        period_patterns = [
            r'(\d+)年.*?缴费',
            r'缴费.*?(\d+)年',
            r'(\d+)年期'
        ]
        
        periods = []
        for pattern in period_patterns:
            matches = re.findall(pattern, page_text)
            for match in matches:
                try:
                    period = int(match)
                    if period not in periods and 1 <= period <= 30:
                        periods.append(period)
                except ValueError:
                    continue
                    
        return sorted(periods) if periods else [10]  # 默认10年
        
    def extract_features(self, soup: BeautifulSoup) -> List[str]:
        """提取产品特色"""
        features = []
        
        # 查找特色列表
        feature_selectors = [
            '.product-features li',
            '.benefits li',
            '.highlights li',
            '.key-features li'
        ]
        
        for selector in feature_selectors:
            elements = soup.select(selector)
            for element in elements:
                feature = element.get_text(strip=True)
                if feature and len(feature) > 2:
                    features.append(feature)
                    
        # 如果没有找到特色列表，从文本中提取关键词
        if not features:
            page_text = soup.get_text()
            feature_keywords = [
                '美元资产', '分红保险', '财富传承', '灵活提取',
                '保证利率', '全球保障', '免垫付', '直付服务'
            ]
            
            for keyword in feature_keywords:
                if keyword in page_text:
                    features.append(keyword)
                    
        return features[:5]  # 最多返回5个特色
        
    def extract_description(self, soup: BeautifulSoup) -> str:
        """提取产品描述"""
        # 查找产品描述
        desc_selectors = [
            '.product-description',
            '.product-summary',
            '.overview',
            '.product-intro p'
        ]
        
        for selector in desc_selectors:
            element = soup.select_one(selector)
            if element:
                desc = element.get_text(strip=True)
                if desc and len(desc) > 10:
                    return desc[:500]  # 限制长度
                    
        # 如果没有找到专门的描述，提取页面前几段文字
        paragraphs = soup.find_all('p')
        for p in paragraphs:
            text = p.get_text(strip=True)
            if text and len(text) > 20:
                return text[:300]
                
        return "暂无产品描述"

def main():
    """主函数"""
    scraper = AIAScraper()
    
    print("开始爬取友邦保险产品数据...")
    products = scraper.scrape_products()
    
    if products:
        print(f"成功爬取 {len(products)} 个产品")
        
        # 保存数据
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        scraper.save_to_json(products, f"aia_products_{timestamp}.json")
        scraper.save_to_csv(products, f"aia_products_{timestamp}.csv")
        
        # 打印示例数据
        if products:
            print("\n示例产品数据:")
            product = products[0]
            print(f"产品名称: {product.product_name}")
            print(f"产品类型: {product.product_type}")
            print(f"货币: {product.currency}")
            print(f"最低保费: {product.min_premium}")
            print(f"预期收益: {product.expected_return}")
            print(f"产品特色: {product.features}")
    else:
        print("未能爬取到产品数据")

if __name__ == "__main__":
    main()
