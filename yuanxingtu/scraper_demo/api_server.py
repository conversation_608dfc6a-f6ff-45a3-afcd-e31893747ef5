#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
保险数据API服务
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import json
import os
from datetime import datetime
from pathlib import Path
import logging

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InsuranceDataAPI:
    """保险数据API类"""
    
    def __init__(self):
        self.data_dir = Path("data/processed")
        self.cache = {}
        self.cache_time = None
        self.load_latest_data()
        
    def load_latest_data(self):
        """加载最新数据"""
        try:
            # 查找最新的数据文件
            json_files = list(self.data_dir.glob("insurance_products_*.json"))
            if not json_files:
                logger.warning("未找到数据文件")
                self.cache = {"products": [], "statistics": {}}
                return
                
            latest_file = max(json_files, key=os.path.getctime)
            
            # 加载产品数据
            with open(latest_file, 'r', encoding='utf-8') as f:
                products = json.load(f)
                
            # 查找对应的统计文件
            timestamp = latest_file.stem.split('_')[-2] + '_' + latest_file.stem.split('_')[-1]
            stats_file = self.data_dir / f"statistics_{timestamp}.json"
            
            statistics = {}
            if stats_file.exists():
                with open(stats_file, 'r', encoding='utf-8') as f:
                    statistics = json.load(f)
                    
            self.cache = {
                "products": products,
                "statistics": statistics,
                "last_updated": statistics.get("generated_at", datetime.now().isoformat())
            }
            self.cache_time = datetime.now()
            
            logger.info(f"加载数据成功: {len(products)} 个产品")
            
        except Exception as e:
            logger.error(f"加载数据失败: {str(e)}")
            self.cache = {"products": [], "statistics": {}}
            
    def get_products(self, filters=None):
        """获取产品列表"""
        products = self.cache.get("products", [])
        
        if not filters:
            return products
            
        # 应用筛选条件
        filtered_products = []
        for product in products:
            if self.match_filters(product, filters):
                filtered_products.append(product)
                
        return filtered_products
        
    def match_filters(self, product, filters):
        """检查产品是否匹配筛选条件"""
        # 公司筛选
        if "company" in filters:
            if product.get("company_name") != filters["company"]:
                return False
                
        # 产品类型筛选
        if "product_type" in filters:
            if product.get("product_type") != filters["product_type"]:
                return False
                
        # 货币筛选
        if "currency" in filters:
            if product.get("currency") != filters["currency"]:
                return False
                
        # 保费范围筛选
        if "min_premium_range" in filters:
            min_range, max_range = filters["min_premium_range"]
            product_premium = product.get("min_premium")
            if product_premium is None:
                return False
            if product_premium < min_range or product_premium > max_range:
                return False
                
        # 收益率筛选
        if "min_return" in filters:
            product_return = product.get("expected_return")
            if product_return is None or product_return < filters["min_return"]:
                return False
                
        return True
        
    def get_statistics(self):
        """获取统计信息"""
        return self.cache.get("statistics", {})
        
    def get_companies(self):
        """获取保险公司列表"""
        companies = set()
        for product in self.cache.get("products", []):
            if product.get("company_name"):
                companies.add(product["company_name"])
        return sorted(list(companies))
        
    def get_product_types(self):
        """获取产品类型列表"""
        types = set()
        for product in self.cache.get("products", []):
            if product.get("product_type"):
                types.add(product["product_type"])
        return sorted(list(types))

# 创建API实例
api = InsuranceDataAPI()

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "data_count": len(api.cache.get("products", [])),
        "last_updated": api.cache.get("last_updated")
    })

@app.route('/api/products', methods=['GET'])
def get_products():
    """获取产品列表"""
    try:
        # 获取查询参数
        filters = {}
        
        if request.args.get('company'):
            filters['company'] = request.args.get('company')
            
        if request.args.get('product_type'):
            filters['product_type'] = request.args.get('product_type')
            
        if request.args.get('currency'):
            filters['currency'] = request.args.get('currency')
            
        if request.args.get('min_premium_min') and request.args.get('min_premium_max'):
            try:
                min_val = float(request.args.get('min_premium_min'))
                max_val = float(request.args.get('min_premium_max'))
                filters['min_premium_range'] = [min_val, max_val]
            except ValueError:
                pass
                
        if request.args.get('min_return'):
            try:
                filters['min_return'] = float(request.args.get('min_return'))
            except ValueError:
                pass
                
        # 分页参数
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        
        # 获取筛选后的产品
        products = api.get_products(filters)
        
        # 分页
        total = len(products)
        start = (page - 1) * per_page
        end = start + per_page
        paginated_products = products[start:end]
        
        return jsonify({
            "success": True,
            "data": paginated_products,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "pages": (total + per_page - 1) // per_page
            },
            "filters": filters
        })
        
    except Exception as e:
        logger.error(f"获取产品列表失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/products/<product_id>', methods=['GET'])
def get_product_detail(product_id):
    """获取产品详情"""
    try:
        products = api.cache.get("products", [])
        
        # 查找产品（使用索引作为ID）
        try:
            index = int(product_id)
            if 0 <= index < len(products):
                return jsonify({
                    "success": True,
                    "data": products[index]
                })
        except ValueError:
            pass
            
        return jsonify({
            "success": False,
            "error": "产品未找到"
        }), 404
        
    except Exception as e:
        logger.error(f"获取产品详情失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/statistics', methods=['GET'])
def get_statistics():
    """获取统计信息"""
    try:
        stats = api.get_statistics()
        return jsonify({
            "success": True,
            "data": stats
        })
    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/companies', methods=['GET'])
def get_companies():
    """获取保险公司列表"""
    try:
        companies = api.get_companies()
        return jsonify({
            "success": True,
            "data": companies
        })
    except Exception as e:
        logger.error(f"获取公司列表失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/product-types', methods=['GET'])
def get_product_types():
    """获取产品类型列表"""
    try:
        types = api.get_product_types()
        return jsonify({
            "success": True,
            "data": types
        })
    except Exception as e:
        logger.error(f"获取产品类型失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/refresh', methods=['POST'])
def refresh_data():
    """刷新数据"""
    try:
        api.load_latest_data()
        return jsonify({
            "success": True,
            "message": "数据刷新成功",
            "data_count": len(api.cache.get("products", []))
        })
    except Exception as e:
        logger.error(f"刷新数据失败: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        "success": False,
        "error": "API端点未找到"
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        "success": False,
        "error": "服务器内部错误"
    }), 500

if __name__ == '__main__':
    print("启动保险数据API服务...")
    print("API文档:")
    print("  GET  /api/health - 健康检查")
    print("  GET  /api/products - 获取产品列表")
    print("  GET  /api/products/<id> - 获取产品详情")
    print("  GET  /api/statistics - 获取统计信息")
    print("  GET  /api/companies - 获取公司列表")
    print("  GET  /api/product-types - 获取产品类型")
    print("  POST /api/refresh - 刷新数据")
    print("\n服务地址: http://localhost:5000")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
