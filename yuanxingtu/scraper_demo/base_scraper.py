#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
香港保险数据爬虫基础类
"""

import requests
import time
import random
import json
import logging
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from dataclasses import dataclass
from typing import List, Dict, Optional
import pandas as pd

@dataclass
class InsuranceProduct:
    """保险产品数据结构"""
    company_name: str
    product_name: str
    product_type: str
    currency: str
    min_premium: Optional[float]
    max_premium: Optional[float]
    expected_return: Optional[float]
    payment_period: List[int]
    features: List[str]
    description: str
    url: str
    last_updated: str

class BaseScraper:
    """爬虫基础类"""
    
    def __init__(self, company_name: str, base_url: str):
        self.company_name = company_name
        self.base_url = base_url
        self.session = requests.Session()
        self.setup_session()
        self.setup_logging()
        
    def setup_session(self):
        """设置请求会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-HK,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(headers)
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'logs/{self.company_name}_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(f'{self.company_name}_scraper')
        
    def get_selenium_driver(self, headless=True):
        """获取Selenium驱动"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        
        # 设置用户代理
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
        
        driver = webdriver.Chrome(options=chrome_options)
        return driver
        
    def rate_limit(self, min_delay=1, max_delay=3):
        """请求频率限制"""
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
        
    def fetch_page(self, url: str, use_selenium=False) -> Optional[str]:
        """获取页面内容"""
        try:
            self.rate_limit()
            
            if use_selenium:
                driver = self.get_selenium_driver()
                try:
                    driver.get(url)
                    WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.TAG_NAME, "body"))
                    )
                    content = driver.page_source
                    return content
                finally:
                    driver.quit()
            else:
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                return response.text
                
        except Exception as e:
            self.logger.error(f"获取页面失败 {url}: {str(e)}")
            return None
            
    def parse_product_list(self, html: str) -> List[str]:
        """解析产品列表页面，提取产品详情页链接"""
        raise NotImplementedError("子类必须实现此方法")
        
    def parse_product_detail(self, html: str, url: str) -> Optional[InsuranceProduct]:
        """解析产品详情页面"""
        raise NotImplementedError("子类必须实现此方法")
        
    def scrape_products(self) -> List[InsuranceProduct]:
        """爬取所有产品信息"""
        products = []
        
        # 获取产品列表页面
        list_page_html = self.fetch_page(self.base_url)
        if not list_page_html:
            self.logger.error("无法获取产品列表页面")
            return products
            
        # 提取产品详情页链接
        product_urls = self.parse_product_list(list_page_html)
        self.logger.info(f"找到 {len(product_urls)} 个产品页面")
        
        # 爬取每个产品的详细信息
        for i, url in enumerate(product_urls):
            self.logger.info(f"正在爬取产品 {i+1}/{len(product_urls)}: {url}")
            
            product_html = self.fetch_page(url, use_selenium=True)
            if product_html:
                product = self.parse_product_detail(product_html, url)
                if product:
                    products.append(product)
                    self.logger.info(f"成功爬取产品: {product.product_name}")
                else:
                    self.logger.warning(f"解析产品失败: {url}")
            else:
                self.logger.error(f"获取产品页面失败: {url}")
                
        return products
        
    def save_to_json(self, products: List[InsuranceProduct], filename: str):
        """保存数据到JSON文件"""
        data = []
        for product in products:
            data.append({
                'company_name': product.company_name,
                'product_name': product.product_name,
                'product_type': product.product_type,
                'currency': product.currency,
                'min_premium': product.min_premium,
                'max_premium': product.max_premium,
                'expected_return': product.expected_return,
                'payment_period': product.payment_period,
                'features': product.features,
                'description': product.description,
                'url': product.url,
                'last_updated': product.last_updated
            })
            
        with open(f'data/raw/{filename}', 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
            
        self.logger.info(f"数据已保存到 data/raw/{filename}")
        
    def save_to_csv(self, products: List[InsuranceProduct], filename: str):
        """保存数据到CSV文件"""
        data = []
        for product in products:
            data.append({
                'company_name': product.company_name,
                'product_name': product.product_name,
                'product_type': product.product_type,
                'currency': product.currency,
                'min_premium': product.min_premium,
                'max_premium': product.max_premium,
                'expected_return': product.expected_return,
                'payment_period': ','.join(map(str, product.payment_period)),
                'features': ','.join(product.features),
                'description': product.description,
                'url': product.url,
                'last_updated': product.last_updated
            })
            
        df = pd.DataFrame(data)
        df.to_csv(f'data/raw/{filename}', index=False, encoding='utf-8-sig')
        self.logger.info(f"数据已保存到 data/raw/{filename}")

if __name__ == "__main__":
    # 基础类测试
    scraper = BaseScraper("test", "https://example.com")
    print("基础爬虫类初始化成功")
