#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
香港保险数据爬取主程序
"""

import os
import sys
import json
import time
import schedule
from datetime import datetime
from pathlib import Path
import logging

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from aia_scraper import AIAScraper
from data_processor import InsuranceDataProcessor

class InsuranceDataManager:
    """保险数据管理器"""
    
    def __init__(self, config_file="config/scraper_config.json"):
        self.config_file = config_file
        self.load_config()
        self.setup_directories()
        self.setup_logging()
        
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except FileNotFoundError:
            # 默认配置
            self.config = {
                "scrapers": {
                    "aia": {
                        "enabled": True,
                        "base_url": "https://www.aia.com.hk/zh-hk/our-products.html",
                        "rate_limit": 2
                    },
                    "prudential": {
                        "enabled": False,
                        "base_url": "https://www.prudential.com.hk/tc/our-products",
                        "rate_limit": 2
                    },
                    "manulife": {
                        "enabled": False,
                        "base_url": "https://www.manulife.com.hk/zh-hk/individual.html",
                        "rate_limit": 2
                    }
                },
                "schedule": {
                    "enabled": False,
                    "frequency": "daily",
                    "time": "02:00"
                },
                "data_retention": {
                    "raw_data_days": 30,
                    "processed_data_days": 90
                },
                "output": {
                    "formats": ["json", "csv"],
                    "include_statistics": True
                }
            }
            self.save_config()
            
    def save_config(self):
        """保存配置文件"""
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)
            
    def setup_directories(self):
        """创建必要的目录"""
        directories = [
            "data/raw",
            "data/processed", 
            "logs",
            "config"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            
    def setup_logging(self):
        """设置日志"""
        log_file = f"logs/scraper_{datetime.now().strftime('%Y%m%d')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger('InsuranceDataManager')
        
    def run_scraper(self, scraper_name: str):
        """运行指定的爬虫"""
        scraper_config = self.config["scrapers"].get(scraper_name)
        
        if not scraper_config or not scraper_config.get("enabled"):
            self.logger.info(f"爬虫 {scraper_name} 未启用")
            return False
            
        self.logger.info(f"开始运行 {scraper_name} 爬虫...")
        
        try:
            if scraper_name == "aia":
                scraper = AIAScraper()
            else:
                self.logger.error(f"未知的爬虫类型: {scraper_name}")
                return False
                
            # 爬取数据
            products = scraper.scrape_products()
            
            if products:
                # 保存数据
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                scraper.save_to_json(products, f"{scraper_name}_products_{timestamp}.json")
                
                if "csv" in self.config["output"]["formats"]:
                    scraper.save_to_csv(products, f"{scraper_name}_products_{timestamp}.csv")
                    
                self.logger.info(f"{scraper_name} 爬虫完成，获取 {len(products)} 个产品")
                return True
            else:
                self.logger.warning(f"{scraper_name} 爬虫未获取到数据")
                return False
                
        except Exception as e:
            self.logger.error(f"运行 {scraper_name} 爬虫失败: {str(e)}")
            return False
            
    def run_all_scrapers(self):
        """运行所有启用的爬虫"""
        self.logger.info("开始运行所有爬虫...")
        
        success_count = 0
        total_count = 0
        
        for scraper_name, config in self.config["scrapers"].items():
            if config.get("enabled"):
                total_count += 1
                if self.run_scraper(scraper_name):
                    success_count += 1
                    
                # 爬虫间延迟
                time.sleep(config.get("rate_limit", 2))
                
        self.logger.info(f"爬虫运行完成: {success_count}/{total_count} 成功")
        return success_count > 0
        
    def process_data(self):
        """处理数据"""
        self.logger.info("开始数据处理...")
        
        try:
            processor = InsuranceDataProcessor()
            processor.run()
            self.logger.info("数据处理完成")
            return True
        except Exception as e:
            self.logger.error(f"数据处理失败: {str(e)}")
            return False
            
    def cleanup_old_data(self):
        """清理旧数据"""
        self.logger.info("开始清理旧数据...")
        
        try:
            from datetime import timedelta
            
            now = datetime.now()
            raw_cutoff = now - timedelta(days=self.config["data_retention"]["raw_data_days"])
            processed_cutoff = now - timedelta(days=self.config["data_retention"]["processed_data_days"])
            
            # 清理原始数据
            raw_dir = Path("data/raw")
            for file_path in raw_dir.glob("*"):
                if file_path.stat().st_mtime < raw_cutoff.timestamp():
                    file_path.unlink()
                    self.logger.info(f"删除旧文件: {file_path}")
                    
            # 清理处理后数据
            processed_dir = Path("data/processed")
            for file_path in processed_dir.glob("*"):
                if file_path.stat().st_mtime < processed_cutoff.timestamp():
                    file_path.unlink()
                    self.logger.info(f"删除旧文件: {file_path}")
                    
        except Exception as e:
            self.logger.error(f"清理数据失败: {str(e)}")
            
    def run_full_pipeline(self):
        """运行完整的数据管道"""
        self.logger.info("=" * 50)
        self.logger.info("开始运行完整数据管道")
        self.logger.info("=" * 50)
        
        # 1. 运行爬虫
        scraper_success = self.run_all_scrapers()
        
        # 2. 处理数据
        if scraper_success:
            process_success = self.process_data()
        else:
            self.logger.warning("跳过数据处理，因为爬虫未成功")
            process_success = False
            
        # 3. 清理旧数据
        self.cleanup_old_data()
        
        self.logger.info("=" * 50)
        self.logger.info("数据管道运行完成")
        self.logger.info("=" * 50)
        
        return scraper_success and process_success
        
    def setup_schedule(self):
        """设置定时任务"""
        if not self.config["schedule"]["enabled"]:
            return
            
        frequency = self.config["schedule"]["frequency"]
        time_str = self.config["schedule"]["time"]
        
        if frequency == "daily":
            schedule.every().day.at(time_str).do(self.run_full_pipeline)
        elif frequency == "weekly":
            schedule.every().week.at(time_str).do(self.run_full_pipeline)
            
        self.logger.info(f"已设置定时任务: {frequency} at {time_str}")
        
    def run_scheduler(self):
        """运行调度器"""
        self.setup_schedule()
        
        self.logger.info("调度器已启动，等待定时任务...")
        
        while True:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="香港保险数据爬取工具")
    parser.add_argument("--mode", choices=["once", "schedule"], default="once",
                       help="运行模式: once(单次运行) 或 schedule(定时运行)")
    parser.add_argument("--scraper", choices=["aia", "all"], default="all",
                       help="指定爬虫: aia 或 all")
    parser.add_argument("--process-only", action="store_true",
                       help="仅处理现有数据，不运行爬虫")
    
    args = parser.parse_args()
    
    # 创建数据管理器
    manager = InsuranceDataManager()
    
    if args.process_only:
        # 仅处理数据
        manager.process_data()
    elif args.mode == "once":
        # 单次运行
        if args.scraper == "all":
            manager.run_full_pipeline()
        else:
            manager.run_scraper(args.scraper)
            manager.process_data()
    elif args.mode == "schedule":
        # 定时运行
        manager.run_scheduler()

if __name__ == "__main__":
    main()
